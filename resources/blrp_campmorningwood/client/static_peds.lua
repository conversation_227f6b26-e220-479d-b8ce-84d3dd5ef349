exports.blrp_core:CreateStaticPed({
  id = 'CM25CAM',
  model = `a_m_y_juggalo_01`,
  coords = vector4(-1265.821, -1538.835, 4.806, 80.015),
  dict = 'timetable@ron@ig_5_p3',
  clip = 'ig_5_p3_base',
  outfit = {
    [0] = {1, 2},
    [1] = {0, 0},
    [2] = {0, 0},
    [3] = {0, 1},
    [4] = {0, 2},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25CREED',
  model = `a_m_y_hippy_01`,
  coords = vector4(-1350.207, 2659.398, 3.856, 5.144),
  dict = 'timetable@ron@ron_ig_2_alt1',
  clip = 'ig_2_alt1_base',
  outfit = {
    [0] = {0, 0},
    [2] = {2, 0},
    [1] = {0, 0},
    [3] = {1, 0},
    [4] = {1, 0},
    [5] = {0, 0},
    [6] = {1, 0},
    [7] = {0, 0},
    [8] = {1, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25KEVIN',
  model = `cs_priest`,
  coords = vector4(-309.280, 6145.796, 32.384, 44.167),
  dict = 'tigerle@custom@jobs@handsonback',
  clip = 'tigerle_custom_handsonback',
  outfit = {
    [0] = {0, 0},
    [1] = {0, 0},
    [2] = {0, 0},
    [3] = {0, 0},
    [4] = {0, 0},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25PAM',
  model = `a_f_y_tourist_01`,
  coords = vector4(-3024.384, 2219.862, 19.351, 27.522),
  dict = 'misshair_shop@hair_dressers',
  clip = 'keeper_base',
  outfit = {
    [0] = {0, 1},
    [1] = {0, 0},
    [2] = {0, 0},
    [3] = {1, 0},
    [4] = {0, 1},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {0, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25DWIGHT',
  model = `A_M_O_ACult_02`,
  coords = vector4(2537.411, 1719.735, 19.157, 116.780),
  dict = 'anim@amb@casino@hangout@ped_male@stand@02b@idles',
  clip = 'idle_a',
  outfit = {
    [0] = {0, 0},
    [1] = {0, 0},
    [2] = {1, 1},
    [3] = {1, 2},
    [4] = {0, 1},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {0, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25TOBY',
  model = `s_m_m_scientist_01`,
  coords = vector4(-895.286, 2836.355, 23.581, 240.084),
  dict = 'anim@mp_corona_idles@male_d@idle_a',
  clip = 'idle_a',
  outfit = {
    [0] = {2, 1},
    [1] = {0, 0},
    [2] = {0, 0},
    [3] = {0, 0},
    [4] = {0, 1},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {1, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 2},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25ALF',
  model = `a_c_husky`,
  coords = vector4(-1351.858, 2659.299, 3.856, 342.717),
  dict = 'creatures@rottweiler@amb@world_dog_sitting@idle_a',
  clip = 'idle_b',
  outfit = {
    [0] = {0, 2},
  },
  quest = true,
})
exports.blrp_core:CreateStaticPed({
  id = 'CM25HOLLY',
  model = `mp_f_freemode_01`,
  coords = vector4(-495.167, 2996.473, 27.510, 200.596),
  dict = 'anim@mp_corona_idles@male_d@idle_a',
  clip = 'idle_a',
  outfit = {
    [0] = {0, 0},
    [1] = {0, 0},
    [2] = {264, 0},
    [3] = {15, 0},
    [4] = {253, 6},
    [5] = {175, 5},
    [6] = {190, 16},
    [7] = {0, 0},
    [8] = {444, 13},
    [9] = {0, 0},
    [10] = {276, 1},
    [11] = {736, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {65, 2},
    ['p2'] = {28, 3},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
  skin = json.decode('{"bodyb_4":100,"complexion_2":100,"eyebrows_2":100,"bodyb_1":6,"decals_2":0,"blush_1":1,"lipstick_1":1,"nose_4":55,"blemishes_2":100,"mask_2":0,"glasses_2":0,"blemishes_1":255,"bodyb_3":255,"bags_1":0,"beard_2":100,"tshirt_2":0,"helmet_2":0,"age_2":100,"moles_1":8,"jaw_1":-47,"makeup_1":3,"makeup_3":255,"righthand_1":-1,"sun_1":255,"beard_4":0,"pants_2":0,"cheeks_2":0,"decals_1":0,"makeup_2":53,"hair_color_2":7,"blush_3":11,"lefthand_2":0,"makeup_type":1,"shoes_2":0,"skin_md_weight":66,"face_md_weight":34,"righthand_2":0,"bproof_2":0,"ears_2":0,"neckarm_1":0,"glasses_1":-1,"neck_thickness":0,"eye_color":5,"age_1":255,"chin_2":0,"mom":25,"arms":15,"beard_1":255,"nose_3":-71,"jaw_2":-26,"eye_squint":-46,"complexion_1":255,"bodyb_2":95,"makeup_4":255,"lip_thickness":-60,"bags_2":0,"pants_1":61,"eyebrows_3":9,"lipstick_4":0,"chest_1":255,"eyebrows_6":0,"hair_1":127,"bproof_1":0,"nose_6":0,"eyebrows_1":17,"eyebrows_5":-46,"helmet_1":-1,"torso_2":0,"chin_3":-14,"cheeks_1":0,"hair_2":0,"lipstick_2":45,"ears_1":-1,"chin_4":0,"chest_3":0,"dad":20,"beard_3":0,"mask_1":0,"chest_4":0,"neckarm_2":0,"cheeks_3":-73,"arms_2":0,"chin_1":0,"sun_2":100,"lefthand_1":-1,"shoes_1":34,"nose_1":-45,"nose_2":4,"nose_5":-13,"hair_color_1":58,"lipstick_3":16,"eyebrows_4":0,"sex":0,"tshirt_1":15,"moles_2":95,"blush_2":76,"chest_2":100,"torso_1":15}'),
  quest = true,
})