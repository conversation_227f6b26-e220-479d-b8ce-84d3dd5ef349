<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Submarine Radar</title>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron&display=swap" rel="stylesheet" />
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100vh;
      width: 100vw;
      background: transparent;
      font-family: 'Orbitron', sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      visibility: hidden;
    }

    #radar-wrapper {
      position: relative;
      width: 800px;
      height: 500px;
      background: #0a1121;
      border: 8px solid #5d738c;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
    }

    #radar-canvas {
      width: 100%;
      height: 100%;
      display: block;
    }

    #radar-text {
      position: absolute;
      bottom: 20px;
      width: 100%;
      text-align: center;
      font-size: 16px;
      color: #00bfff;
      text-shadow: 0 0 2px #00bfff;
    }
  </style>
</head>
<body>
  <div id="radar-wrapper" onclick="showMessage()">
    <canvas id="radar-canvas"></canvas>
    <div id="radar-text"></div>
  </div>

  <script>
    const canvas = document.getElementById('radar-canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    let angle = 0;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 10;

    const blips = [
      { x: centerX + -140, y: centerY - -30, pulse: 0 }
    ];

    function drawRadar() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const bgGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
      bgGradient.addColorStop(0, 'rgba(0, 191, 255, 0.06)');
      bgGradient.addColorStop(1, 'rgba(0, 191, 255, 0.01)');
      ctx.fillStyle = bgGradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.strokeStyle = 'rgba(0, 191, 255, 0.2)';
      for (let i = 1; i <= 4; i++) {
        ctx.beginPath();
        ctx.arc(centerX, centerY, (radius / 4) * i, 0, 2 * Math.PI);
        ctx.stroke();
      }

      const sweepLength = 45 * Math.PI / 180;
      const extendedRadius = Math.max(canvas.width, canvas.height) * 1.2;
      const grad = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, extendedRadius);
      grad.addColorStop(0.0, 'rgba(0, 191, 255, 0.25)');
      grad.addColorStop(1.0, 'rgba(0, 191, 255, 0.0)');
      ctx.fillStyle = grad;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, extendedRadius, angle, angle + sweepLength);
      ctx.closePath();
      ctx.fill();

      // Blips (pulsing)
      blips.forEach(blip => {
        const opacity = 0.5 + 0.5 * Math.sin(blip.pulse);
        ctx.fillStyle = `rgba(0, 191, 255, ${opacity.toFixed(2)})`;
        ctx.beginPath();
        ctx.arc(blip.x, blip.y, 5, 0, 2 * Math.PI);
        ctx.fill();
        blip.pulse += 0.1;
      });

      ctx.strokeStyle = 'rgba(0, 191, 255, 0.03)';
      ctx.lineWidth = 1;
      const lineSpacing = 6;
      for (let y = 0; y < canvas.height; y += lineSpacing) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }

      angle += 0.01;
      if (angle >= 2 * Math.PI) angle = 0;

      requestAnimationFrame(drawRadar);
    }

    function showMessage() {
      const text = "Unidentified object at latitude 2336 and longitude -3198";
      const textElement = document.getElementById("radar-text");
      textElement.innerHTML = "";
      let i = 0;

      const interval = setInterval(() => {
        textElement.innerHTML += text[i];
        i++;
        if (i >= text.length) clearInterval(interval);
      }, 35);
    }

    window.addEventListener("message", function (event) {
      if (event.data.action === "openRadar") {
        document.body.style.visibility = "visible";
      }
      if (event.data.action === "closeRadar") {
        document.body.style.visibility = "hidden";
      }
    });

    document.addEventListener("keydown", function (event) {
      if (event.key === "Escape") {
        fetch(`https://${GetParentResourceName()}/close`, {
          method: "POST",
          headers: { "Content-Type": "application/json; charset=UTF-8" },
          body: JSON.stringify({})
        });
      }
    });

    window.onload = () => {
      drawRadar();
    };
  </script>
</body>
</html>