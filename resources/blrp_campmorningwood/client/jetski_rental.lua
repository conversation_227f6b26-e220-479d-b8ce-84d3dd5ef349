function DisplayHelpText(str)
  SetTextComponentFormat("STRING")
  AddTextComponentString(str)
  DisplayHelpTextFromStringLabel(0, 0, 0, -1)
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1)

    local player_coords = GetEntityCoords(PlayerPedId())

    for _, jetski_location in pairs({
      vector3(-552.933, 2931.775, 14.024),
    }) do
      local distance = #(player_coords - jetski_location)

      if distance < 100 then
        DrawMarker(35, jetski_location.x, jetski_location.y, jetski_location.z, 0, 0, 0, 0, 0, 0, 1.0, 1.0, 1.0, 64, 64, 64, 180, 0, true, 0, 0)

        if distance < 5 then
          if not IsPedInAnyVehicle(PlayerPedId(), false) and not exports.blrp_core:me().isInComa() and not exports.blrp_core:me().isHandcuffed() then
            DisplayHelpText("Press ~INPUT_CONTEXT~ to rent a jetski for $2000")

            if IsControlJustReleased(1, 51) then
              TriggerServerEvent('blrp_campmorningwood:server:requestJetski')
            end
          elseif IsPedInAnyVehicle(PlayerPedId(), false) then
            local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

            if vehicle and vehicle > 0 and GetEntityModel(vehicle) == `seashark` then
              DisplayHelpText("Press ~INPUT_CONTEXT~ to return this vehicle")

              DisableControlAction(0, 86, true) -- Disable horn, deconflicts 'E' being pressed

              if IsControlJustReleased(1, 51) then
                TriggerServerEvent('blrp_campmorningwood:server:returnJetski')
              end
            end
          end
        end
      end
    end
  end
end)