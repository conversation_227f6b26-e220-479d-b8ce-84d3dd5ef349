tQuests = T.getInstance('blrp_quests', 'main')

local running_dialogue = nil
local running_dialogue_index = 0
local running_dialogue_promise = nil
local running_dialogue_mugshots = {}

tQuests.runDialogue = function(dialogue)
  running_dialogue = dialogue
  running_dialogue_index = 1
  running_dialogue_mugshots = {}

  for i = 1, 30 do
    UnregisterPedheadshot(i)
  end

  for _, line in pairs(dialogue) do
    local ped_left, ped_right, text = table.unpack(line)

    if ped_left and not running_dialogue_mugshots[ped_left] then
      running_dialogue_mugshots[ped_left] = getMugshotForPed(ped_left)
    end

    if ped_right and not running_dialogue_mugshots[ped_right] then
      running_dialogue_mugshots[ped_right] = getMugshotForPed(ped_right)
    end
  end

  if running_dialogue_promise then
    running_dialogue_promise:resolve(false)
  end

  running_dialogue_promise = promise:new()

  Citizen.Wait(100)

  SendNUIMessage({
    action = 'setQuestInfo',
    img_dicts = running_dialogue_mugshots,
    quest_text = running_dialogue[running_dialogue_index]
  })

  SetNuiFocus(true, false)

  return Citizen.Await(running_dialogue_promise)
end

RegisterNUICallback('close', function(_, cb)
  SetNuiFocus(false, false)
  SendNUIMessage({ action = "closeRadar" })
  cb('ok')
end)

RegisterNetEvent('submarine:radar:openUI', function()
  SetNuiFocus(true, true)
  SendNUIMessage({ action = "openRadar" })
end)