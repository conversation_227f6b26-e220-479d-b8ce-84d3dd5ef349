local jetski_cooldowns = {}
local jetski_count = 0

RegisterNetEvent('blrp_campmorningwood:server:requestJetski', function()
  local character = exports.blrp_core:character(source)
  local c_id = tonumber(character.get('id'))

  if jetski_cooldowns[c_id] and jetski_cooldowns[c_id] > os.time() then
    character.notify('You rented a Jetski recently. Wait a while')
    return
  end

  local character_coords = character.getCoordinates()

  if #(character_coords - vector3(-552.933, 2931.775, 14.024)) > 50.0 then
    return
  end

  if
    not character.request('Pay $2000 (non-refundable) to rent a jetski?') or
    not character.tryPayment(2000, true, false, true, false)
  then
    return
  end

  jetski_cooldowns[c_id] = os.time() + (30 * 60)
  exports.blrp_core:AnticheatSetVehicleLockoutTrueForSourceTimed(character.source, 5)

  local vehicle_id = 'morningwoodp' .. os.date('%y%m%d') .. jetski_count

  jetski_count = jetski_count + 1

  local function generateJetskiPlate()
    local letters = 'ABCDEFGHJKLMNPRSTUVWXYZ'
    local numbers = '1234567890'

    local function rnd(keyspace, iterations)
      local length = string.len(keyspace)

      local output = ''

      for i = 1, iterations do
        local position = math.random(1, length)
        output = output .. string.sub(keyspace, position, position)
      end

      return output
    end

    return rnd(letters, 2) .. rnd(numbers, 3) .. rnd(letters, 3)
  end

  local plate = generateJetskiPlate()

  local network_id = exports.blrp_vehicles:SpawnGarageVehicle{
    player = character.source,
    vehicle_data = {
      server_uid = vehicle_id,
      vehicle = 'seashark',
      registration = plate,
    },
    is_admin = true,
  }

  character.client('blrp_vehicles:client:applyCustomisation', network_id, 'admin', {
    registration = plate,
    windows = 0,
    colour = 0,
    scolour = 0,
    ecolor = 0,
    ecolorextra = 0,
    platetype = 1,
    neon = 0,
    engineDamage = 1000,
    bodyDamage = 1000,
    fuelDamage = 1000,
  })

  tFuel.setFuelLevelPercent(character.source, { network_id, 100 })

  character.log('ACTION', 'Paid $2000 to rent Jetski at Camp Morningwood')
end)

RegisterNetEvent('blrp_campmorningwood:server:returnJetski', function()
  local character = exports.blrp_core:character(source)
  local c_id = tonumber(character.get('id'))

  local vehicle = GetVehiclePedIsIn(GetPlayerPed(character.source), false)

  if not vehicle or vehicle <= 0 or GetEntityModel(vehicle) ~= `seashark` then
    character.notify('You have no vehicle to return')
    return
  end

  local state = Entity(vehicle).state

  if state.owner_char_id ~= c_id then
    character.notify('You can only return vehicles you have rented yourself')
    return
  end

  if not character.request('Return this vehicle?') then
    return
  end

  character.notify('Thank you for returning your vehicle!')
  DeleteEntity(vehicle)
  jetski_cooldowns[c_id] = 0
end)