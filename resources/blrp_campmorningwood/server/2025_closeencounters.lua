local QUEST_ID = 'cm25quest:CloseEncounters'
local stage_key = QUEST_ID .. ':stage'
local s3_time_lock_key = QUEST_ID .. ':s3time'

local dialogue = {
  [1] = {
    running = {
      { false, 'CM25CREED', "*The man starts rapidly closing tabs on his computer* Wha- get the HELL outta my house!" },
      { 'SELF', false, "I’d hardly call this busted shack a house." },
      { false, 'CM25CREED', "Are you one of those IAA stooges again? I already told you once, I never kept any of those pictures!" },
      { 'SELF', false, "Do I look like a Fed to you? Wait, what pictures?" },
      { false, 'CM25CREED', "Nevermind that" },
      { false, 'CM25CREED', "*He starts swinging at flys with a ping-pong paddle* I doubt you can tell me anything I don’t already know, so get the fuck out. Unless you know more about the Pyramids. Or aliens. Or both." },
      { 'SELF', false, "Or both? I believe in the pyramids, but aliens…?" },
      { false, 'CM25CREED', "If you can believe in pyramids, you can believe in aliens." },
      { false, 'CM25CREED', "*<PERSON> extends his hand out* I’m <PERSON> by the way." },
      { 'SELF', false, "*His handshake is firm, and he pulls you around the side of his desk to look at something on his computer monitor. His finger jabs against the monitor. It’s a picture of a didgeridoo.*" },
      { false, 'CM25CREED', "Do you know anything about this?!" },
      { 'SELF', false, "I think that’s a didgeridoo." },
      { false, 'CM25CREED', "Oh. Another dead end…." },
      { false, 'CM25CREED', "Are you free to help me check into a couple other leads?" },
      { 'SELF', false, {
        "Sure, not like I have anything better to do…",
        "No thanks, check on your own leads."
      } },
    },
    responses = {
      [1] = {
        { false, 'CM25CREED', "That’s the spirit! There’s two people I want you to catch up with. One is some clown, he’s always busking on live stream from his spot down south at the beach. Said he saw something weird the other night while he was freestylin’. The other is my old pal Rev, he’s usually bumming around in his robe over there in Paleto." },
        { false, 'CM25CREED', "*Creed leans close and whispers* If they saw what I think they saw, we need to get it out of them before the Feds do." },
      },
      [2] = {
        { false, 'CM25CREED', "Are you free to help me check into a couple other leads?" },
        { 'SELF', false, {
          "Sure, not like I have anything better to do…",
          "No thanks, check on your own leads."
        } },
      },
    },
  },

  [2] = {
    cam_before_creed = {
      { false, 'CM25CAM', "Woop! Woop! You’re harshin my style homie, step off!" },
    },
    cam_after_creed_start = {
      { false, 'CM25CAM', "Woop! Woop! you lookin’ to buy some weed killa?" },
      { 'SELF', false, "Hey not today man… You know some old guy that lives in a shitty shack off of the Zancudo trail?" },
      { false, 'CM25CAM', "Oh Creed? What’s up with that old wacko? What’s he trippin’ on?" },
      { 'SELF', false, "He said you saw something maybe a little out of the ordinary" },
      { false, 'CM25CAM', "Yo, shhh! I seen somethin' wild, for real! But I’m gonna need a little sum first y’know? A dealer ain’t puffin' his own stash." },
    },
    cam_needs_joint = {
      { 'SELF', false, "Sorry, I don’t have anything right now." },
      { false, 'CM25CAM', "Aight, Whatever…Come back when you got somethin for me to light." },
    },
    cam_joint_given = {
      { 'SELF', false, "Yeah, I got something for you." },
      { false, 'CM25CAM', "*Cam lights up the joint and takes a big rip* Ight, so listen up, there was these shinin' lights up in the sky, zippin' 'round like crazy in all kinds of ways, it looked like they was going north ya feel me?" },
      { 'SELF', false, "How fucking high were you?" },
      { false, 'CM25CAM', "Nah beeyatch, I was straight! I seen it, if you ain't buyin' that, it's all good, but no need to be a clowner downer…" },
      { 'SELF', false, "Fine, I’ll bite. Where did you see these lights?" },
      { false, 'CM25CAM', "Shit killa, I don’t really remember. Me and the homies was havin’ a dope ass time on the beach by the lifeguard towers, it was sick!" },
      { 'SELF', false, "Well I appreciate the info." },
      { false, 'CM25CAM', "No problem killa!" },
    },
    cam_repeat = {
      { false, 'CM25CAM', "Woop! Woop! You find those lights you were looking for bro? They were heading north along the beach, I swear playa!" },
    },

    kevin_before_creed = {
      { false, 'CM25KEVIN', "Risus abundat in ore stultorum." },
    },
    kevin_after_creed = {
      { false, 'CM25KEVIN', "Nitimur in vetitum." },
      { 'SELF', false, "What?" },
      { false, 'CM25KEVIN', "Nothing, just something to ward off the tourists. What can I help you with my child?" },
      { 'SELF', false, "You know some guy named Creed?" },
      { false, 'CM25KEVIN', "Why yes! I spoke to him just the other day about some strange things I have seen up this way." },
      { 'SELF', false, "Yeah, he wanted me to find out exactly what you saw." },
      { false, 'CM25KEVIN', "Well, I was in the confessional the other night, waiting for a member of my congregation to show up, then the whole church started shaking violently! I took a step outside and there were lights flashing everywhere! It looked like they were heading west along the coast." },
      { 'SELF', false, "West huh? Thanks for the info bro." },
      { false, 'CM25KEVIN', "It’s… Father, and you’re welcome." },
    },
    kevin_repeat = {
      { false, 'CM25KEVIN', "Hello again, my child. Did you manage to investigate those lights I saw heading west along the coast?" },
    },
  },

  [3] = {
    remind = {
      { false, 'CM25CREED', "You find both of those guys yet?" },
      { 'SELF', false, "Not yet." },
      { false, 'CM25CREED', "C’mon man. A clown busking on a beach down south, and a man wearing a robe in Paleto, it can’t be that hard. Find out what they know and report back." },
    },
    both_found = {
      { false, 'CM25CREED', "You found both of them?" },
      { 'SELF', false, "*You explain what the two witnesses saw, to Creed*" },
      { false, 'CM25CREED', "Outstanding findings! Based on the direction the lights were headed, I’m pretty sure there’s activity on the west coast. In that general area, anyways." },
      { 'SELF', false, "The west coast of the entire island?" },
      { false, 'CM25CREED', "Precisely. Scour the beaches, see if there’s anything out of the ordinary! Come back around this time tomorrow and let me know what you’ve found." },
    },
    too_early_return = {
      { false, 'CM25CREED', "Creed waves you away, eyes glued to the computer monitor as he types away furiously." },
    },
    pam_found = {
      { false, 'CM25PAM', "Oh my God, you scared me so bad just now!" },
      { 'SELF', false, "Sorry, I figured you’d hear me coming." },
      { false, 'CM25PAM', "I’m too focused on the water! Fleet Week is soon. I need to be ready!" },
      { 'SELF', false, "Have you seen anything noteworthy?" },
      { false, 'CM25PAM', "No, just the same cargo ship full of uggos passing back and forth. A couple nice sunsets, too. Also a dead sea lion but that wasn’t noteworthy, really… it was more like… gross." },
      { 'SELF', false, "Did you get any pictures of that cargo ship?" },
      { false, 'CM25PAM', "Did I get any pictures of that cargo ship? As if I’d show you my work for FREE! Fuck off, random. I’m gonna miss the seamen." },
    },
    pam_repeat = {
      { false, 'CM25PAM', "I said leave me alone! I don’t care about your stupid cargo ship!" },
    }
  },
  [4] = {
    creed_no_findings = {
      { false, 'CM25CREED', "Did you find anything out of the ordinary on the west coast yet?" },
      { 'SELF', false, "I’m not sure if I’ve found anything you’d be interested in yet…" },
      { false, 'CM25CREED', "We need something major! UNDENIABLE EVIDENCE!!! Find me something good!" },
    },
    creed_with_goo = {
      { false, 'CM25CREED', "Did you find anything out of the ordinary on the west coast yet?" },
      { 'SELF', false, "Yeah, I found this. *You show Creed a vial of the green goo you collected*" },
      { false, 'CM25CREED', "Holy SHIT! Quick, put that back away, before someone sees!" },
      { false, 'CM25CREED', "Here, I have something for you." },
      { false, 'CM25CREED', "*Creed roots around in his drawers, and reveals a strange device.*" },
      { 'item:cm25_xtool', false, "*hands you an underwater hydraulic cutter*" },
      { 'SELF', false, "What do I do with this?" },
      { false, 'CM25CREED', "What you showed me tells me there’s mulderite nearby. Do you think you could extract some? I’ve never seen it in person…" },
      { 'SELF', false, "Mulderite? What is that?" },
      { false, 'CM25CREED', "What? You haven’t heard about mulderite? I hear it’s quite rare and might have some… Unique properties. I think the feds are trying to get their hands on it though, so be quick." },
      { 'SELF', false, "I’ll do my best to get you some." },
    },
    creed_no_metal = {
      { false, 'CM25CREED', "Did you find any of that mulderite yet?" },
      { 'SELF', false, "Not yet." },
      { false, 'CM25CREED', "We got to act quick on this! The feds will be all over it in no time!" },
    },
    creed_with_metal = {
      { 'SELF', false, "Hey, Creed, look what I got!" },
      { false, 'CM25CREED', "Quiet down! Let’s see, let’s see." },
      { 'SELF', false, "*You hand Creed the sample of mulderite, and he grabs it away from you quickly.* So… what do you do with it?" },
      { false, 'CM25CREED', "Oh. Uh… I might need some help with that. I have an old contact, a military scientist. Dr. Dwight. He’s a bit odd, though. Are you okay with that?" },
      { 'SELF', false, "…sure. Where does he live?" },
      { false, 'CM25CREED', "I’m not totally sure. We used to meet near the Palmer-Taylor Power Station, something about the windmills jamming signals… said to let myself in if I ever needed a place to crash…" },
      { 'SELF', false, "Do you have Dr. Dwight’s address, or…?" },
      { false, 'CM25CREED', "It was somewhere around the power station, I don’t know! The garage code was 5697… or was it 9765? You’ll figure it out! Take that sample of mulderite to Dwight. I need to get back to my own research." },
    },
    creed_code_remind = {
      { false, 'CM25CREED', "Hey I told you, Dwight will help you from here." },
      { false, 'CM25CREED', "I said the code is 7596! Something like that!" }, -- 6759 actual code
    },
  },
  [5] = {
    dwight_early = {
      { false, 'CM25DWIGHT', "Who the hell are you, get out before I call the cops!" },
    },
    door_knock_error = {
      { false, 'CM25DWIGHT', "Wait, you’re not my BadEats driver! Who are you?!" },
      { 'SELF', false, "Uh, Creed sent me?" },
      { false, 'CM25DWIGHT', "Creed thought it wise to send you my way? You must have found something interesting." },
      { 'SELF', false, "He told me to come and show you something I found at this crash site in the ocean! Do you wanna see it?" },
      { false, 'CM25DWIGHT', "Of course, of course." },
      { 'SELF', false, "*You realise you don’t have the mulderite on you*" },
      { 'SELF', false, "I uhh… I forgot to bring it…" },
      { false, 'CM25DWIGHT', "Well I think you should go and fetch it then, and hurry!" },
    },
    door_knock_false = {
      { false, 'CM25DWIGHT', "Well… Do you have it??" },
      { 'SELF', false, "Not yet…" },
      { false, 'CM25DWIGHT', "Well hurry up then!" },
    },
    door_knock_proceed = {
      { false, 'CM25DWIGHT', "Well… Do you have it??" },
      { 'SELF', false, "Yeah I have it right here." },
      { 'item:cm25_xmetal', false, "*You hand Dwight a sample of the Mysterious Metal*" },
      { false, 'CM25DWIGHT', "It’s certainly a curious little rock, but it’s too soon to tell if it’s abnormal… it does seem to be humming a bit, does it not?" },
      { 'SELF', false, "I hadn’t really noticed. Do you think you can tell me what it’s for?" },
      { false, 'CM25DWIGHT', "It will take me some time to cross compare samples and run it through my multimeter…" },
      { false, 'CM25DWIGHT', "I’ll need to check the isotopic renaissance chamber…" },
      { false, 'CM25DWIGHT', "And also check for x-ray fluorescence…" },
      { false, 'CM25DWIGHT', "Do you happen to know anything about atomic absorption spectroscopy?" },
      { 'SELF', false, "Uhh… No?" },
      { false, 'CM25DWIGHT', "I could really use an assistant." },
      { 'SELF', false, "Well I could light your bunsen burner or something?!" },
      { false, 'CM25DWIGHT', "That won’t do… See, BadEats is always cancelling my grocery order, saying they can’t find my front door. Here’s a list. Go make yourself useful, now." },
    },
    door_knock = {
      { false, 'CM25DWIGHT', "Wait, you’re not my BadEats driver! Who are you?!" },
      { 'SELF', false, "Uh, Creed sent me?" },
      { false, 'CM25DWIGHT', "Creed thought it wise to send you my way? You must have found something interesting." },
      { 'SELF', false, "He told me to come and show you something I found at this crash site in the ocean! Do you wanna see it?" },
      { false, 'CM25DWIGHT', "Of course, of course." },
      { 'item:cm25_xmetal', false, "*You hand Dwight a sample of the Mysterious Metal*" },
      { false, 'CM25DWIGHT', "It’s certainly a curious little rock, but it’s too soon to tell if it’s abnormal… it does seem to be humming a bit, does it not?" },
      { 'SELF', false, "I hadn’t really noticed. Do you think you can tell me what it’s for?" },
      { false, 'CM25DWIGHT', "It will take me some time to cross compare samples and run it through my multimeter…" },
      { false, 'CM25DWIGHT', "I’ll need to check the isotopic renaissance chamber…" },
      { false, 'CM25DWIGHT', "And also check for x-ray fluorescence…" },
      { false, 'CM25DWIGHT', "Do you happen to know anything about atomic absorption spectroscopy?" },
      { 'SELF', false, "Uhh… No?" },
      { false, 'CM25DWIGHT', "I could really use an assistant." },
      { 'SELF', false, "Well I could light your bunsen burner or something?!" },
      { false, 'CM25DWIGHT', "That won’t do… See, BadEats is always cancelling my grocery order, saying they can’t find my front door. Here’s a list. Go make yourself useful, now." },
    },
    needs_items = {
      { false, 'CM25DWIGHT', "I need everything on the list, double check you’ve got all of it. I’ll keep working in the meantime." },
    },
    has_items = {
      { false, 'CM25DWIGHT', "Oh, perfect! Thank you for fetching everything. I’m excited to show you my findings, too." },
      { false, 'CM25DWIGHT', "This chowder is cold though… Here, you keep it." },
      { 'SELF', false, "Uhh… Ok. What did you learn about that rock or whatever it was I gave you anyway?" },
      { false, 'CM25DWIGHT', "I’ve only seen this once before, a long time ago. I was on a contract, working in a lab under Fort Zancudo. Soldiers would escort me to my work station, and watch while I worked." },
      { 'SELF', false, "Soldiers? Wait, why is the military investigating this stuff I found down at what I’m pretty sure was a U.F.O. crash site?!" },
      { false, 'CM25DWIGHT', "Are you sure it was an Unidentified Flying Object?" },
      { 'SELF', false, "Well, no… what do you know?" },
      { false, 'CM25DWIGHT', "Probably more than you can comprehend. You’re in the thick of it now, and I told you I needed an assistant. Here, take this." },
      { false, 'CM25DWIGHT', "*Dwight hands you a radio transceiver.*" },
      { false, 'CM25DWIGHT', "I made this prototype out of the sample that you gave me. You’ll need to fetch more of the metal you brought me, along with some other bits and bobs… we need to hexagulate the signal. You’re welcome to borrow the tools in my garage." },
      { false, 'CM25DWIGHT', "A good starting point would be at… Let me have a think…" },
      { false, 'CM25DWIGHT', "Let’s say latitude 340 and longitude -2214, and here before I forget take this." },
      { 'item:cm25_dbunkercard', false, "*Dwight hands you a keycard*" },
      { false, 'CM25DWIGHT', "Come back once you’ve set up all the transceivers!" },
    },
  },

  [6] = {
    not_enough_placed = {
      { false, 'CM25DWIGHT', "I’ve got a weak signal, it’s not enough! Get back out there. You got that first one set up right?" },
      { false, 'CM25DWIGHT', "Remember… the latitude is 340 and longitude is -2214." },
    },
    all_placed = {
      { false, 'CM25DWIGHT', "Fantastic, you’re here! Thank you for setting up all those transceivers." },
      { 'SELF', false, "Did you learn anything new?" },
      { false, 'CM25DWIGHT', "Well… yes and no. The good news is that I can confirm the labs under Zancudo are still active. The bad news… I’m in no shape to sneak into a military base." },
      { 'SELF', false, "And why are we sneaking into a military base?" },
      { false, 'CM25DWIGHT', "To seek the truth! Mulderite is capable of more than you might think. They were trying to get me to extract something from the metal… but why…" },
      { 'SELF', false, "How would I even get into the labs under Fort Zancudo? I didn’t even know there was anything under Fort Zancudo until now…" },
      { false, 'CM25DWIGHT', "You’ll need to keep your wits about you, and I’d recommend trying to blend in. Take this, too." },
      { 'item:cm25_wristunit', false, "*He hands you a heavy wristband with a dim monitor and lots of buttons.*" },
      { false, 'CM25DWIGHT', "Please let me know if they were ever successful with those extractions… in the meantime, you’re still welcome to use the tools in my garage." },
    },
  },

  [7] = {
    final_reminder = {
      { false, 'CM25DWIGHT', "Be sure to have your XP-ZC wrist unit when you head to Zancudo. And don’t forget to try and blend in!" },
    },
  },
}

local function CreateDwightShoppingPaper(character)
  local paper_id = tonumber('99' .. math.random(7000000, 9000000))

  local items = {
    "Clam Chowder",
    "Guava Gush Rolling Papers",
    "Deer Meat (4pc)",
    "6 pack of Cervezas",
    "A copy of FUD magazine"
  }

  local itemListHTML = ""
  for _, item in ipairs(items) do
    itemListHTML = itemListHTML .. "<li>" .. item .. "</li>\n"
  end

  local html = string.format([[ 
[nobg]
<div style="width: 540px; padding: 10px; height: 679px; background-color: white; border:solid darkgrey;">
  <h1 style="text-align: center;">Dr Dwight, PhD's Shopping List</h1>
  <hr class="solid">
  <p>Shopping List:</p>
  <ul>
    %s
  </ul>
</div>
]], itemListHTML)

  TriggerEvent('blrp_paper:server:givePaperDirectSafe', character.source, paper_id, "Dwight's Shopping", html, 1)
end

RegisterNetEvent('blrp_quests:server:talkToPed', function(_, event_data)
  local ped_id = (event_data and event_data.filter_value) or false
  if not ped_id then return end

  local character = exports.blrp_core:character(source)
  local stage = tonumber(character.getUserData(stage_key, false) or 1)

  if ped_id == 'CM25CREED' then
    if stage == 1 then
      local alreadySaidNo = character.getUserData('cm25:no_to_creed', false)
      local response
      if alreadySaidNo then
        response = tQuests.runDialogue(character.source, { dialogue[1].responses[2] })
      else
        response = tQuests.runDialogue(character.source, { dialogue[1].running })
      end

      if response == 1 then
        tQuests.runDialogue(character.source, { dialogue[1].responses[1] })
        character.setUserData(stage_key, 2, true)
        character.set(stage_key, 2)
      elseif response == 2 then
        character.setUserData('cm25:no_to_creed', true, true)
      end
    elseif stage == 2 then
      if character.getUserData('cm25:found_cam', false) and character.getUserData('cm25:found_kevin', false) then
        tQuests.runDialogue(character.source, { dialogue[3].both_found })
        character.setUserData(stage_key, 3, true)
        character.set(stage_key, 3)
        character.setUserData(s3_time_lock_key, os.time() + 86400, true)
      else
        tQuests.runDialogue(character.source, { dialogue[3].remind })
      end
    elseif stage == 3 then
      local lockTime = tonumber(character.getUserData(s3_time_lock_key, false) or 0)
      if os.time() >= lockTime then
        character.setUserData(stage_key, 4, true)
        character.set(stage_key, 4)
        tQuests.runDialogue(character.source, { dialogue[4].creed_no_findings })
      else
        tQuests.runDialogue(character.source, { dialogue[3].too_early_return })
      end
    elseif stage == 4 then
      if character.hasItemQuantity('cm25_xgoo', 1) then
        tQuests.runDialogue(character.source, { dialogue[4].creed_with_goo })
        character.take('cm25_xgoo', 1, false)
        character.give('cm25_xtool', 1)
      elseif character.hasItemQuantity('cm25_xmetal', 1) then
        tQuests.runDialogue(character.source, { dialogue[4].creed_with_metal })
        character.setUserData(stage_key, 5, true)
        character.set(stage_key, 5)
      elseif character.hasItemQuantity('cm25_xtool', 1) and not character.hasItemQuantity('cm25_xmetal', 1, false, true) then
        tQuests.runDialogue(character.source, { dialogue[4].creed_no_metal })
      else
        tQuests.runDialogue(character.source, { dialogue[4].creed_no_findings })
      end
    elseif stage >= 5 then
        tQuests.runDialogue(character.source, { dialogue[4].creed_code_remind })
    end
  elseif ped_id == 'CM25PAM' then
  if character.getUserData('cm25:pam_found', false) == true then
    tQuests.runDialogue(character.source, { dialogue[3].pam_repeat })
  else
    tQuests.runDialogue(character.source, { dialogue[3].pam_found })
    character.setUserData('cm25:pam_found', true, true)
  end
  elseif ped_id == 'CM25CAM' then
    if stage < 2 then
      tQuests.runDialogue(character.source, { dialogue[2].cam_before_creed })
    elseif not character.getUserData('cm25:cam_joint_given', false) and not character.getUserData('cm25:cam_prompted', false) then
      if character.hasItemQuantity('weed2', 1) then
        tQuests.runDialogue(character.source, { dialogue[2].cam_after_creed_start })
        tQuests.runDialogue(character.source, { dialogue[2].cam_joint_given })
        character.take('weed2', 1, false)
        character.setUserData('cm25:cam_joint_given', true, true)
        character.setUserData('cm25:found_cam', true, true)
      else
        tQuests.runDialogue(character.source, { dialogue[2].cam_after_creed_start })
        tQuests.runDialogue(character.source, { dialogue[2].cam_needs_joint })
        character.setUserData('cm25:cam_prompted', true, true)
      end
    elseif character.getUserData('cm25:cam_prompted', false) and not character.getUserData('cm25:cam_joint_given', false) then
      if character.hasItemQuantity('weed2', 1) then
        tQuests.runDialogue(character.source, { dialogue[2].cam_joint_given })
        character.take('weed2', 1, false)
        character.setUserData('cm25:cam_joint_given', true, true)
        character.setUserData('cm25:found_cam', true, true)
      else
        tQuests.runDialogue(character.source, { dialogue[2].cam_needs_joint })
      end
    elseif character.getUserData('cm25:found_cam', false) then
      tQuests.runDialogue(character.source, { dialogue[2].cam_repeat })
    end
  elseif ped_id == 'CM25KEVIN' then
    if stage < 2 then
      tQuests.runDialogue(character.source, { dialogue[2].kevin_before_creed })
    elseif not character.getUserData('cm25:found_kevin', false) then
      tQuests.runDialogue(character.source, { dialogue[2].kevin_after_creed })
      character.setUserData('cm25:found_kevin', true, true)
    else
      tQuests.runDialogue(character.source, { dialogue[2].kevin_repeat })
    end
  elseif ped_id == 'CM25DWIGHT' then
    if stage < 5 then
      tQuests.runDialogue(character.source, { dialogue[5].dwight_early })
      return
    end

    if stage == 5 then
      if not character.getUserData('cm25:bunker_entered', false) then
        if not character.hasItemQuantity('cm25_xmetal', 1) then
          if not character.getUserData('cm25:dwight_no_metal', false) then
            tQuests.runDialogue(character.source, { dialogue[5].door_knock_error })
            character.setUserData('cm25:dwight_no_metal', true, true)
          else
            tQuests.runDialogue(character.source, { dialogue[5].door_knock_false })
          end
          return
        elseif character.getUserData('cm25:dwight_no_metal', false) then
          tQuests.runDialogue(character.source, { dialogue[5].door_knock_proceed })
          character.setUserData('cm25:bunker_entered', true, true)
          character.setUserData('cm25:dwight_no_metal', false, true)
          character.take('cm25_xmetal', 1, false)
          CreateDwightShoppingPaper(character)
          return
        end

        tQuests.runDialogue(character.source, { dialogue[5].door_knock })
        character.setUserData('cm25:bunker_entered', true, true)
        character.setUserData('cm25:dwight_no_metal', false, true)
        character.take('cm25_xmetal', 1, false)
        CreateDwightShoppingPaper(character)
      else
        local groceries = {
          { 'camp_chowder', 1, false, true },
          { 'll_paper_b', 1, false },
          { 'deer_meat', 4, false },
          { 'food_mexicanbeer', 6, false },
          { 'prop_porn_mag_03', 1, false }
        }

        local has_all = true
        for _, item in ipairs(groceries) do
          if not character.hasItemQuantity(item[1], item[2], item[3] or false, item[4] or false) then
            has_all = false
            break
          end
        end

        if has_all then
          for _, item in ipairs(groceries) do
            character.take(item[1], item[2], false)
          end
          tQuests.runDialogue(character.source, { dialogue[5].has_items })
          character.give('cm25_transceiver', 1)
          character.give('cm25_dbunkercard', 1)
          character.setUserData('cm25:has_dwight_keycard', true, true)
          character.setUserData(stage_key, 6, true)
          character.set(stage_key, 6)
        else
          tQuests.runDialogue(character.source, { dialogue[5].needs_items })
        end
      end
    elseif stage == 6 then
      local placed = tonumber(character.getUserData('cm25:transceivers', false) or 0)
      if stage == 6 then
        if placed < 6 then
          tQuests.runDialogue(character.source, { dialogue[6].not_enough_placed })
        elseif placed == 6 then
          tQuests.runDialogue(character.source, { dialogue[6].all_placed })
          character.give('cm25_wristunit', 1)
          character.setUserData(stage_key, 7, true)
          character.set(stage_key, 7)
          character.log('CM25-QUEST', 'Finished Close Encounters quest dialogue, received zancudo access item', {
          item = 'cm25_wristunit',
          stage = '7',
          })
        end
      end
      elseif stage == 7 then
        tQuests.runDialogue(character.source, { dialogue[7].final_reminder })
    end
  end
end)

return {
  dialogue = dialogue,
  QUEST_ID = QUEST_ID,
  stage_key = stage_key,
  s3_time_lock_key = s3_time_lock_key,
}