tCore = T.getInstance('blrp_core', 'core')
tProps = T.getInstance('blrp_core', 'props')
tLockpick = T.getInstance('blrp_lockpick', 'LockPick')

local allowed_models = {
  [`bl_mil_cargo_a`] = {
    label = 'Cargo Crate',
    required_tool = 'wbody|WEAPON_CROWBAR',
    animation = {
      animDict = 'amb@prop_human_bum_bin@base',
      anim = 'base',
      flags = 33
    },
    loot_table = {
      { idname = 'cm25_antenna', min_amount = 1, max_amount = 1, weight = 35 },
      { idname = 'cm25_xmetal', min_amount = 1, max_amount = 2, weight = 1 },
      { idname = 'cm25_cloth', min_amount = 1, max_amount = 2, weight = 5 },
      { idname = 'cm25_thread', min_amount = 1, max_amount = 3, weight = 10 },
      { idname = '5g_blocker', min_amount = 1, max_amount = 1, weight = 1 },
      { idname = nil, min_amount = 0, max_amount = 0, weight = 39 }
    },
  },
  [`bl_mil_cargo_b`] = {
    label = 'Cargo Crate',
    required_tool = 'lockpick',
    animation = {
      animDict = 'mp_arresting',
      anim = 'a_uncuff',
      flags = 49
    },
    loot_table = {
      { idname = 'cm25_antenna', min_amount = 1, max_amount = 1, weight = 35 },
      { idname = 'cm25_xmetal', min_amount = 1, max_amount = 2, weight = 1 },
      { idname = 'cm25_cloth', min_amount = 1, max_amount = 2, weight = 5 },
      { idname = 'cm25_thread', min_amount = 1, max_amount = 3, weight = 10 },
      { idname = '5g_blocker', min_amount = 1, max_amount = 1, weight = 1 },
      { idname = nil, min_amount = 0, max_amount = 0, weight = 39 }
    },
  },
  [`bl_mil_bunker_a`] = {
    label = 'Military Crate',
    required_tool = 'lockpick',
    animation = {
      animDict = 'mp_arresting',
      anim = 'a_uncuff',
      flags = 49
    },
    loot_table = {
      { idname = 'category:zancudomre', min_amount = 1, max_amount = 1, weight = 12 },
      { idname = 'cm25_xmetal', min_amount = 1, max_amount = 2, weight = 15 },
      { idname = 'cm25_cloth', min_amount = 2, max_amount = 3, weight = 10 },
      { idname = 'cm25_thread', min_amount = 2, max_amount = 4, weight = 15 },
      { idname = 'clth_hazmat_suit', min_amount = 1, max_amount = 1, weight = 2 },
      { idname = '5g_blocker', min_amount = 1, max_amount = 1, weight = 2 },
      { idname = 'ammo_556x45', min_amount = 6, max_amount = 12, weight = 1 },
      { idname = nil, min_amount = 0, max_amount = 0, weight = 30 }
    },
  },
  [`bl_mil_bunker_b`] = {
    label = 'Military Crate',
    required_tool = 'wbody|WEAPON_CROWBAR',
    animation = {
      animDict = 'amb@prop_human_bum_bin@base',
      anim = 'base',
      flags = 49
    },
    loot_table = {
      { idname = 'category:zancudomre', min_amount = 1, max_amount = 1, weight = 12 },
      { idname = 'cm25_xmetal', min_amount = 1, max_amount = 2, weight = 15 },
      { idname = 'cm25_cloth', min_amount = 2, max_amount = 3, weight = 10 },
      { idname = 'cm25_thread', min_amount = 2, max_amount = 4, weight = 15 },
      { idname = 'clth_hazmat_suit', min_amount = 1, max_amount = 1, weight = 2 },
      { idname = '5g_blocker', min_amount = 1, max_amount = 1, weight = 2 },
      { idname = 'ammo_556x45', min_amount = 6, max_amount = 12, weight = 1 },
      { idname = nil, min_amount = 0, max_amount = 0, weight = 30 }
    },
  },
  [`bl_mil_radiated_a`] = {
    label = 'Military Crate',
    required_tool = 'lockpick',
    animation = {
      animDict = 'mp_arresting',
      anim = 'a_uncuff',
      flags = 49
    },
    loot_table = {
      { idname = 'category:zancudomre', min_amount = 1, max_amount = 1, weight = 1000 },
      { idname = 'cm25_xmetal', min_amount = 2, max_amount = 4, weight = 1500 },
      { idname = 'cm25_cloth', min_amount = 5, max_amount = 7, weight = 1500 },
      { idname = 'cm25_thread', min_amount = 6, max_amount = 8, weight = 1600 },
      { idname = 'clth_hazmat_suit', min_amount = 1, max_amount = 1, weight = 500 },
      { idname = 'cm25_aliengoo', min_amount = 1, max_amount = 1, weight = 100 },
      { idname = '5g_blocker', min_amount = 1, max_amount = 1, weight = 200 },
      { idname = 'wbody|WEAPON_BULLPUPRIFLE_MK2', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'wbody|WEAPON_SPECIALCARBINE_MK2', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'wbody|WEAPON_ASSAULTRIFLE_MK2', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'wbody|WEAPON_SP45', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'wbody|WEAPON_VP897', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'wbody|WEAPON_GRENADE', min_amount = 1, max_amount = 1, weight = 10 },
      { idname = 'ammo_556x45', min_amount = 15, max_amount = 40, weight = 100 },
      -- { idname = 'cm25_sublvlkey', min_amount = 1, max_amount = 1, weight = 200 },
      { idname = nil, min_amount = 0, max_amount = 0, weight = 2000 }
    },
  },
}

local lockouts = {}
local box_in_use = {}
local player_box_usage = {}

-- Helper function to clear box usage
local function clearBoxUsage(entity_key, src)
  if box_in_use[entity_key] and box_in_use[entity_key].player == src then
    box_in_use[entity_key] = nil
  end
  if player_box_usage[src] then
    player_box_usage[src] = nil
  end
end

RegisterNetEvent('blrp_campmorningwood:server:searchLootable', function(_, event_data)
  local src = source
  local character = exports.blrp_core:character(src)

  local position = event_data.position
  local entity_hash = event_data.entity_hash
  local model_data = allowed_models[entity_hash]

  if not position or not entity_hash or not model_data then return end

  local entity_key = entity_hash .. GetHashKey(position)
  local last_searched = lockouts[entity_key]

  -- Check if the box is already in use
  if box_in_use[entity_key] then
    character.notify('Someone is already searching this box. Please wait.')
    return
  end

  -- Check if the player is already searching another box
  if player_box_usage[src] then
    character.notify('You are already searching another box.')
    return
  end

  if last_searched and (os.time() - last_searched) < (10 * 60) then
    character.notify('This box has been searched through recently. Try again later.')
    return
  end

  if model_data.required_tool and not character.hasItemQuantity(model_data.required_tool, 1, false, true) then
    character.notify('You don\'t have the right tool to get into this.')
    return
  end

  -- Mark the box as in use and track the player
  box_in_use[entity_key] = { player = src, timestamp = os.time() }
  player_box_usage[src] = entity_key

  -- if model_data.required_tool == 'wbody|WEAPON_CROWBAR' then
  --   character.client('core:client:addPlayerProp', 'w_me_crowbar', 57005, 0.01, -0.1, -0.08, -66.0, 1.0, -38.0, false)
  -- end

  if not character.progressPromise('Searching ' .. (model_data.label or 'crate'), 3, {
    animation = model_data.animation or {
      animDict = 'amb@prop_human_bum_bin@base',
      anim = 'base',
      flags = 33
    }
  }) then
    clearBoxUsage(entity_key, src)
    return
  end

  tProps.softRemoveProps(character.source, {})

  if model_data.required_tool == 'lockpick' then
    local raw = tLockpick.hack(character.source, {7, math.random(2, 3), 4})
    if not raw.success then
      character.stopAnimation(false)
      character.take('lockpick', 1)
      clearBoxUsage(entity_key, src)
      return
    end
  end

  lockouts[entity_key] = os.time()

  local loot_table = model_data.loot_table or {}
  local total_weight = 0
  for _, item in ipairs(loot_table) do
    total_weight = total_weight + item.weight
  end

  local roll = math.random(1, total_weight)

  local current = 0
  local selected_item = nil
  for _, item in ipairs(loot_table) do
    current = current + item.weight
    if roll <= current then
      selected_item = item
      break
    end
  end

  if selected_item then
    local was_substituted = false
    local idname = selected_item.idname
    local amount = math.random(selected_item.min_amount, selected_item.max_amount)

    if idname and tostring(idname):sub(1, 6) == 'wbody|' then
      local wbody_lock_key = ':wbodylock'
      local now = os.time()
      local locked_until = tonumber(character.getUserData(wbody_lock_key, true) or 0)

      if locked_until > now then
        local category_items = exports.blrp_core:GetItemsByCategory('zancudomre') or {}
        if #category_items > 0 then
          idname = category_items[math.random(#category_items)]
          amount = 1
          was_substituted = true
        else
          idname = nil
          amount = 0
        end
      else
        character.setUserData(wbody_lock_key, now + 86400, true)
      end
    end

    if idname and idname:sub(1, 9) == 'category:' then
      local category = idname:sub(10)
      local items = exports.blrp_core:GetItemsByCategory(category) or {}
      if #items > 0 then
        idname = items[math.random(#items)]
      else
        idname = nil
      end
    end

    if idname and amount > 0 then
      character.give(idname, amount, nil, false)
      local item_label = exports.blrp_core:GetItemName(idname)
      if amount == 1 then
        local article = 'a'
        if item_label:lower():find('^[aeiou]') then article = 'an' end
        character.notify('You found ' .. article .. ' ' .. item_label .. ' rummaging through the crate.')
      else
        character.notify('You found ' .. amount .. 'x ' .. item_label .. ' rummaging through the crate.')
      end
      if was_substituted then
        character.log('CM25-QUEST', 'Substituted weapon reward due to lockout', {
          crate = model_data.label,
          reward = idname,
          amount = amount
        })
      else
        character.log('CM25-QUEST', 'Searched ' .. model_data.label, {
          reward = idname,
          amount = amount
        })
      end
    else
      character.notify('You find nothing of value here.')
    end
  else
    character.notify('You find nothing of value here.')
  end

  clearBoxUsage(entity_key, src)
end)

-- Handle player disconnect to clear box usage
AddEventHandler('playerDropped', function()
  local src = source
  if player_box_usage[src] then
    local entity_key = player_box_usage[src]
    clearBoxUsage(entity_key, src)
  end
end)