local QUEST_ID = 'cm25quest:otherpeds'
local stage_key = QUEST_ID .. ':stage'

local petDog = {
  [1] = {
    running = {
      { false, false, "*the dog growls at you*" },
      { 'CM25CREED', false, "Careful around A.L.F.! He can spot a fed from a yard away!"}
    },
  }
}

local hollyDialogue = {
  [1] = {
    running = {
      { 'CM25HOLLY', false, "Hey, Do you need a copy of the Camp Morningwood site map?" },
      { 'SELF', false, {
        "Sure.",
        "No thanks.",
      } },
    },
  }
}

local BETTADialogue = {
  [1] = {
    running = {
      { 'CM25TOBY', false, "Hey, can I interest you in a short run down about what we at BETTA Pharmaceuticals do for the commmunity?" },
      { 'SELF', false, {
        "Sure.",
        "No thanks.",
      } },
    },
    responses = {
      { 'CM25TOBY', false, "Okay have a good da... Wait you said yes? Wonderful!" },
      { 'CM25TOBY', false, "Here at BETTA Pharmaceuticals we pride ourselves on new and innovative forms of medicine designed to target every day, every person issues." },
      { 'CM25TOBY', false, "Take <PERSON><PERSON> for example, did you know that 80% of the population over the age of 23 secretly use <PERSON>llis to enhance their every day lives." },
      { false, 'SELF', "Umm and Mollis does what exactly?" },
      { 'CM25TOBY', false, "Ah, now isn't that the real question!" },
      { false, 'SELF', "Yeah bu..." },
      { 'CM25TOBY', false, "Anyway, here's free shirt if you're wanting to represent our brand. I think I see another excited future pharmaceuticals rep, have a good day!" },
    },
    alreadyTee = {
      { 'CM25TOBY', false, "Hey there BETTA rep! Hope you're doing well."},
    },
  }
}

RegisterNetEvent('blrp_quests:server:talkToPed', function(_, event_data)
  local ped_id = (event_data and event_data.filter_value) or false

  if not ({
    ['CM25ALF'] = true,
    ['CM25HOLLY'] = true,
    ['CM25TOBY'] = true,
  })[ped_id] then
    return
  end

  local character = exports.blrp_core:character(source)
  local stage = tonumber(character.getUserData(stage_key, false) or 1)

  if ped_id == 'CM25ALF' then
    if stage == 1 then
      local response = tQuests.runDialogue(character.source, { petDog[stage].running })

      if not response then
        return
      end

      character.setUserData(stage_key, 2, true)
      character.set(stage_key, 2)
      character.playSoundAround(3, 'dog_bark')
    end

    if stage == 2 then
      character.playSoundAround(3, 'dog_bark')
    end

  elseif ped_id == 'CM25HOLLY' then
    local response = tQuests.runDialogue(character.source, { hollyDialogue[1].running })

    if response == 1 then
      character.give('camp_map', 1)
    elseif response == 2 then
    end
    elseif ped_id == 'CM25TOBY' then
      if character.getUserData('cm25:got_betta_tee', false) then
        tQuests.runDialogue(character.source, { BETTADialogue[1].alreadyTee })
        return
      end
    
      local response = tQuests.runDialogue(character.source, { BETTADialogue[1].running })
      if response == 1 then
        tQuests.runDialogue(character.source, { BETTADialogue[1].responses })
      
        local shirtOptions = {
          'clth_cm25_jbib_a',
          'clth_cm25_jbib_b',
          'clth_cm25_jbib_c',
          'clth_cm25_jbib_d',
          'clth_cm25_jbib_e',
          'clth_cm25_jbib_f',
          'clth_cm25_jbib_g',
          'clth_cm25_jbib_h',
        }
        local randomItem = shirtOptions[math.random(#shirtOptions)]

        character.give(randomItem, 1)
        character.setUserData('cm25:got_betta_tee', true, true)
    end
  end
end)