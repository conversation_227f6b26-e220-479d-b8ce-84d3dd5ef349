local placed_antennas = {}

local antenna_zones = {
  dwight_antenna_1 = { center = vector3(-2214.647, 342.539, 199.106), length = 5.0, width = 5.0, minZ = 195.5, maxZ = 205.0 },
  dwight_antenna_2 = { center = vector3(-701.245, 59.038, 69.685), length = 5.0, width = 5.0, minZ = 65.0, maxZ = 75.5 },
  dwight_antenna_3 = { center = vector3(758.423, 1274.283, 360.297), length = 15.0, width = 15.0, minZ = 355.5, maxZ = 365.0 },
  dwight_antenna_4 = { center = vector3(-1005.474, 4851.990, 274.606), length = 5.0, width = 5.0, minZ = 270.0, maxZ = 280.5 },
  dwight_antenna_5 = { center = vector3(450.848, 5566.435, 796.624), length = 5.0, width = 5.0, minZ = 790.0, maxZ = 800.5 },
  dwight_antenna_6 = { center = vector3(2794.760, 5994.551, 356.107), length = 10.0, width = 5.0, minZ = 350.0, maxZ = 360.0 }
}

local function isInsideBoxZone(coords, center, length, width, minZ, maxZ)
  local dx = math.abs(coords.x - center.x)
  local dy = math.abs(coords.y - center.y)
  local dz = coords.z
  return dx <= (length / 2) and dy <= (width / 2) and dz >= (minZ or -math.huge) and dz <= (maxZ or math.huge)
end

function placeTransceiver(character, item_id)
  local source = character.source
  local player_id = character.get('id')
  local coords = GetEntityCoords(GetPlayerPed(source))

  local matched_zone = nil
  for name, data in pairs(antenna_zones) do
    if isInsideBoxZone(coords, data.center, data.length, data.width, data.minZ, data.maxZ) then
      matched_zone = name
      break
    end
  end

  if not matched_zone then
    character.notify("This doesn't seem like the best place to set up a tranceiver.")
    return
  end

  local raw = character.getUserData('cm25:transceivers_zones', false, false)
  local placed_data = {}

  if type(raw) == "string" then
    local success, decoded = pcall(json.decode, raw)
    if success and type(decoded) == "string" then
      local success2, decoded2 = pcall(json.decode, decoded)
      if success2 and type(decoded2) == "table" then
        placed_data = decoded2
      end
    elseif success and type(decoded) == "table" then
      placed_data = decoded
    end
  elseif type(raw) == "table" then
    placed_data = raw
  end

  placed_antennas[player_id] = placed_antennas[player_id] or {}

  if placed_data[matched_zone] or placed_antennas[player_id][matched_zone] then
    character.notify('You already set up a radio tranceiver here.')
    return
  end

  character.client('blrp_inventory:hide')
  character.animate({{ 'anim@heists@ornate_bank@hack_heels', 'hack_loop', 1 }}, false, true)
  if not character.progressPromise('Installing Radio Tranceiver', 10) then
    character.stopAnimation(false)
    return
  end

  character.stopAnimation(false)
  placed_antennas[player_id][matched_zone] = true
  placed_data[matched_zone] = true

  local encoded = json.encode(placed_data)
  character.setUserData('cm25:transceivers_zones', encoded, true, false)

  local current_count = tonumber(character.getUserData('cm25:transceivers', false) or 0)
  local new_count = math.min(current_count + 1, 6)
  character.setUserData('cm25:transceivers', new_count, true)

  character.take(item_id, 1, false)
  character.notify('You set up a radio tranceiver.')
end

exports('placeTransceiver', function(character, item_id)
  return placeTransceiver(character, item_id)
end)