if config.bridge.nd or  config.bridge.esx or config.bridge.qb then return end

-- For support join my discord: https://discord.gg/Z9Mxu72zZ6
tAtmRobberies = T.getInstance('blrp_atm_robbery', 'atm-robberies')
local rope_validations = { }
local valid_locations = { }
local gangCooldown = { }

local gangCooldownTime = (60 * 60)-- 45 minutes

RegisterServerEvent('blrp_atm_robbery:server:startDrillFromItem')
AddEventHandler('blrp_atm_robbery:server:startDrillFromItem', function(_source)
   local character = exports.blrp_core:character(_source)

  if not character.hasItemQuantity('bank_drill', 1) then
    character.notify('What are you going to use?')
    return
  end

  local gangs = character.getBusinessesByType('Gang')

  for _, gang_data in pairs(gangs) do
    if gangCooldown[gang_data.business_id] and os.time() - gangCooldown[gang_data.business_id] < gangCooldownTime then
      character.notify('Your drill bit slipped, try again later.')
      return
    end
  end

  if #gangs == 0 then
    character.notify('You don\'t have the skill, your drill bit breaks.')
    return
  end

  local drillSuccess = tAtmRobberies.startDrill(character.source, {})

  if drillSuccess then
    character.set('pending_atm_reward', true)
    local gangs = character.getBusinessesByType('Gang')

    for _, gang_data in pairs(gangs) do
      gangCooldown[gang_data.business_id] = os.time()
    end
  end
end)

RegisterServerEvent('blrp_atm_robbery:server:triedToCancelEmotes')
AddEventHandler('blrp_atm_robbery:server:triedToCancelEmotes', function()

end)

AddEventHandler('blrp_atm_robbery:server:registerAtmRopePull', function(_player)
    rope_validations[_player] = true
    TriggerClientEvent('blrp_atm_robbery:client:startRope', _player)
end)


-- you can notify cops using this, or not if you don't want to. notifyType can be "drill" or "rope".
function notifyCops(notifyType, coords, street)
  local chance = math.random(1, 10)
  if notifyType == 'rope' and chance < 4 then
      exports.blrp_core:group('Police').alert({
        coords = coords,
        location_override = street,
        badge = 'Priority 3',
        badge_style = 'info',
        msg = '<br/>ATM Tamper Alert',
        icon = 'far fa-door-open',
        sound = 'ToneP3',

        leo_auto_status_code = 'LC',
        leo_auto_status_message = 'ATM Tamper Alert',
      })
  elseif notifyType == 'drill' and chance < 3 then
    exports.blrp_core:group('Police').alert({
      coords = coords,
      location_override = street,
      badge = 'Priority 3',
      badge_style = 'info',
      msg = '<br/>ATM Tamper Alert',
      icon = 'far fa-door-open',
      sound = 'ToneP3',

      leo_auto_status_code = 'LC',
      leo_auto_status_message = 'ATM Tamper Alert',
    })
  end

end

-- result after searching the atm.
function atmRobbed(source)
  local character = exports.blrp_core:character(source)

  if not character.get('pending_atm_reward') then
      character.notify('The person that drilled must collect the reward')
      return
  end

  character.set('pending_atm_reward', false)


  local moneyFound = math.random(5000, 7400)

  local copsAroundPlayer = getCopsCountAroundSource(character.source)

  if copsAroundPlayer > 0 then
      moneyFound = math.random(12400, 17600)
  end

  if copsAroundPlayer > 1 then
      moneyFound = math.random(15000, 22800)
  end



  character.give('cash', moneyFound)
  character.log('REWARD', 'Gave ' .. moneyFound .. ' cash from atm robbery reward')
end

function getCopsCountAroundSource(_player)
    local character = exports.blrp_core:character(_player)
    local sources_around_me = character.peopleAroundMe(500)
    local total = 0

    for _, _source in pairs(sources_around_me) do
        local checking_character = exports.blrp_core:character(_source)

        if checking_character.hasGroup('LEO') then
            total = total + 1
        end
    end

    return total
end

-- check for amount of police and return true if there's enough. Other wise it will return false.
function policeCheck()
    return true
end