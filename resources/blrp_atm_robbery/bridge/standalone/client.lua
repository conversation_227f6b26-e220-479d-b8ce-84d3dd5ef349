if config.bridge.nd or  config.bridge.esx or config.bridge.qb then return end

-- For support join my discord: https://discord.gg/Z9Mxu72zZ6

-- For support join my discord: https://discord.gg/Z9Mxu72zZ6
tAtmRobberies = { }
T.bindInstance('atm-robberies', tAtmRobberies)
-- Create blip on the map for the cops.
RegisterNetEvent("ND_ATMRobbery:Report", function(coords, street, report)
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)

    SetBlipSprite(blip, 161)
    SetBlipColour(blip, 1)
    SetBlipScale(blip, 1.0)
    SetBlipAsShortRange(blip, true)

    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("ATM Alarm: " .. street)
    EndTextCommandSetBlipName(blip)

    TriggerEvent("chat:addMessage", {
        color = {255, 0, 0},
        args = {"ATM Alarm", report}
    })

    Wait(30000)
    RemoveBlip(blip)
end)


tAtmRobberies.startDrill = function()
  local drillStatus = useDrill()
  return drillStatus
end


RegisterNetEvent('blrp_atm_robbery:client:startRope')
AddEventHandler('blrp_atm_robbery:client:startRope', function()
    useRope()
end)