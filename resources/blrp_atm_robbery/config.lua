language = {
    ["alreadyDrilled"] = "This ATM has already been drilled, you can now attach a hook.",
    ["drillingComplete"] = "Drilling ATM complete, you can now attach a hook.",
    ["nothingToDrill"] = "Hmmm, something doesn't seem right here.",
    ["alreadyUsingRope"] = "You're already using a rope.",
    ["tryAgain"] = "Error try again.",
    ["nothingToAttach"] = "Nothing to attach the rope to.",
    ["vehicleFar"] = "Vehicle went too far.",
    ["copCheck"] = "Not enough police online.",
    ["cooldownCheck"] = "Another atm was robbed recently, there's currenlty a %s minute cooldown.",
    ["search"] = "[G] - Search",
    ["attach"] = "[G] - Attach rope",
    ["detach"] = "[G] - Detach rope",
    ["drillhole"] = "Drill a hole in the ATM to attach the hook.",
    ["info"] = "Info",
    ["error"] = "Error",
    ["success"] = "Success",
    ["snapped"] = "Rope snapped",
    ["mightsnap"] = "Rope might snap",
    ["cantattach"] = "Can't attach!"

}

config = {
    framework = "standalone", -- nd, esx, qb.
    drillTime = 27, -- 15 seconds to finish drilling the atm.
    interactKey = 47,
    copCheck = 0, -- there has to be 3 or more cops online.
    globalCooldown = 0, -- global cooldown in (minutes)
    ox_inventory = false -- if you're using ox inventory then set to true, otherwise keep it false.
}

-- Notification, this can be changed to whatever you want, buy default it uses ox_lib notification.
function notify(notiTitle, notiDescription, notiType)
    TriggerEvent('vrp:client:notify', notiDescription)
end