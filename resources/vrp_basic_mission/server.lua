
local Tunnel = module("vrp", "panopticon/sv_pano_tunnel")
local Proxy = module("vrp", "lib/Proxy")
local Lang = module("vrp", "lib/Lang")
local cfg = module("vrp_basic_mission", "cfg/missions")

-- load global and local languages
local glang = Lang.new(module("vrp", "cfg/lang/"..cfg.lang) or {})
local lang = Lang.new(module("vrp_basic_mission", "cfg/lang/"..cfg.lang) or {})

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP","vRP_basic_mission")
Tunnel.initiateProxy()

function stringsplit(inputstr, sep)
	if inputstr ~= nil then
		if sep == nil then
						sep = "%s"
		end
		local t={}
		for str in string.gmatch(inputstr, "([^"..sep.."]+)") do
						table.insert(t,str)
		end
		return t
	else
		return nil
	end
end

function task_mission()
  -- REPAIR
  for k,v in pairs(cfg.repair) do -- each repair perm def
    -- add missions to users
    local users = vRP.getUsersByPermission({k})
    for l,w in pairs(users) do
      local user_id = w
      local player = vRP.getUserSource({user_id})
      if not vRP.hasMission({player}) then
        if math.random(1,v.chance) == 1 then -- chance check
          -- build mission
          local mdata = {}
          mdata.name = lang.repair({v.title})
          mdata.steps = {}

          -- build steps
          for i=1,v.steps do
            local step = {
              text = lang.repair({v.title}).."<br />"..lang.reward({v.reward}),
              onenter = function(player, area)
                local character = exports.blrp_core:character(player)
								if vRP.hasMission({player}) then
									vRPclient.isPedInCar(player,{},function(inveh)
										if not inveh then
			                vRPclient.getActionLock(player, {},function(locked)
			                  if not locked then
			                    if character.take('repairkit', 1) then
			                      vRPclient.setActionLock(player,{true})
			                      vRPclient.playAnim(player,{false,{task="WORLD_HUMAN_WELDING"},false})
			                      SetTimeout(15000, function()
			                        vRP.nextMissionStep({player})
			                        vRPclient.stopAnim(player,{false})
			                        vRPclient.setActionLock(player,{false})

			                        -- last step
			                        if i == v.steps then
																character.giveBankMoney(v.reward)
			                          vRPclient.notify(player,'$' .. v.reward .. ' has been deposited into your bank account')
			                        end
			                      end)
			                    end
			                  end
			                end)
										end
									end)
								end
              end,
              position = v.positions[math.random(1,#v.positions)]
            }

            table.insert(mdata.steps, step)
          end

          vRP.startMission({player,mdata})
        end
      end
    end
  end

  -- DELIVERY
  --for k,v in pairs(cfg.delivery) do -- each repair perm def
  --  -- add missions to users
  --  local users = vRP.getUsersByPermission({k})
  --  for l,w in pairs(users) do
  --    local user_id = w
  --    local player = vRP.getUserSource({user_id})
  --    if not vRP.hasMission({player}) then
	--			if math.random(1,v.chance) == 1 then -- chance check
	--        -- build mission
	--        local mdata = {}
	--        mdata.name = lang.delivery.title()
  --
	--        -- generate items
	--        local todo = 0
	--        local delivery_items = {}
	--        for idname,data in pairs(v.items) do
	--          local amount = math.random(data[1],data[2])
	--          if amount > 0 then
	--            delivery_items[idname] = amount
	--            todo = todo+1
	--          end
	--        end
  --
	--        local step = {
	--          text = "",
	--          onenter = function(player, area)
  --            local character = exports.blrp_core:character(player)
	--			local _r,_f,business = table.unpack(stringsplit(area, ":"))
	--            for idname,amount in pairs(delivery_items) do
	--              if amount > 0 then -- check if not done
	--                if character.take(idname, amount) then
	--                  local reward = (v.items[idname][3]*amount )
	--									character.giveBankMoney(reward)
	--									TriggerClientEvent('vrp:client:notify', player, '$' .. reward .. ' has been deposited into your bank account')
  --
	--                  todo = todo-1
	--                  delivery_items[idname] = 0
	--                  if todo == 0 then -- all received, finish mission
	--                    vRP.nextMissionStep({player})
	--                  end
	--                end
	--              end
	--            end
	--          end,
	--          position = v.positions[math.random(1,#v.positions)]
	--        }
  --
	--        -- mission display
	--        for idname,amount in pairs(delivery_items) do
	--          local name = exports.blrp_core:GetItemName(idname)
	--          step.text = step.text..lang.delivery.item({name,amount}).."<br />"
	--        end
  --
	--        mdata.steps = {step}
  --
	--        if todo > 0 then
	--          vRP.startMission({player,mdata})
	--        end
	--			end
  --    end
  --  end
  --end

  SetTimeout(60000,task_mission)
end

task_mission()
