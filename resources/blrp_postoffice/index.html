<!DOCTYPE html>
<html lang="en">
    <head>
        <title>BLRP Post Office</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <script src="js/jquery-3.5.1.min.js"></script>
        <script src="js/bootstrap.min.js"></script>
        <link href="css/bootstrap.min.css" rel="stylesheet">
        <link href="css/main.css" rel="stylesheet">
    </head>

    <body>
        <div id="content" class="container">
            <div class="row">
                <div class="col-4">
                    <img src="images/gp_logo.png" style="width:175px;height:182px;opacity:0.75" alt="GoPostal Logo">
                </div>

                <div class="col">
                    <p class="fs-1 mb-1 text-center">GoPostal Mail Service</p>

                    <div class="bd-highlight mb-2">Complete the form below. On the next step, you will be able to deposit your items.</div>

                    <div class="mb-2">
                        <label for="address_select" class="form-label">Destination Address</label>
                        <select class="form-select" id="address_select">
                            <option value="-1">Default option</option>
                        </select>
                    </div>

                    <div class="mb-2">
                        <label for="address_select" class="form-label">Mail Type</label>
                        <select class="form-select" id="type_select">
                            <option value="-1">Default option</option>
                        </select>
                    </div>

                    <div class="mb-2">
                        <label for="input_to" class="form-label">Recipient (to)</label>
                        <input type="text" id="input_to" class="form-control">
                        <div class="form-text">Recipient's name, nickname, etc.</div>
                    </div>

                    <div class="mb-2">
                        <label for="input_from" class="form-label">Sender (from)</label>
                        <input type="text" id="input_from" class="form-control">
                        <div class="form-text">Your name or leave blank to remain anonymous</div>
                    </div>

                    <div class="row">
                        <div class="col-8">
                            <div class="bd-highlight mb-2" id="cost_wrapper" style="display: none">
                              <span>Shipment cost:</span>
                              <span class="float-end">$<span id="cost_value"></span></span>
                            </div>
                        </div>

                        <div class="col-4">
                            <button type="button" class="btn btn-secondary float-end" id="btn_next">Next Step &raquo;</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let interfaceShowing = false;
            let house_data = {};
            let office_locations = null;
            let selected_office_name = null;
            let selected_office_location = null;
            let mail_types = null;

            function showInterface() {
                interfaceShowing = true;
                $('body').show();
            }

            function hideInterface() {
                if(interfaceShowing) escapeInterface();
                interfaceShowing = false;
                $('body').hide();
            }

            function escapeInterface() {
                fetch(`https://blrp_postoffice/escape`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json; charset=UTF-8',},
                    body: JSON.stringify({})
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });
            }

            $('#address_select, #type_select').change(function() {
                let address_id = $('#address_select').find(":selected").val();
                let type_id = $('#type_select').find(":selected").val();

                if(address_id != -1 && type_id != -1) {
                    let cost = get_cost(address_id, type_id);

                    if(cost > 0) {
                        $("#cost_wrapper").show();
                        $("#cost_value").text(cost);
                    } else {
                        $("#cost_wrapper").hide();
                        $("#cost_value").text("");
                    }
                }
            });

            $('#btn_next').click(function() {
                let address_id = $('#address_select').find(":selected").val();
                let type_id = $('#type_select').find(":selected").val();
                let recipient_name = $('#input_to').val();
                let sender_name = $('#input_from').val();

                if(address_id != -1 && type_id != -1 && recipient_name != "") {
                    $.each(house_data, function(k, house) {
                        if(house !== null && house.id == address_id) {
                            fetch(`https://blrp_postoffice/next_step`, {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json; charset=UTF-8',},
                                body: JSON.stringify({
                                  recipient_name: recipient_name,
                                  sender_name: sender_name,
                                  mail_type: type_id,
                                  destination: house,
                                  system_location: selected_office_name,
                                })
                            }).then(resp => resp.json()).then(resp => {
                                return resp;
                            });

                            return false;
                        }
                    });
                }
            });

            window.addEventListener('message', event => {
                if(!event.data) return false;

                if(event.data.type === 'interface:show') {
                    $('#address_select').val("-1");
                    $('#type_select').val("-1");
                    $('#input_to').val("");
                    $('#input_from').val("");
                    $("#cost_wrapper").hide();
                    $("#cost_value").text("");

                    $.each(office_locations, function(k, location) {
                        if(location.args == event.data.location) {
                            selected_office_location = location.coords;
                            selected_office_name = location.args;
                        }
                    });

                    showInterface();
                }

                if(event.data.type === 'interface:hide') {
                    hideInterface();
                }

                if(event.data.type === 'interface:updateData') {
                    $('#address_select').find('option').remove();
                    $('#type_select').find('option').remove();

                    house_data = event.data.house_data;
                    office_locations = event.data.office_locations;
                    mail_types = event.data.mail_types;

                    $.each(house_data, function(k, house) {
                        if(house === null) {
                            house_data.splice(k, 1)
                        }
                    });

                    var reA = /[^a-zA-Z]/g;
                    var reN = /[^0-9]/g;

                    // Sort by street (alphabetically), then by number
                    house_data.sort(function(a, b) {
                        if(a == null || a == undefined || a.address == null || a.address == undefined) {
                          a = ''
                        } else {
                          a = a.address
                        }

                        if(b == null || b == undefined || b.address == null || b.address == undefined) {
                          b = ''
                        } else {
                          b = b.address
                        }

                        var aA = a.replace(reA, "");
                        var bA = b.replace(reA, "");

                        if(aA === bA) {
                          var aN = parseInt(a.replace(reN, ""), 10);
                          var bN = parseInt(b.replace(reN, ""), 10);
                          return aN === bN ? 0 : aN > bN ? 1 : -1;
                        } else {
                          return aA > bA ? 1 : -1;
                        }
                    });

                    $('#address_select').append('<option value="-1">--- Select an address ---</option>')

                    $.each(house_data, function(k, house) {
                        if(house == null) {
                            return;
                        }

                        if(
                          house.mail_location !== null &&
                          house.mail_location !== undefined &&
                          house.hasOwnProperty("owner_character_id") &&
                          house.owner_character_id !== null &&
                          /\d/.test(house.address) &&
                          !house.address.includes("*")

                        ) {
                            $('#address_select').append('<option value="' + house.id + '">' + house.address + '</option>')
                        }
                    });

                    $('#type_select').append('<option value="-1">--- Select package type ---</option>')

                    $.each(mail_types, function(type_name, type_options) {
                        let type_name_uc = type_name.charAt(0).toUpperCase() + type_name.slice(1)

                        $('#type_select').append('<option value="' + type_name + '">' + type_name_uc + ' (available weight: ' + type_options.chest_weight + 'kg)</option>')
                    });
                }
            });

            document.onkeydown = evt => {
                if(!interfaceShowing) return false;
                evt = evt || window.event;
                if (evt.keyCode === 27) hideInterface();
            }

            function get_cost(address_id, type_id) {
                let cost = 0;

                if(address_id != -1 && type_id != -1) {
                    $.each(mail_types, function(type_name, type_options) {
                        if(type_name == type_id) {
                            $.each(house_data, function(k, house) {
                                if(house !== null && house.id == address_id) {
                                    let distance = get_2d_distance(house.mail_location, selected_office_location);

                                    cost = distance * type_options.cost_per_meter;

                                    return false;
                                }
                            });

                            return false;
                        }
                    });
                }

                return Math.ceil(cost);
            }

            function get_2d_distance(point1, point2) {
                let dx = point2.x - point1.x;
                let dy = point2.y - point1.y;

                return Math.ceil(Math.sqrt(dx * dx + dy * dy))
            }
        </script>
    </body>
</html>
