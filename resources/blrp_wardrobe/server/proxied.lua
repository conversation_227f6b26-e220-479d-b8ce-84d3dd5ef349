-- Wardrobe Proxied Server Functions
pWardrobe = {}
P.bindInstance('wardrobe', pWardrobe)

-- Get clothing store instance
tClothingStore = T.getInstance('blrp_clothingstore', 'clothingstore')

-- Save outfit to wardrobe
pWardrobe.saveOutfit = function(outfit_name, allow_dress, force_user_id, favorite, save_hair, folder_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe then
        wardrobe = {}
    end

    -- Get current customization using the clothing store's method
    local custom_to_save = tClothingStore.getCustomization(character.source)

    if not custom_to_save then
        character.notify('Failed to get current customization')
        return
    end

    -- Convert folder_id to number if it's a string, or keep as nil if not provided
    if folder_id and folder_id ~= "" then
        folder_id = tonumber(folder_id)

        -- Validate that the folder exists
        if not folder_cache[user_id] then
            loadFolders(user_id)
        end

        if folder_id and folder_cache[user_id] and not folder_cache[user_id][folder_id] then
            character.notify('Invalid folder selected')
            print('[WARDROBE DEBUG] Invalid folder_id: ' .. tostring(folder_id) .. ' for user: ' .. user_id)
            folder_id = nil
        end
    else
        folder_id = nil
    end

    -- Add metadata
    custom_to_save.version = tonumber(current_version)
    custom_to_save.favorite = favorite or nil
    custom_to_save.save_hair = save_hair or nil
    custom_to_save.folder_id = folder_id
    custom_to_save.created_at = os.time()

    wardrobe[outfit_name] = custom_to_save
    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit saved: ' .. outfit_name)
    character.log('ACTION', 'Saved outfit: ' .. outfit_name .. (folder_id and (' to folder ID: ' .. folder_id) or ' as unassigned'))

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Apply outfit customization
pWardrobe.setCustomization = function(outfit_name, force_user_id, model)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    local outfit_data = wardrobe[outfit_name]
    
    -- Apply customization using clothing store's method
    local save_hair = outfit_data.save_hair
    tClothingStore.setCustomization(character.source, { outfit_data, false, false, false, save_hair })
    
    character.notify('Applied outfit: ' .. outfit_name)
    character.log('ACTION', 'Applied outfit: ' .. outfit_name)
end

-- Create a new folder
pWardrobe.createFolder = function(folder_name, color, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_name or folder_name == '' or string.len(folder_name) > 50 then
        character.notify('Invalid folder name')
        return
    end

    local folder_id = createFolder(user_id, folder_name, color)

    print('created folder_id', folder_id)
    if folder_id then
        character.notify('Folder created: ' .. folder_name)
        character.log('ACTION', 'Created folder: ' .. folder_name)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to create folder (name may already exist)')
    end
end

-- Update a folder
pWardrobe.updateFolder = function(folder_id, folder_name, color, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_id then
        character.notify('Invalid folder')
        return
    end

    if not folder_name or folder_name == '' or string.len(folder_name) > 50 then
        character.notify('Invalid folder name')
        return
    end

    local success = updateFolder(user_id, folder_id, folder_name, color or '#5865f2')

    if success then
        character.notify('Folder updated: ' .. folder_name)
        character.log('ACTION', 'Updated folder ID: ' .. folder_id .. ' to name: ' .. folder_name)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to update folder')
    end
end

-- Delete a folder
pWardrobe.deleteFolder = function(folder_id, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_id then
        character.notify('Invalid folder')
        return
    end

    if not character.request('Are you sure you want to delete this folder? Outfits in this folder will become unassigned.') then
        return
    end

    local success = deleteFolder(user_id, folder_id)

    if success then
        character.notify('Folder deleted')
        character.log('ACTION', 'Deleted folder ID: ' .. folder_id)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to delete folder')
    end
end

-- Move outfit to folder
pWardrobe.moveOutfitToFolder = function(outfit_name, folder_id, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end

    -- Update outfit folder
    wardrobe[outfit_name].folder_id = folder_id

    -- Save to database
    saveWardrobe(user_id, wardrobe)

    local folder_name = 'Unassigned'
    if folder_id and folder_cache[user_id] and folder_cache[user_id][folder_id] then
        folder_name = folder_cache[user_id][folder_id].name
    end

    character.notify('Moved outfit to: ' .. folder_name)
    character.log('ACTION', 'Moved outfit "' .. outfit_name .. '" to folder: ' .. folder_name)

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Toggle outfit favorite status
pWardrobe.toggleFavorite = function(outfit_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    local current_favorite = wardrobe[outfit_name].favorite or false
    wardrobe[outfit_name].favorite = not current_favorite
    
    saveWardrobe(user_id, wardrobe)

    local status = wardrobe[outfit_name].favorite and 'favorited' or 'unfavorited'
    character.notify('Outfit ' .. status .. ': ' .. outfit_name)
    character.log('ACTION', 'Toggled favorite for outfit: ' .. outfit_name)

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Rename outfit
pWardrobe.renameOutfit = function(old_name, new_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[old_name] then
        character.notify('Outfit not found')
        return
    end
    
    if wardrobe[new_name] then
        character.notify('An outfit with that name already exists')
        return
    end
    
    wardrobe[new_name] = wardrobe[old_name]
    wardrobe[old_name] = nil
    
    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit renamed to: ' .. new_name)
    character.log('ACTION', 'Renamed outfit from "' .. old_name .. '" to "' .. new_name .. '"')

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    character.log('ACTION', 'Renamed outfit from "' .. old_name .. '" to "' .. new_name .. '"')
end

-- Delete outfit
pWardrobe.deleteOutfit = function(outfit_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    wardrobe[outfit_name] = nil
    saveWardrobe(user_id, wardrobe)
    
    -- Reopen wardrobe to refresh
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, character.source, true, force_user_id)
    
    character.notify('Outfit deleted: ' .. outfit_name)
    character.log('ACTION', 'Deleted outfit: ' .. outfit_name)
end

-- Share outfit with nearby player
pWardrobe.shareOutfit = function(outfit_name, target_player_id, force_user_id)
    local character = exports.blrp_core:character(source)
    local target_character = exports.blrp_core:character(target_player_id)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not target_character then
        character.notify('Target player not found')
        return
    end
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    local outfit_data = wardrobe[outfit_name]
    
    -- Get target player's wardrobe
    local target_user_id = tonumber(target_character.get('identifier'))
    
    if not wardrobe_cache[target_user_id] then
        loadWardrobe(target_user_id)
    end
    
    local target_wardrobe = wardrobe_cache[target_user_id] or {}
    
    -- Create shared outfit name
    local shared_name = outfit_name .. ' (from ' .. character.get('name') .. ')'
    local counter = 1
    
    while target_wardrobe[shared_name] do
        shared_name = outfit_name .. ' (from ' .. character.get('name') .. ') ' .. counter
        counter = counter + 1
    end
    
    -- Copy outfit data
    local shared_outfit = {}
    for k, v in pairs(outfit_data) do
        shared_outfit[k] = v
    end
    shared_outfit.created_at = os.time()
    shared_outfit.folder = 'shared'
    
    target_wardrobe[shared_name] = shared_outfit
    saveWardrobe(target_user_id, target_wardrobe)
    
    character.notify('Outfit shared with ' .. target_character.get('name'))
    target_character.notify('Received outfit "' .. shared_name .. '" from ' .. character.get('name'))
    
    character.log('ACTION', 'Shared outfit "' .. outfit_name .. '" with ' .. target_character.get('name'))
    target_character.log('ACTION', 'Received outfit "' .. shared_name .. '" from ' .. character.get('name'))
end

-- Update outfit folder
pWardrobe.updateOutfitFolder = function(outfit_name, new_folder, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    wardrobe[outfit_name].folder = new_folder or 'default'
    saveWardrobe(user_id, wardrobe)
    
    character.notify('Outfit moved to folder: ' .. (new_folder or 'default'))
end

-- Upgrade outfit to current version
pWardrobe.upgradeOutfit = function(outfit_name, gender, allow_dress)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    -- Update outfit version
    wardrobe[outfit_name].version = tonumber(current_version)
    saveWardrobe(user_id, wardrobe)
    
    -- Reopen wardrobe to refresh
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, character.source, true)
    
    character.notify('Outfit updated to version ' .. current_version .. ' (' .. gender .. ')')
    character.log('ACTION', 'Upgraded outfit "' .. outfit_name .. '" to version ' .. current_version)
end
