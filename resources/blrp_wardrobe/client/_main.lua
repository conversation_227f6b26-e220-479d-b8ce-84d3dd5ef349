-- Wardrobe Client Main
tWardrobe = {}
T.bindInstance('wardrobe', tWardrobe)

pWardrobe = P.getInstance('blrp_wardrobe', 'wardrobe')

local isWardrobeOpen = false
local currentWardrobeData = {}

-- Initialize wardrobe
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if is<PERSON>ardrobe<PERSON><PERSON> then
            DisableControlAction(0, 1, true) -- LookLeftRight
            DisableControlAction(0, 2, true) -- LookUpDown
            DisableControlAction(0, 142, true) -- MeleeAttackAlternate
            DisableControlAction(0, 18, true) -- Enter
            DisableControlAction(0, 322, true) -- ESC
            DisableControlAction(0, 106, true) -- VehicleMouseControlOverride
        end
    end
end)

-- Open wardrobe interface
tWardrobe.openWardrobe = function(current_version, allow_dress, allow_undress, outfits, force_user_id, folders)
    -- Allow refreshing if wardrobe is already open
    
    force_user_id = force_user_id or false
    
    -- Sort outfits by favorite first, then alphabetically
    table.sort(outfits, function(a, b)
        local a_fav = a.favorite or false
        local b_fav = b.favorite or false
        
        if a_fav ~= b_fav then
            return a_fav and (not b_fav)
        end
        
        return string.lower(a.name) < string.lower(b.name)
    end)
    
    -- Prepare wardrobe data
    currentWardrobeData = {
        current_version = current_version,
        allow_dress = allow_dress,
        allow_undress = allow_undress,
        outfits = outfits,
        folders = folders or {},
        force_user_id = force_user_id,
        user_identifier = force_user_id or exports.blrp_core:me().get('identifier')
    }
    
    -- Add folders and timestamps to outfits if they don't exist
    for i, outfit in ipairs(currentWardrobeData.outfits) do
        if not outfit.folder then
            outfit.folder = 'default'
        end
        if not outfit.created_at then
            outfit.created_at = os.time()
        end
        if not outfit.id then
            outfit.id = i
        end
    end
    
    -- Only set focus if not already open (for refreshes)
    if not isWardrobeOpen then
        isWardrobeOpen = true
        SetNuiFocus(true, true)
    end
    
    SendNUIMessage({
        action = 'openWardrobe',
        data = currentWardrobeData
    })
    
    -- Start proximity monitoring for sharing
    tWardrobe.startProximityMonitor()
end

-- Close wardrobe interface
tWardrobe.closeWardrobe = function()
    if not isWardrobeOpen then
        return
    end
    
    isWardrobeOpen = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        action = 'closeWardrobe'
    })
    
    currentWardrobeData = {}
end

-- Start proximity monitoring for outfit sharing
tWardrobe.startProximityMonitor = function()
    Citizen.CreateThread(function()
        while isWardrobeOpen do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local nearbyPlayers = {}
            
            for _, player in ipairs(GetActivePlayers()) do
                if player ~= PlayerId() then
                    local targetPed = GetPlayerPed(player)
                    local targetCoords = GetEntityCoords(targetPed)
                    local distance = #(playerCoords - targetCoords)
                    
                    if distance <= 5.0 then
                        table.insert(nearbyPlayers, {
                            id = GetPlayerServerId(player),
                            name = GetPlayerName(player),
                            distance = distance
                        })
                    end
                end
            end
            
            SendNUIMessage({
                action = 'updateNearbyPlayers',
                data = nearbyPlayers
            })
            
            Citizen.Wait(1000)
        end
    end)
end

-- NUI Callbacks
RegisterNUICallback('closeWardrobe', function(data, cb)
    tWardrobe.closeWardrobe()
    cb('ok')
end)

RegisterNUICallback('saveOutfit', function(data, cb)
    local outfitName = data.name
    local folderId = data.folder_id -- Use folder_id instead of folder
    local saveHair = data.saveHair or false

    if not outfitName or outfitName == '' or string.len(outfitName) > 25 then
        exports.blrp_core:me().notify('Invalid outfit name')
        cb('error')
        return
    end

    -- Convert empty string to nil for unassigned
    if folderId == "" then
        folderId = nil
    end

    pWardrobe.saveOutfit({
        outfitName,
        currentWardrobeData.allow_dress,
        currentWardrobeData.force_user_id,
        false,
        saveHair,
        folderId
    })

    cb('ok')
end)

RegisterNUICallback('applyOutfit', function(data, cb)
    local outfitName = data.name
    
    pWardrobe.setCustomization({
        outfitName,
        currentWardrobeData.force_user_id,
        GetEntityModel(PlayerPedId())
    })
    
    cb('ok')
end)

RegisterNUICallback('toggleFavorite', function(data, cb)
    local outfitName = data.name
    
    pWardrobe.toggleFavorite({
        outfitName,
        currentWardrobeData.allow_dress,
        currentWardrobeData.force_user_id
    })
    
    cb('ok')
end)

RegisterNUICallback('renameOutfit', function(data, cb)
    local oldName = data.oldName
    local newName = data.newName
    
    if not newName or newName == '' or string.len(newName) > 25 then
        exports.blrp_core:me().notify('Invalid outfit name')
        cb('error')
        return
    end
    
    pWardrobe.renameOutfit({
        oldName,
        newName,
        currentWardrobeData.allow_dress,
        currentWardrobeData.force_user_id
    })
    
    cb('ok')
end)

RegisterNUICallback('deleteOutfit', function(data, cb)
    local outfitName = data.name
    
    pWardrobe.deleteOutfit({
        outfitName,
        currentWardrobeData.allow_dress,
        currentWardrobeData.force_user_id
    })
    
    cb('ok')
end)

RegisterNUICallback('shareOutfit', function(data, cb)
    local outfitName = data.name
    local targetPlayerId = data.targetPlayerId
    
    pWardrobe.shareOutfit({
        outfitName,
        targetPlayerId,
        currentWardrobeData.force_user_id
    })
    
    cb('ok')
end)

RegisterNUICallback('updateOutfitFolder', function(data, cb)
    local outfitName = data.name
    local newFolder = data.folder
    
    pWardrobe.updateOutfitFolder({
        outfitName,
        newFolder,
        currentWardrobeData.force_user_id
    })
    
    cb('ok')
end)

RegisterNUICallback('upgradeOutfit', function(data, cb)
    local outfitName = data.name
    local gender = data.gender
    
    pWardrobe.upgradeOutfit({
        outfitName,
        gender,
        currentWardrobeData.allow_dress
    })
    
    cb('ok')
end)

-- Create folder callback
RegisterNUICallback('createFolder', function(data, cb)
    pWardrobe.createFolder({data.name, data.color, currentWardrobeData.force_user_id})
    cb('ok')
end)

-- Update folder callback
RegisterNUICallback('updateFolder', function(data, cb)
    pWardrobe.updateFolder({data.folder_id, data.folder_name, data.color, currentWardrobeData.force_user_id})
    cb('ok')
end)

-- Delete folder callback
RegisterNUICallback('deleteFolder', function(data, cb)
    pWardrobe.deleteFolder({data.folder_id, currentWardrobeData.force_user_id})
    cb('ok')
end)

-- Move outfit to folder callback
RegisterNUICallback('moveOutfitToFolder', function(data, cb)
    pWardrobe.moveOutfitToFolder({data.outfit_name, data.folder_id, currentWardrobeData.force_user_id})
    cb('ok')
end)

-- Client event to open wardrobe
RegisterNetEvent('blrp_wardrobe:client:openWardrobe', function(current_version, allow_dress, allow_undress, outfits, force_user_id, folders)
    tWardrobe.openWardrobe(current_version, allow_dress, allow_undress, outfits, force_user_id, folders)
end)

-- Export functions
exports('IsWardrobeOpen', function()
    return isWardrobeOpen
end)

exports('OpenWardrobe', function(current_version, allow_dress, allow_undress, outfits, force_user_id, folders)
    tWardrobe.openWardrobe(current_version, allow_dress, allow_undress, outfits, force_user_id, folders)
end)

-- Handle ESC key
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isWardrobeOpen and IsControlJustPressed(0, 322) then -- ESC
            tWardrobe.closeWardrobe()
        end
    end
end)
