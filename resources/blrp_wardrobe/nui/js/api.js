// API communication functions
const WardrobeAPI = {
    // Close wardrobe
    closeWardrobe() {
        return fetch('https://blrp_wardrobe/closeWardrobe', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    },

    // Save outfit
    saveOutfit(name, folderId, saveHair) {
        return fetch('https://blrp_wardrobe/saveOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                name: name,
                folder_id: folderId || null,
                saveHair: saveHair
            })
        });
    },

    // Apply outfit
    applyOutfit(outfitName) {
        return fetch('https://blrp_wardrobe/applyOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: outfitName })
        });
    },

    // Toggle favorite
    toggleFavorite(outfitName) {
        return fetch('https://blrp_wardrobe/toggleFavorite', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: outfitName })
        });
    },

    // Rename outfit
    renameOutfit(oldName, newName) {
        return fetch('https://blrp_wardrobe/renameOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ oldName: oldName, newName: newName })
        });
    },

    // Delete outfit
    deleteOutfit(outfitName) {
        return fetch('https://blrp_wardrobe/deleteOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: outfitName })
        });
    },

    // Share outfit
    shareOutfit(outfitName, targetPlayerId) {
        return fetch('https://blrp_wardrobe/shareOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                name: outfitName, 
                targetPlayerId: targetPlayerId 
            })
        });
    },

    // Upgrade outfit
    upgradeOutfit(outfitName, gender) {
        return fetch('https://blrp_wardrobe/upgradeOutfit', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                name: outfitName, 
                gender: gender 
            })
        });
    },

    // Create folder
    createFolder(name, color) {
        return fetch('https://blrp_wardrobe/createFolder', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                name: name,
                color: color || WardrobeConfig.defaults.folderColor
            })
        });
    },

    // Update folder
    updateFolder(folderId, name, color) {
        return fetch('https://blrp_wardrobe/updateFolder', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                folder_id: folderId,
                folder_name: name,
                color: color
            })
        });
    },

    // Delete folder
    deleteFolder(folderId) {
        return fetch('https://blrp_wardrobe/deleteFolder', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ folder_id: folderId })
        });
    },

    // Move outfit to folder
    moveOutfitToFolder(outfitName, folderId) {
        return fetch('https://blrp_wardrobe/moveOutfitToFolder', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                outfit_name: outfitName,
                folder_id: folderId
            })
        });
    },

    // Update nearby players
    updateNearbyPlayers() {
        return fetch('https://blrp_wardrobe/updateNearbyPlayers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
};
