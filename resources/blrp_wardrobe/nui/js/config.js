// Configuration and global variables
const WardrobeConfig = {
    animations: {
        fadeIn: 200,
        fadeOut: 150,
        modalFade: 150
    },
    
    defaults: {
        folderColor: '#5865f2',
        sortBy: 'name',
        selectedFolder: 'all'
    },

    colors: [
        { name: 'Blue', value: '#5865f2' },
        { name: '<PERSON>', value: '#3ba55d' },
        { name: 'Red', value: '#ed4245' },
        { name: 'Orange', value: '#faa61a' },
        { name: 'Purple', value: '#9333ea' },
        { name: 'Pink', value: '#ec4899' },
        { name: '<PERSON><PERSON>', value: '#06b6d4' },
        { name: 'Indigo', value: '#6366f1' },
        { name: 'Yellow', value: '#eab308' },
        { name: 'Emerald', value: '#10b981' },
        { name: '<PERSON>', value: '#f43f5e' },
        { name: 'Violet', value: '#8b5cf6' }
    ],
    
    limits: {
        outfitNameLength: 25,
        folderNameLength: 50
    }
};

// Global state
let wardrobeData = {};
let nearbyPlayers = [];
let currentOutfits = [];
let currentFolders = [];
let searchQuery = '';
let sortBy = WardrobeConfig.defaults.sortBy;
let selectedFolder = WardrobeConfig.defaults.selectedFolder;
