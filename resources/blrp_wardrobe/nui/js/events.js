// Event handlers and initialization
const WardrobeEvents = {
    // Initialize all event handlers
    initialize() {
        this.bindGlobalEvents();
        this.bindToolbarEvents();
        this.bindModalEvents();
        this.bindNUICallbacks();
    },

    // Bind global events
    bindGlobalEvents() {
        // ESC key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                e.preventDefault();
                e.stopPropagation();
                
                if ($('#save-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideSaveModal();
                } else if ($('#new-folder-modal').is(':visible')) {
                    WardrobeFolders.hideCreateModal();
                } else if ($('#edit-folder-modal').is(':visible')) {
                    WardrobeFolders.hideEditModal();
                } else if ($('#rename-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideRenameModal();
                } else if ($('#delete-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideDeleteModal();
                } else if ($('#wardrobe-container').is(':visible')) {
                    WardrobeUI.close();
                }
            }
        });
        
        // Prevent modal overlay clicks from affecting main wardrobe
        $(document).on('click', '.modal-overlay', function(e) {
            if (e.target === this) {
                e.preventDefault();
                e.stopPropagation();
                
                if ($(this).attr('id') === 'save-outfit-modal') {
                    WardrobeOutfits.hideSaveModal();
                } else if ($(this).attr('id') === 'new-folder-modal') {
                    WardrobeFolders.hideCreateModal();
                } else if ($(this).attr('id') === 'edit-folder-modal') {
                    WardrobeFolders.hideEditModal();
                } else if ($(this).attr('id') === 'rename-outfit-modal') {
                    WardrobeOutfits.hideRenameModal();
                } else if ($(this).attr('id') === 'delete-outfit-modal') {
                    WardrobeOutfits.hideDeleteModal();
                }
            }
        });
        
        // Prevent modal content clicks from bubbling
        $(document).on('click', '.modal-content', function(e) {
            e.stopPropagation();
        });

        // NUI message handler
        window.addEventListener('message', function(event) {
            const data = event.data;

            switch (data.action) {
                case 'openWardrobe':
                    WardrobeUI.show(data.data);
                    break;
                case 'updateNearbyPlayers':
                    nearbyPlayers = data.players || [];
                    break;
                case 'closeWardrobe':
                    WardrobeUI.close();
                    break;
                case 'refreshWardrobe':
                    // Handle refresh without showing/hiding the interface
                    if ($('#wardrobe-container').is(':visible')) {
                        wardrobeData = data.data;
                        currentOutfits = data.data.outfits || [];
                        currentFolders = data.data.folders || [];
                        WardrobeFolders.updateFolderOptions();
                        WardrobeOutfits.render();
                    }
                    break;
            }
        });
    },

    // Bind toolbar events
    bindToolbarEvents() {
        // Close wardrobe
        $(document).on('click', '#close-wardrobe', function() {
            WardrobeUI.close();
        });

        // Search input
        $(document).on('input', '#search-input', function() {
            searchQuery = $(this).val();
            WardrobeOutfits.render();
        });

        // Sort select
        $(document).on('change', '#sort-select', function() {
            sortBy = $(this).val();
            WardrobeOutfits.render();
        });

        // Save outfit button
        $(document).on('click', '#save-outfit-btn', function() {
            WardrobeOutfits.showSaveModal();
        });

        // New folder button
        $(document).on('click', '#new-folder-btn', function() {
            WardrobeFolders.showCreateModal();
        });
    },

    // Bind modal events
    bindModalEvents() {
        // Save outfit modal
        $(document).on('click', '#confirm-save', function() {
            WardrobeOutfits.save();
        });

        $(document).on('click', '#cancel-save, #cancel-save-alt', function() {
            WardrobeOutfits.hideSaveModal();
        });

        // Save hair checkbox
        $(document).on('change', '#save-hair-checkbox', function() {
            if ($(this).is(':checked')) {
                $('#save-hair-info').slideDown(200);
            } else {
                $('#save-hair-info').slideUp(200);
            }
        });

        // New folder modal
        $(document).on('click', '#confirm-folder', function() {
            WardrobeFolders.create();
        });

        $(document).on('click', '#cancel-folder, #cancel-folder-alt', function() {
            WardrobeFolders.hideCreateModal();
        });

        // Edit folder modal
        $(document).on('click', '#confirm-edit-folder', function() {
            WardrobeFolders.update();
        });

        $(document).on('click', '#cancel-edit-folder, #cancel-edit-folder-alt', function() {
            WardrobeFolders.hideEditModal();
        });

        // Color picker events
        $(document).on('click', '.color-option', function() {
            const color = $(this).data('color');
            const inputId = $(this).data('input');

            // Update hidden input
            $(`#${inputId}`).val(color);

            // Update visual selection
            $(this).siblings('.color-option').removeClass('selected');
            $(this).addClass('selected');
        });

        // Rename outfit modal
        $(document).on('click', '#confirm-rename', function() {
            WardrobeOutfits.rename();
        });

        $(document).on('click', '#cancel-rename, #cancel-rename-alt', function() {
            WardrobeOutfits.hideRenameModal();
        });

        // Delete outfit modal
        $(document).on('click', '#confirm-delete', function() {
            WardrobeOutfits.deleteOutfit();
        });

        $(document).on('click', '#cancel-delete, #cancel-delete-alt', function() {
            WardrobeOutfits.hideDeleteModal();
        });

        // Enter key in inputs
        $(document).on('keypress', '#outfit-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeOutfits.save();
            }
        });

        $(document).on('keypress', '#folder-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeFolders.create();
            }
        });

        $(document).on('keypress', '#edit-folder-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeFolders.update();
            }
        });

        $(document).on('keypress', '#rename-outfit-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeOutfits.rename();
            }
        });
    },

    // Bind NUI callbacks
    bindNUICallbacks() {
        // Register NUI callbacks for server communication
        const callbacks = {
            closeWardrobe: () => ({ status: 'ok' }),
            saveOutfit: (data) => ({ status: 'ok' }),
            applyOutfit: (data) => ({ status: 'ok' }),
            toggleFavorite: (data) => ({ status: 'ok' }),
            renameOutfit: (data) => ({ status: 'ok' }),
            deleteOutfit: (data) => ({ status: 'ok' }),
            shareOutfit: (data) => ({ status: 'ok' }),
            upgradeOutfit: (data) => ({ status: 'ok' }),
            createFolder: (data) => ({ status: 'ok' }),
            updateFolder: (data) => ({ status: 'ok' }),
            deleteFolder: (data) => ({ status: 'ok' }),
            moveOutfitToFolder: (data) => ({ status: 'ok' }),
            updateNearbyPlayers: (data) => ({ status: 'ok' })
        };

        // Note: NUI callbacks are handled by the game engine
        // This is just for reference of what callbacks are available
    }
};

// Initialize everything when document is ready
$(document).ready(function() {
    // Initialize UI
    WardrobeUI.initialize();
    
    // Initialize events
    WardrobeEvents.initialize();
    
    // Update nearby players on load
    WardrobeAPI.updateNearbyPlayers();
});
