tCircuitHack = {}
T.bindInstance('circuithack', tCircuitHack)

local running = false
local _promise = nil

HasCircuitFailed = false
local hasCircuitCompleted = false
local gameBounds = {
    vec2(0.159, 0.153), -- Top Left
    vec2(0.159, 0.848), -- Bottom Left
    vec2(0.841, 0.848), -- Bottom Right
    vec2(0.841, 0.153) -- Top Right
}
local textureDictionaries = {'MPCircuitHack', 'MPCircuitHack2', 'MPCircuitHack3'}
local gameEndTime
local gameStartTime
local delayedStartTime
local initCursorSpeed
local _cursorSpeed
local illegalAreas
local genericPorts
local _cursor
local _scaleform
local _currentDifficulty
local _currentLevelNumber
local _isEndScreenActive
local backgroundSoundId
local trailSoundId
local startingHealth
local debugPortHeading = 0
local remainingFails = 2

local defaultDelayStartTimeMs <const> = 1000
local minDelayEndGameTimeMs <const> = 5000
local maxDelayEndGameTimeMs <const> = 5000
local minCursorSpeed <const> = 0.002
local maxCursorSpeed <const> = 0.002
local debuggingMapPosition = false

local winOrLoss = false

--#endregion

--#region Utility Functions

local function drawMapSprite(currentMap)
    local levelTextureDict = currentMap > 3 and 'MPCircuitHack3' or 'MPCircuitHack2'
    DrawSprite(levelTextureDict, ('CBLevel%s'):format(currentMap), 0.5, 0.5, 1, 1, 0, 255, 255, 255, 255)
end

local function getCursorSpeedFromDifficulty(currentDifficulty)
    if currentDifficulty == Difficulty.Beginner then return 0.002
    elseif currentDifficulty == Difficulty.Easy then return 0.002
    elseif currentDifficulty == Difficulty.Medium then return 0.002
    elseif currentDifficulty == Difficulty.Hard then return 0.002 end
    return 0.00085
end

local function isInPolygon(pos, polygon)
    local x, y = pos.x, pos.y
    local inside = false
    local j = #polygon
    for i = 1, #polygon do
        local xi, yi = polygon[i].x, polygon[i].y
        local xj, yj = polygon[j].x, polygon[j].y
        if ((yi > y) ~= (yj > y)) and
           (x < (xj - xi) * (y - yi) / (yj - yi + 0.00001) + xi) then
            inside = not inside
        end
        j = i
    end
    return inside
end

local function isCursorOutOfBounds(position)
    return not isInPolygon(position, gameBounds)
end

local function isCursorInIllegalArea(position)
    for _, area in ipairs(illegalAreas) do
        if isInPolygon(position, area) then return true end
    end
    return false
end

local function disposeScaleform()
    if not _scaleform then return end
    SetScaleformMovieAsNoLongerNeeded(_scaleform)
    _scaleform = nil
    Wait(50)
end

local function disposeSounds()
    StopSound(trailSoundId)
    StopSound(backgroundSoundId)
    if backgroundSoundId > 0 then ReleaseSoundId(backgroundSoundId) end
    if trailSoundId > 0 then ReleaseSoundId(trailSoundId) end
    ReleaseScriptAudioBank('DLC_MPHEIST/HEIST_HACK_SNAKE')
end

local function dispose()
    disposeSounds()
    disposeScaleform()
end

local function endGame()
    dispose()
end

local function setScaleform()
    disposeScaleform()
    _scaleform = RequestScaleformMovie('HACKING_MESSAGE')
    local loadAttempt = 0
    while not HasScaleformMovieLoaded(_scaleform) and loadAttempt <= 50 do
        Wait(5)
        loadAttempt += 1
    end
end

local function showDisplayScaleform(title, msg, r, g, b, stagePassed)
    if not _scaleform then return end
    BeginScaleformMovieMethod(_scaleform, 'SET_DISPLAY')
    ScaleformMovieMethodAddParamInt(0)
    ScaleformMovieMethodAddParamTextureNameString(title)
    ScaleformMovieMethodAddParamTextureNameString(msg)
    ScaleformMovieMethodAddParamInt(r)
    ScaleformMovieMethodAddParamInt(g)
    ScaleformMovieMethodAddParamInt(b)
    ScaleformMovieMethodAddParamBool(stagePassed)
    EndScaleformMovieMethod()
end

local function initializeResources()
    for i = 1, #textureDictionaries do
        RequestStreamedTextureDict(textureDictionaries[i], false)
    end
    RequestScriptAudioBank('DLC_MPHEIST/HEIST_HACK_SNAKE', false)

    local timeout = GetGameTimer() + 5000
    while GetGameTimer() < timeout do
        local allLoaded = true
        for i = 1, #textureDictionaries do
            if not HasStreamedTextureDictLoaded(textureDictionaries[i]) then
                allLoaded = false
            end
        end
        if allLoaded then break end
        Wait(100)
    end

    setScaleform()
    backgroundSoundId = GetSoundId()
    trailSoundId = GetSoundId()
end

local function playStartSound(delayMs)
    CreateThread(function()
        Wait(delayMs)
        PlaySoundFrontend(-1, 'Start', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
        PlaySoundFrontend(trailSoundId, 'Trail_Custom', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
    end)
end

local function initializeCursorSpeed(cursorSpeed)
    initCursorSpeed = cursorSpeed
    _cursorSpeed = cursorSpeed
end

local function initializeLevelVariables(levelNumber, difficultyLevel, cursorSpeed, delayStartMs)
    HasCircuitFailed = false
    hasCircuitCompleted = false
    _isEndScreenActive = false
    _currentLevelNumber = levelNumber
    illegalAreas = GetBoxBounds(levelNumber)
    genericPorts = newGeneric(levelNumber)
    _cursor = newCursor(genericPorts)
    initializeCursorSpeed(cursorSpeed)
    _currentDifficulty = difficultyLevel
    gameStartTime = GetGameTimer()
    delayedStartTime = gameStartTime + delayStartMs
    startingHealth = GetEntityHealth(PlayerPedId())
    remainingFails = 2
end

local function showFailureScreenAndPlaySound()
    PlaySoundFrontend(-1, 'Crash', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
    StopSound(trailSoundId)
    showDisplayScaleform('CIRCUIT FAILED', 'Security Tunnel Detected', 188, 49, 43, false)
end

local function showSuccessScreenAndPlaySound()
    PlaySoundFrontend(-1, 'Goal', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
    StopSound(trailSoundId)
    showDisplayScaleform('CIRCUIT COMPLETE', 'Decryption Execution x86 Tunneling', 45, 203, 134, true)
end

local function runMinigameTask(levelNumber, difficultyLevel, cursorSpeed, delayStartMs)
    if not NetworkIsSessionStarted() then
        Wait(1000)
        endGame()
        return GameStatus.FailedToStart
    end

    initializeResources()
    PlaySoundFrontend(backgroundSoundId, 'Background', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
    playStartSound(delayStartMs)
    initializeLevelVariables(levelNumber, difficultyLevel, cursorSpeed, delayStartMs)

    while true do
        DisableAllControlActions(0)

        if IsEntityDead(PlayerPedId()) then
            endGame()
            return GameStatus.PlayerDied
        end

        if IsDisabledControlPressed(0, 44) and not HasCircuitFailed then
            endGame()
            return GameStatus.PlayerQuit
        end

        drawMapSprite(_currentLevelNumber)
        genericPorts:DrawPorts()
        _cursor:DrawCursor()
        _cursor:DrawTailHistory()
        DrawScaleformMovieFullscreen(_scaleform, 255, 255, 255, 255, 0)

        -- Check for win condition
        if not _isEndScreenActive and genericPorts:IsCursorInGameWinningPosition(_cursor.position) then
            hasCircuitCompleted = true
            gameEndTime = GetGameTimer() + minDelayEndGameTimeMs
            showSuccessScreenAndPlaySound()
            _isEndScreenActive = true
            winOrLoss = true
        end
        
        -- Check for fail condition (including out-of-bounds)
        if not _isEndScreenActive and _cursor.isAlive then
            local failed = false
        
            if genericPorts:IsCollisionWithPort(_cursor.position) then
                failed = true
            elseif _cursor:CheckTailCollision() then
                failed = true
            elseif isCursorInIllegalArea(_cursor.position) then
                failed = true
            elseif isCursorOutOfBounds(_cursor.position) then
                failed = true
            end
        
            if failed then
                HasCircuitFailed = true
                _cursor.isAlive = false
                _cursor:StartCursorDeathAnimation()
            end
        end
        
        -- Retry logic after failure
        if not _isEndScreenActive and not _cursor.isVisible and HasCircuitFailed then
            remainingFails -= 1
            if remainingFails > 0 then
                _cursor = newCursor(genericPorts)
                _cursor:MoveCursor(_cursorSpeed)
                HasCircuitFailed = false
                goto continue
            else
                showFailureScreenAndPlaySound()
                gameEndTime = GetGameTimer() + maxDelayEndGameTimeMs
                _isEndScreenActive = true
                winOrLoss = false
            end
        end

        ::continue::

        if GetGameTimer() > delayedStartTime and not _isEndScreenActive and _cursor.isAlive then
            _cursor:GetCursorInputFromPlayer()
            _cursor:MoveCursor(_cursorSpeed)
        end

        if _isEndScreenActive and (HasCircuitFailed or hasCircuitCompleted) and GetGameTimer() > gameEndTime then
            StopSound(backgroundSoundId)
            if hasCircuitCompleted then
                PlaySoundFrontend(-1, 'Success', 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true)
            end
            endGame()
            return hasCircuitCompleted and GameStatus.Success or GameStatus.Failure
        end

        Wait(0)
    end
end

local function runMiniGame(levelNumber, difficultyLevel, delayStartMs)
    levelNumber = math.clamp(levelNumber, 1, 6)
    difficultyLevel = math.clamp(difficultyLevel, 0, 3)
    local cursorSpeed = getCursorSpeedFromDifficulty(difficultyLevel)
    delayStartMs = math.clamp(delayStartMs, 1000, 60000)
    local gameStatus = runMinigameTask(levelNumber, difficultyLevel, cursorSpeed, delayStartMs)
    return gameStatus == GameStatus.Success
end

function runDefaultMiniGame()
    if running then return false end

    running = true
    _promise = promise:new()

    CreateThread(function()
        local levelNumber = math.random(1, 3)
        local difficultyLevel = 0
        local delayStartMs = defaultDelayStartTimeMs

        local success = runMiniGame(levelNumber, difficultyLevel, delayStartMs)

        _promise:resolve(success)
        running = false
    end)

    return Citizen.Await(_promise)
end

tCircuitHack.runDefaultMiniGame = runDefaultMiniGame

tCircuitHack.hack = function(level, difficulty, delay)
    return runMiniGame(level or 1, difficulty or 0, delay or 1000)
end

exports('runMiniGame', runMiniGame)
exports('runDefaultRandom', runDefaultMiniGame)