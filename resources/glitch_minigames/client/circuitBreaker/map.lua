-- Glitch Minigames
-- Copyright (C) 2024 Glitch
-- 
-- This program is free software: you can redistribute it and/or modify
-- it under the terms of the GNU General Public License as published by
-- the Free Software Foundation, either version 3 of the License, or
-- (at your option) any later version.
-- 
-- This program is distributed in the hope that it will be useful,
-- but WITHOUT ANY WARRANTY; without even the implied warranty of
-- MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
-- GNU General Public License for more details.
-- 
-- You should have received a copy of the GNU General Public License
-- along with this program. If not, see <https://www.gnu.org/licenses/>.

---Out of bounds areas for each map
---@param mapNumber integer
---@return vector2[][]
function GetBoxBounds(mapNumber)
    if mapNumber == 1 then
        return {
            {
                vec2(0.18, 0.155),
                vec2(0.18, 0.583),
                vec2(0.307, 0.583),
                vec2(0.307, 0.154)
            },
            {
                vec2(0.321, 0.154),
                vec2(0.321, 0.477),
                vec2(0.382, 0.477),
                vec2(0.382, 0.154)
            },
            {
                vec2(0.396, 0.154),
                vec2(0.396, 0.379),
                vec2(0.429, 0.379),
                vec2(0.429, 0.155)
            },
            {
                vec2(0.443, 0.155),
                vec2(0.443, 0.378),
                vec2(0.477, 0.378),
                vec2(0.477, 0.154)
            },
            {
                vec2(0.491, 0.154),
                vec2(0.491, 0.379),
                vec2(0.525, 0.379),
                vec2(0.525, 0.155)
            },
            {
                vec2(0.538, 0.155),
                vec2(0.538, 0.308),
                vec2(0.585, 0.308),
                vec2(0.585, 0.155)
            },
            {
                vec2(0.597, 0.155),
                vec2(0.597, 0.308),
                vec2(0.645, 0.308),
                vec2(0.645, 0.155)
            },
            {
                vec2(0.66, 0.155),
                vec2(0.66, 0.255),
                vec2(0.73, 0.255),
                vec2(0.73, 0.154)
            },
            {
                vec2(0.692, 0.311),
                vec2(0.692, 0.373),
                vec2(0.584, 0.376),
                vec2(0.584, 0.452),
                vec2(0.838, 0.452),
                vec2(0.838, 0.31)
            },
            {
                vec2(0.343, 0.544),
                vec2(0.343, 0.639),
                vec2(0.398, 0.639),
                vec2(0.398, 0.544)
            },
            {
                vec2(0.302, 0.7),
                vec2(0.302, 0.846),
                vec2(0.434, 0.846),
                vec2(0.434, 0.7)
            },
            {
                vec2(0.451, 0.435),
                vec2(0.451, 0.847),
                vec2(0.569, 0.847),
                vec2(0.569, 0.436)
            },
            {
                vec2(0.587, 0.477),
                vec2(0.587, 0.846),
                vec2(0.705, 0.846),
                vec2(0.705, 0.477)
            },
            {
                vec2(0.721, 0.477),
                vec2(0.721, 0.846),
                vec2(0.838, 0.846),
                vec2(0.838, 0.475)
            }
        }
    elseif mapNumber == 2 then
        return {
            {
                vec2(0.162, 0.152),
                vec2(0.163, 0.645),
                vec2(0.249, 0.643),
                vec2(0.252, 0.275),
                vec2(0.375, 0.275),
                vec2(0.375, 0.35),
                vec2(0.416, 0.35),
                vec2(0.416, 0.157)
            },
            {
                vec2(0.313, 0.36),
                vec2(0.313, 0.844),
                vec2(0.442, 0.844),
                vec2(0.442, 0.419),
                vec2(0.349, 0.415),
                vec2(0.348, 0.36)
            },
            {
                vec2(0.458, 0.238),
                vec2(0.458, 0.844),
                vec2(0.515, 0.844),
                vec2(0.515, 0.238)
            },
            {
                vec2(0.555, 0.156),
                vec2(0.555, 0.454),
                vec2(0.541, 0.458),
                vec2(0.538, 0.551),
                vec2(0.685, 0.551),
                vec2(0.688, 0.46),
                vec2(0.725, 0.456),
                vec2(0.728, 0.309),
                vec2(0.757, 0.303),
                vec2(0.759, 0.157)
            },
            {
                vec2(0.552, 0.635),
                vec2(0.552, 0.786),
                vec2(0.695, 0.787),
                vec2(0.695, 0.633)
            },
            {
                vec2(0.776, 0.36),
                vec2(0.776, 0.455),
                vec2(0.839, 0.455),
                vec2(0.839, 0.358)
            },
            {
                vec2(0.739, 0.517),
                vec2(0.739, 0.679),
                vec2(0.801, 0.681),
                vec2(0.801, 0.514)
            },
            {
                vec2(0.739, 0.749),
                vec2(0.739, 0.846),
                vec2(0.839, 0.846),
                vec2(0.838, 0.747)
            }
        }
    elseif mapNumber == 3 then
        return {
            {
                vec2(0.299, 0.153),
                vec2(0.299, 0.245),
                vec2(0.372, 0.249),
                vec2(0.375, 0.343),
                vec2(0.465, 0.344),
                vec2(0.465, 0.247),
                vec2(0.448, 0.242),
                vec2(0.446, 0.154)
            },
            {
                vec2(0.163, 0.298),
                vec2(0.163, 0.715),
                vec2(0.328, 0.715),
                vec2(0.331, 0.578),
                vec2(0.499, 0.578),
                vec2(0.502, 0.771),
                vec2(0.567, 0.771),
                vec2(0.568, 0.564),
                vec2(0.649, 0.564),
                vec2(0.649, 0.473),
                vec2(0.574, 0.468),
                vec2(0.572, 0.247),
                vec2(0.501, 0.247),
                vec2(0.501, 0.403),
                vec2(0.329, 0.403),
                vec2(0.328, 0.299)
            },
            {
                vec2(0.365, 0.674),
                vec2(0.365, 0.846),
                vec2(0.436, 0.846),
                vec2(0.436, 0.674)
            },
            {
                vec2(0.615, 0.154),
                vec2(0.615, 0.383),
                vec2(0.839, 0.383),
                vec2(0.839, 0.155)
            },
            {
                vec2(0.698, 0.429),
                vec2(0.698, 0.561),
                vec2(0.839, 0.561),
                vec2(0.839, 0.43)
            },
            {
                vec2(0.613, 0.649),
                vec2(0.613, 0.845),
                vec2(0.839, 0.845),
                vec2(0.839, 0.649)
            }
        }
    elseif mapNumber == 4 then
        return {
            {
                vec2(0.162, 0.154),
                vec2(0.162, 0.593),
                vec2(0.305, 0.595),
                vec2(0.307, 0.654),
                vec2(0.419, 0.658),
                vec2(0.421, 0.78),
                vec2(0.54, 0.78),
                vec2(0.542, 0.658),
                vec2(0.69, 0.653),
                vec2(0.69, 0.559),
                vec2(0.542, 0.552),
                vec2(0.54, 0.489),
                vec2(0.324, 0.484),
                vec2(0.322, 0.154)
            },
            {
                vec2(0.276, 0.728),
                vec2(0.276, 0.846),
                vec2(0.381, 0.846),
                vec2(0.381, 0.73)
            },
            {
                vec2(0.352, 0.22),
                vec2(0.352, 0.298),
                vec2(0.368, 0.302),
                vec2(0.369, 0.434),
                vec2(0.421, 0.434),
                vec2(0.422, 0.41),
                vec2(0.576, 0.41),
                vec2(0.576, 0.478),
                vec2(0.735, 0.48),
                vec2(0.736, 0.715),
                vec2(0.578, 0.718),
                vec2(0.578, 0.847),
                vec2(0.837, 0.847),
                vec2(0.837, 0.397),
                vec2(0.78, 0.397),
                vec2(0.779, 0.427),
                vec2(0.763, 0.427),
                vec2(0.761, 0.374),
                vec2(0.687, 0.369),
                vec2(0.687, 0.23),
                vec2(0.643, 0.23),
                vec2(0.643, 0.371),
                vec2(0.624, 0.371),
                vec2(0.623, 0.315),
                vec2(0.422, 0.313),
                vec2(0.421, 0.22)
            },
            {
                vec2(0.46, 0.154),
                vec2(0.46, 0.263),
                vec2(0.596, 0.261),
                vec2(0.597, 0.154)
            },
            {
                vec2(0.723, 0.154),
                vec2(0.723, 0.262),
                vec2(0.778, 0.262),
                vec2(0.778, 0.155)
            }
        }
    elseif mapNumber == 5 then
        return {
            {
                vec2(0.254, 0.156),
                vec2(0.253, 0.436),
                vec2(0.195, 0.439),
                vec2(0.195, 0.514),
                vec2(0.253, 0.515),
                vec2(0.255, 0.701),
                vec2(0.337, 0.704),
                vec2(0.339, 0.788),
                vec2(0.372, 0.787),
                vec2(0.372, 0.636),
                vec2(0.401, 0.636),
                vec2(0.401, 0.673),
                vec2(0.471, 0.672),
                vec2(0.471, 0.637),
                vec2(0.606, 0.637),
                vec2(0.606, 0.682),
                vec2(0.652, 0.682),
                vec2(0.652, 0.483),
                vec2(0.497, 0.483),
                vec2(0.496, 0.53),
                vec2(0.328, 0.53),
                vec2(0.328, 0.261),
                vec2(0.409, 0.261),
                vec2(0.41, 0.359),
                vec2(0.441, 0.359),
                vec2(0.441, 0.244),
                vec2(0.531, 0.244),
                vec2(0.532, 0.305),
                vec2(0.577, 0.305),
                vec2(0.577, 0.255),
                vec2(0.605, 0.253),
                vec2(0.605, 0.154)
            },
            {
                vec2(0.163, 0.58),
                vec2(0.163, 0.635),
                vec2(0.219, 0.635),
                vec2(0.219, 0.581)
            },
            {
                vec2(0.232, 0.761),
                vec2(0.232, 0.844),
                vec2(0.305, 0.846),
                vec2(0.305, 0.761)
            },
            {
                vec2(0.383, 0.413),
                vec2(0.383, 0.493),
                vec2(0.461, 0.493),
                vec2(0.461, 0.414)
            },
            {
                vec2(0.417, 0.744),
                vec2(0.417, 0.846),
                vec2(0.654, 0.846),
                vec2(0.654, 0.744),
                vec2(0.552, 0.743),
                vec2(0.55, 0.704),
                vec2(0.497, 0.704),
                vec2(0.495, 0.742),
                vec2(0.417, 0.745)
            },
            {
                vec2(0.482, 0.301),
                vec2(0.482, 0.431),
                vec2(0.561, 0.431),
                vec2(0.561, 0.368),
                vec2(0.511, 0.364),
                vec2(0.509, 0.302)
            },
            {
                vec2(0.658, 0.199),
                vec2(0.657, 0.366),
                vec2(0.578, 0.368),
                vec2(0.578, 0.432),
                vec2(0.75, 0.434),
                vec2(0.75, 0.495),
                vec2(0.694, 0.496),
                vec2(0.694, 0.845),
                vec2(0.742, 0.845),
                vec2(0.743, 0.646),
                vec2(0.763, 0.644),
                vec2(0.764, 0.555),
                vec2(0.805, 0.554),
                vec2(0.805, 0.435),
                vec2(0.788, 0.432),
                vec2(0.787, 0.368),
                vec2(0.707, 0.367),
                vec2(0.706, 0.199)
            },
            {
                vec2(0.754, 0.155),
                vec2(0.753, 0.22),
                vec2(0.775, 0.22),
                vec2(0.775, 0.155)
            },
            {
                vec2(0.818, 0.259),
                vec2(0.818, 0.327),
                vec2(0.838, 0.325),
                vec2(0.838, 0.258)
            },
            {
                vec2(0.808, 0.616),
                vec2(0.809, 0.707),
                vec2(0.838, 0.706),
                vec2(0.838, 0.616)
            }
        }
    elseif mapNumber == 6 then
        return {
            {
                vec2(0.232, 0.155),
                vec2(0.232, 0.218),
                vec2(0.254, 0.218),
                vec2(0.254, 0.154)
            },
            {
                vec2(0.225, 0.281),
                vec2(0.224, 0.328),
                vec2(0.162, 0.331),
                vec2(0.162, 0.515),
                vec2(0.214, 0.515),
                vec2(0.214, 0.425),
                vec2(0.247, 0.422),
                vec2(0.247, 0.281)
            },
            {
                vec2(0.163, 0.572),
                vec2(0.163, 0.847),
                vec2(0.273, 0.847),
                vec2(0.273, 0.758),
                vec2(0.205, 0.757),
                vec2(0.205, 0.622),
                vec2(0.216, 0.621),
                vec2(0.216, 0.572)
            },
            {
                vec2(0.24, 0.648),
                vec2(0.24, 0.715),
                vec2(0.261, 0.715),
                vec2(0.261, 0.649)
            },
            {
                vec2(0.301, 0.154),
                vec2(0.3, 0.249),
                vec2(0.284, 0.251),
                vec2(0.284, 0.327),
                vec2(0.3, 0.331),
                vec2(0.3, 0.47),
                vec2(0.251, 0.472),
                vec2(0.251, 0.563),
                vec2(0.299, 0.563),
                vec2(0.3, 0.537),
                vec2(0.324, 0.539),
                vec2(0.324, 0.603),
                vec2(0.298, 0.605),
                vec2(0.298, 0.697),
                vec2(0.324, 0.7),
                vec2(0.325, 0.806),
                vec2(0.499, 0.806),
                vec2(0.499, 0.758),
                vec2(0.377, 0.755),
                vec2(0.377, 0.598),
                vec2(0.425, 0.596),
                vec2(0.425, 0.543),
                vec2(0.377, 0.541),
                vec2(0.375, 0.458),
                vec2(0.354, 0.455),
                vec2(0.354, 0.253),
                vec2(0.392, 0.25),
                vec2(0.392, 0.155)
            },
            {
                vec2(0.375, 0.339),
                vec2(0.375, 0.407),
                vec2(0.396, 0.407),
                vec2(0.396, 0.339)
            },
            {
                vec2(0.453, 0.154),
                vec2(0.453, 0.225),
                vec2(0.474, 0.223),
                vec2(0.474, 0.155)
            },
            {
                vec2(0.454, 0.282),
                vec2(0.452, 0.341),
                vec2(0.425, 0.344),
                vec2(0.425, 0.423),
                vec2(0.599, 0.426),
                vec2(0.599, 0.511),
                vec2(0.525, 0.514),
                vec2(0.524, 0.65),
                vec2(0.422, 0.653),
                vec2(0.422, 0.71),
                vec2(0.536, 0.713),
                vec2(0.537, 0.846),
                vec2(0.838, 0.846),
                vec2(0.838, 0.747),
                vec2(0.755, 0.746),
                vec2(0.754, 0.696),
                vec2(0.647, 0.695),
                vec2(0.646, 0.745),
                vec2(0.591, 0.745),
                vec2(0.59, 0.653),
                vec2(0.57, 0.65),
                vec2(0.57, 0.598),
                vec2(0.651, 0.596),
                vec2(0.653, 0.342),
                vec2(0.666, 0.34),
                vec2(0.665, 0.216),
                vec2(0.629, 0.216),
                vec2(0.628, 0.342),
                vec2(0.478, 0.342),
                vec2(0.477, 0.282)
            },
            {
                vec2(0.464, 0.477),
                vec2(0.464, 0.616),
                vec2(0.485, 0.615),
                vec2(0.485, 0.477)
            },
            {
                vec2(0.51, 0.164),
                vec2(0.51, 0.286),
                vec2(0.589, 0.286),
                vec2(0.589, 0.165)
            },
            {
                vec2(0.698, 0.155),
                vec2(0.697, 0.577),
                vec2(0.681, 0.58),
                vec2(0.681, 0.629),
                vec2(0.747, 0.627),
                vec2(0.749, 0.559),
                vec2(0.796, 0.556),
                vec2(0.797, 0.458),
                vec2(0.749, 0.456),
                vec2(0.749, 0.154)
            },
            {
                vec2(0.779, 0.319),
                vec2(0.779, 0.402),
                vec2(0.838, 0.401),
                vec2(0.838, 0.319)
            },
            {
                vec2(0.784, 0.615),
                vec2(0.784, 0.696),
                vec2(0.837, 0.695),
                vec2(0.837, 0.615)
            }
        }
    end

    return {}
end