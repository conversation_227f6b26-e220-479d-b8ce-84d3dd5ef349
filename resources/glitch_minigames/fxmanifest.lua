fx_version 'cerulean'
game 'gta5'

author '<PERSON><PERSON> in collaboration with Glitch Studios'
description 'Glitch Minigames'
version '1.0.4'

client_scripts {
    '@blrp_rpc/tunnel/client.lua',

    'client/circuitBreaker/init.lua',
    'client/circuitBreaker/globals.lua',
    'client/circuitBreaker/map.lua',
    'client/circuitBreaker/portlights.lua',
    'client/circuitBreaker/helper.lua',
    'client/circuitBreaker/generic.lua',
    'client/circuitBreaker/cursor.lua',
    'client/circuitBreaker/circuit.lua',
}

ui_page 'ui/index.html'

files {
    'client/circuitBreaker/class.lua',
    'ui/index.html',
    'ui/css/style.css',
    'ui/js/app.js',
    'ui/js/rhythm.js',
    'ui/js/keymash.js',
    'ui/js/varHack.js',
    'ui/sounds/*.mp3',
    'ui/sounds/songs/*.mp3',
}

lua54 'yes'
use_experimental_fxv2_oal 'yes'
