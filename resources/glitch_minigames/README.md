# Glitch Minigames

Welcome to the official repository for **Glitch Minigames**! This is a collection of unique and engaging minigames designed for FiveM servers. Each minigame can be easily triggered through simple exports, making integration seamless and straightforward.

## Features
- **Unique Gameplay**: Every minigame offers a distinct experience.
- **Easy Integration**: Trigger minigames with simple exports.
- **Optimized for FiveM**: Designed to run smoothly on any FiveM server.
- **Fully Customizable**: Adjust difficulty parameters to suit your server's needs.
- **Audio Feedback**: Includes sound effects for interactions, success, and failure.

--- 

## 📚 Documentation

https://glitchstudios.gitbook.io/glitch-studios/free-resources/glitch-minigames

---

## Special Thanks
### A Heartfelt Thank You

I would like to extend my deepest gratitude to the following individuals and projects for their incredible contributions to the FiveM community. Your hard work, creativity, and dedication have inspired and enabled countless developers, including myself, to create engaging and innovative resources:

- [utkuali](https://github.com/utkuali/datacrack) for the **Data Crack Minigame**, which served as a foundation for one of the games in this resource.
- [BerkieBb](https://github.com/BerkieBb/CircuitBreakerMinigame_lua) and [Timothy<PERSON>ex<PERSON>](https://github.com/TimothyDexter/FiveM-CircuitBreakerMinigame) for the **Circuit Breaker Minigame**, which provided invaluable insights into precision-based gameplay mechanics.
- [TransitNode](https://github.com/TransitNode/Hacking_PC/tree/master) for their innovative hacking minigame concepts, which inspired several features in this resource.
- [MxttDev](https://github.com/MxttDev/M-drilling) for their **Drilling Minigame**, which laid the groundwork for the drilling mechanics implemented here.
- [SezayK](https://github.com/SezayK/six_atmrobbery/tree/main) for their **Plasma Drilling Minigame**, which laid the groundwork for the plasma drilling mechanics implemented here.

Your contributions have not only enriched the FiveM ecosystem but have also fostered a spirit of collaboration and innovation within the community. Thank you for sharing your work and inspiring others to push the boundaries of what is possible. This resource would not have been possible without your efforts.

---

## Support
For support, issues, or feature requests, please open an issue on this repository or contact us directly through our Discord. [![Discord Badge](https://img.shields.io/badge/-Glitch%20Studios-000000?style=flat&labelColor=7289DA&logo=discord&link=https://discord.gg/yourdiscordlink)](https://discord.gg/3DsNKxq2DQ)
