local debug_enabled = false
local bag_log = {}
local compiled_times = {}

AddStateBagChangeHandler(nil, nil, function(bagName, key, value, reserved, replicated) 
  if not debug_enabled then
    return
  end

  local thetime = os.time()

  if not bag_log[thetime] then
    bag_log[thetime] = {}
  end

  table.insert(bag_log[thetime], {
    bagName,
    key,
    string.len(json.encode(value))
  })
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    if debug_enabled then
      local target_time = os.time() - 1

      if not compiled_times[target_time] and bag_log[target_time] then
        local biggest = { '', '', 0 }

        for _, bag_data in pairs(bag_log[target_time]) do
          if bag_data[3] > biggest[3] then
            biggest = bag_data
          end
        end

        print('--------------------------------------')
        print('---- Bag diagnostic ' .. os.time() .. ' ----')
        print('Biggest bag', biggest[1], biggest[2], biggest[3])
        print('Total bags', #bag_log[target_time])
      end
    end
  end
end)

AddEventHandler('rconCommand', function(command, args)
  if command == 'sbdiagtoggle' then
    debug_enabled = not debug_enabled

    CancelEvent()
  end

  if command == 'sbdiagdump' and args[1] then
    args[1] = tonumber(args[1])

    if not bag_log[args[1]] then
      return
    end

    for _, bag_data in pairs(bag_log[args[1]]) do
      print(bag_data[1], bag_data[2], bag_data[3])
    end

    CancelEvent()
  end
end)