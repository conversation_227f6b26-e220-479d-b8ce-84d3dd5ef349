cfg = {}

--Please note the config order is important, dealerPositions must start from 0 and increase consecutively
cfg.blackjackTables = {
  -- Always available
  [0] = {
    dealerPos = vector3(1043.305, 70.608, 70.254),
    dealerHeading = 15.334,
    tablePos = vector3(1043.098, 71.39528, 69.25376),
    tableHeading = 195.334,
    prop = 'vw_prop_casino_blckjack_01',
    distance = 1000.0,

    maxBet = 5000,
  },

  [1] = {
    dealerPos = vector3(1043.836, 68.051, 70.254),
    dealerHeading = 195.334,
    tablePos = vector3(1044.052, 67.25893, 69.25376),
    tableHeading = 15.334,
    prop = 'vw_prop_casino_blckjack_01',
    distance = 1000.0,

    maxBet = 5000,
  },

  -- High limit pit south
  [2] = {
    dealerPos = vector3(1026.685, 54.516, 71.059),
    dealerHeading = 286.626,
    tablePos = vector3(1027.471, 54.73643, 70.0537),
    tableHeading = 106.626,
    prop = 'vw_prop_casino_blckjack_01b',
    distance = 1000.0,

    minEmployees = 1,
    maxBet = 10000,
  },

  -- High limit pit north
  [3] = {
    dealerPos = vector3(1021.902, 75.253, 71.059),
    dealerHeading = 281.195,
    tablePos = vector3(1022.707, 75.4406, 70.0537),
    tableHeading = 101.195,
    prop = 'vw_prop_casino_blckjack_01b',
    distance = 1000.0,

    minEmployees = 3,
    maxBet = 25000,
  },
}

function chair2table(chair)
  return math.floor(chair / 4)
end
