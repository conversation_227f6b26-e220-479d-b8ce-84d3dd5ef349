local smokeHeight = 27.6


-- The key must be same like in Config.MixerList otherwise it wont show in mixer
Config.WorldEffects["Bahama Mamas"] = {
    effects = {
        {
            label = "Sparklers",
            name = Effects.SPARKLERS,

            ButtonType = ButtonType.BUTTON,

            -- this variable will work only with Button type effects
            RemoveAfterTime = 10000,

            -- this is working for both vanilla unicorn + gabz map aswell
            position = {
                {
                    mixerPosition = vec3(-1376.861, -607.669, 31.320),
                    particlesPosition = {
                        { pos = vec3(-1393.696655, -620.059753, 30.338564), rot = vector4(0, 0, 0, 0.1) },
                        { pos = vec3(-1390.913940, -624.091492, 30.338564), rot = vector4(0, 0, 0, 0.1) },
                        { pos = vec3(-1381.507202, -623.405518, 30.338564), rot = vector4(0, 0, 0, 0.1) },
                        { pos = vector3(-1397.188, -607.118, 30.320), rot = vector4(0, 0, 0, 0.1) },
                    }
                },
            },
        },

        {
            label = "Smoke floor",
            name = Effects.SMOKE_FLOOR,

            ButtonType = ButtonType.TOGGLE,

            -- this is working for both vanilla unicorn + gabz map aswell
            position = {
                {
                    mixerPosition = vector3(-1376.861, -607.669, 31.320),
                    particlesPosition = {
                        { pos = vector3(-1390.637451, -618.373779, smokeHeight), rot = vector4(0, 0, 0, 1.2) },
                        { pos = vector3(-1387.608154, -615.676819, smokeHeight), rot = vector4(0, 0, 0, 1.2) },

                        { pos = vector3(-1384.484741, -619.496460, smokeHeight), rot = vector4(0, 0, 0, 1.2) },
                        { pos = vector3(-1387.991943, -621.928711, smokeHeight), rot = vector4(0, 0, 0, 1.2) },

                        { pos = vector3(-1383.476929, -621.997742, smokeHeight), rot = vector4(0, 0, 0, 1.2) },


                        { pos = vector3(-1394.995, -610.413, smokeHeight), rot = vector4(0, 0, 0, 1.2) },
                        { pos = vector3(-1404.483, -607.039, smokeHeight), rot = vector4(0, 0, 0, 1.2) },

                    }
                },
            },
        },

        {
            label = "Gold bars",
            name = Effects.YELLOW_BAR,

            ButtonType = ButtonType.TOGGLE,

            position = {
                {
                    mixerPosition = vec3(-1387.117432, -620.849243, 30.819593),
                    particlesPosition = {
                        { pos = vector3(-1390.835, -616.8078, 29.75044), rot = vector4(90, 0, 0, 1.0) },
                        { pos = vector3(-1387.892, -621.4323, 29.71509), rot = vector4(90, 0, 0, 1.0) },
                        { pos = vector3(-1393.778, -612.3046, 29.71196), rot = vector4(90, 0, 0, 1.0) },
                        { pos = vector3(-1391.334, -632.2741, 30.34243), rot = vector4(90, 0, 0, 1.0) },
                        { pos = vector3(-1369.976, -621.3768, 29.49987), rot = vector4(90, 0, 0, 1.0) },
                        { pos = vector3(-1409.142, -606.8165, 29.53339), rot = vector4(90, 0, 0, 1.0) },
                    }
                },
            },
        },

        {
            label = "Red bars",
            name = Effects.RED_BAR,

            ButtonType = ButtonType.TOGGLE,

            position = {
                {
                    mixerPosition = vec3(-1387.117432, -620.849243, 30.819593),
                    particlesPosition = {
                      { pos = vector3(-1390.835, -616.8078, 29.75044), rot = vector4(90, 0, 0, 1.0) },
                      { pos = vector3(-1387.892, -621.4323, 29.71509), rot = vector4(90, 0, 0, 1.0) },
                      { pos = vector3(-1393.778, -612.3046, 29.71196), rot = vector4(90, 0, 0, 1.0) },
                      { pos = vector3(-1391.334, -632.2741, 30.34243), rot = vector4(90, 0, 0, 1.0) },
                      { pos = vector3(-1369.976, -621.3768, 29.49987), rot = vector4(90, 0, 0, 1.0) },
                      { pos = vector3(-1409.142, -606.8165, 29.53339), rot = vector4(90, 0, 0, 1.0) },
                    }
                },
            },
        },

        {
            label = "RGB",
            name = Effects.RGB,

            ButtonType = ButtonType.TOGGLE,

            position = {
                {
                    mixerPosition = vec3(-1387.117432, -620.849243, 30.819593),
                    particlesPosition = {
                        { pos = vector3(-1384.255615, -627.097534, 30.812243), rot = vector4(0.0, 0, 0, 0.0), intensity = 10.0, lerpColor = 1.0, },
                        { pos = vector3(-1392.152100, -616.627625, 30.819561), rot = vector4(0.0, 0, 0, 0.0), intensity = 10.0, lerpColor = 2.0, },
                        { pos = vector3(-1384.232788, -617.954529, 30.819561), rot = vector4(0.0, 0, 0, 0.0), intensity = 10.0, lerpColor = 3.0, },
                        { pos = vector3(-1384.232788, -617.954529, 30.819561), rot = vector4(0.0, 0, 0, 0.0), intensity = 10.0, lerpColor = 3.0, },
                        { pos = vector3(-1394.965, -594.294, 30.320), rot = vector4(0.0, 0, 0, 0.0), intensity = 10.0, lerpColor = 3.0, },
                    }
                },
            },
        },
    },
}