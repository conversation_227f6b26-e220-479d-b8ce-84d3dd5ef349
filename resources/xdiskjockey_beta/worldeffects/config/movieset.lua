local smokeHeight = 32.0

-- The key must be same like in Config.MixerList otherwise it wont show in mixer
Config.WorldEffects["Movie Set"] = {
    effects = {
        {
            label = "Smoke floor",
            name = Effects.SMOKE_FLOOR,

            ButtonType = ButtonType.TOGGLE,

            position = {
                {
                    mixerPosition = vector3(-1193.26, -508.12, 36.19),
                    particlesPosition = {
                        { pos = vector3(-1198.53, -511.83, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1193.17, -511.72, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1186.39, -508.6, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1180.67, -507.86, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1173.45, -509.29, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1179.72, -502.61, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1178.42, -495.53, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1176.32, -487.88, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1178.45, -482.66, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1182.7, -487.73, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1184.94, -494.69, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1191.61, -494.73, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1197.16, -499.6, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                        { pos = vector3(-1195.6, -504.7, smokeHeight), rot = vector4(0, 0, 0, 1.3) },
                    }
                },
            },
        },
    },
}