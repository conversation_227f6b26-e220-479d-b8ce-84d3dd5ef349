local sharedObject = GetSharedObject()
local playerData = {}


function IsPlayerAtJob(job)
  local hasJob = false
-- Standalone
  if exports.blrp_phone:IsPhoneInputFocused() then
    return false
  end

  for k, v in pairs(job) do
    if exports.blrp_core:me().hasGroup(v) then
      hasJob = true
    end
    if v == 'Civilian' then
      if exports.blrp_core:me().hasGroup('LEO') then
        hasJob = true
      end
    end
  end
  return hasJob
end