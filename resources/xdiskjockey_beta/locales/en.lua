Locales["en"] = {
    ["mixer_enter"] = "Use ~INPUT_PICKUP~ to open the mixer",
    ["mixer_enter_user"] = "Use ~INPUT_PICKUP~ to open the mixer\nUse ~INPUT_DETONATE~ to pickup the mixer",

    ["music_doest_exists"] = "This music doesnt exists please make sure it is valid youtube/MP3 URL",
    ["name_is_in_use"] = "This name is already in use please choose another one!",
    ["added_music"] = "Successfully added new music!",
    ["stop_all_music"] = "All music has been stopped",

    ["category_to_que"] = "Successfully added the whole category to queue",
    ["music_to_que"] = "Music added to the queue",
    ["music_remove_que"] = "Music has been removed from queue",
    ["playing_que"] = "Playing all queued music",
    ["stop_que"] = "Stopping the queued music list",

    ["edited_music"] = "The music has been edited!",

    ["removed_music"] = "The music has been removed!",

    ["category_deleted"] = "The category has been deleted!",
    ["category_edited"] = "The category has been edited",
    ["category_created"] = "The category has been created!",

    ["entrance"] = "~INPUT_CONTEXT~ to enter",
    ["exit"] = "~INPUT_CONTEXT~ to leave",

    ["not_whitelisted"] = "This music is not whitelisted!",

    ["category_minimum_letters"] = "Input has to be at least four char long!",
    ["already_exists_category"] = "this category with this exact name already exists!",

    ["edit_key_exit"] = "You quit editing the keybinds for the effects.",

    ["nothing_can_be_heard"] = "There isnt any active music around you!",

    ["key_edited"] = "Key has been changed!",
    ["editing_key_taken"] = "This key is already taken by some other effect! Please use another key!",
    ["editing_key"] = "You're editing key for this specific effect by pressing 'backspace' or 'ESC' you will end the editing. Simply just press any key and it will bind the sound effect to it.",

    [Effects.DISABLE_NEON] = "disable neons",
    [Effects.NEON_YELLOW] = "yellow neons",
    [Effects.NEON_WHITE] = "white neons",
    [Effects.NEON_PURPLE] = "purple neons",
    [Effects.NEON_CYAN] = "cyan neons",

    [Effects.DISABLE_LIGHTS] = "disable lights",
    [Effects.LIGHT_YELLOW] = "yellow lights",
    [Effects.LIGHT_GREEN] = "green lights",
    [Effects.LIGHT_WHITE] = "white lights",
    [Effects.LIGHT_PURPLE] = "purple lights",

    [Effects.DISABLE_LASER] = "disable lasers",
    [Effects.LASER_YELLOW] = "yellow lasers",
    [Effects.LASER_GREEN] = "green lasers",
    [Effects.LASER_WHITE] = "white lasers",
    [Effects.LASER_PURPLE] = "purple lasers",

    [Effects.DISABLE_BANDS] = "disable bands",
    [Effects.BANDS_YELLOW] = "yellow bands",
    [Effects.BANDS_GREEN] = "green bands",
    [Effects.BANDS_WHITE] = "white bands",
    [Effects.BANDS_CYAN] = "cyan bands",


    [Effects.REMOVE_PANEL] = "disable the panel",
    [Effects.PANEL_PURPLE] = "purple panel",
    [Effects.PANEL_GREEN] = "green panel",
    [Effects.PANEL_PINK] = "pink panel",

    [Effects.REMOVE_NEON] = "disable the neon",
    [Effects.NEON_OBJECT_YELLOW] = "yellow neons",
    [Effects.NEON_OBJECT_WHITE] = "white neons",
    [Effects.NEON_OBJECT_PURPLE] = "purple neons",
    [Effects.NEON_OBJECT_CYAN] = "cyan neons",

    [Effects.REMOVE_LIGHT] = "disable the droplets",
    [Effects.LIGHT_OBJECT_YELLOW] = "yellow droplets",
    [Effects.LIGHT_OBJECT_GREEN] = "green droplets",
    [Effects.LIGHT_OBJECT_WHITE] = "white droplets",
    [Effects.LIGHT_OBJECT_PURPLE] = "purple droplets",

    [Effects.REMOVE_BANDS] = "disable bands",
    [Effects.BANDS_OBJECT_YELLOW] = "yellow band",
    [Effects.BANDS_OBJECT_GREEN] = "green band",
    [Effects.BANDS_OBJECT_WHITE] = "white band",
    [Effects.BANDS_OBJECT_CYAN] = "cyan band",
}