globalOptionsCache = {}
brokenOne = {}
isPlayerCloseToMusic = false
disableMusic = false

function getDefaultInfo()
    return {
        volume = 1.0,
        url = "",
        id = "",
        position = nil,
        distance = 0,
        playing = false,
        paused = false,
        loop = false,
        isDynamic = false,
        loaded = false,
        timeStamp = -1,
        maxDuration = 0,
        destroyOnFinish = true,
    }
end

function UpdatePlayerPositionInNUI()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)

    SendNUIMessage({
        type = "position",
        x = pos.x,
        y = pos.y,
        z = pos.z
    })
end

if not Config.UseExternalxSound then
    -- updating position on html side so we can count how much volume the sound needs.
    CreateThread(function()
        local refresh = Config.RefreshTime
        local ped = PlayerPedId()
        local pos = GetEntityCoords(ped)
        local lastPos = pos
        local changedPosition = false
        while true do
            Wait(refresh)
            if not disableMusic and isPlayerCloseToMusic then
                ped = PlayerPedId()
                pos = GetEntityCoords(ped)

                -- we will update position only when player have moved
                if #(lastPos - pos) >= 0.1 then
                    lastPos = pos
                    UpdatePlayerPositionInNUI()
                end

                if changedPosition then
                    UpdatePlayerPositionInNUI()
                    SendNUIMessage({ type = "unmuteAll" })
                end
                changedPosition = false
            else
                if not changedPosition then
                    changedPosition = true
                    SendNUIMessage({ type = "position", x = -900000, y = -900000, z = -900000 })
                    SendNUIMessage({ type = "muteAll" })
                end
                Wait(1000)
            end
        end
    end, "updating volume for playing music")

    -- checking if player is close to sound so we can switch bool value to true.
    CreateThread(function()
        local ped = PlayerPedId()
        local playerPos = GetEntityCoords(ped)
        while true do
            Wait(500)
            ped = PlayerPedId()
            playerPos = GetEntityCoords(ped)
            isPlayerCloseToMusic = false
            for k, v in pairs(soundInfo) do
                if v.position ~= nil and v.isDynamic then
                    if #(v.position - playerPos) < v.distance + Config.distanceBeforeUpdatingPos then
                        isPlayerCloseToMusic = true
                        break
                    end
                end
            end
        end
    end, "setting variable state to x if player is close to any music")

    -- checking if player is close to sound so we can switch bool value to true.
    CreateThread(function()
        local ped = PlayerPedId()
        local playerPos = GetEntityCoords(ped)
        while true do
            Wait(500)
            ped = PlayerPedId()
            playerPos = GetEntityCoords(ped)
            for k, v in pairs(soundInfo) do
                if v.position ~= nil and v.isDynamic then
                    if #(v.position - playerPos) < (v.distance + Config.distanceBeforeUpdatingPos) then
                        if brokenOne[v.id] then
                            brokenOne[v.id] = nil
                            v.SkipTimestamp = true
                            PlayUrlPos(v.id, v.url, v.volume, v.position, v.loop)
                            onPlayStart(v.id, function()
                                if getInfo(v.id).maxDuration then
                                    setTimeStamp(v.id, v.timeStamp or 0)
                                end
                                Distance(v.id, v.distance)

                                SetLowpassSound(v.id, GetStatusLowpassSound(v.id))

                                if v.pulse then
                                    PulseSoundStart(v.id, v.pulse.percentage, v.pulse.time)
                                end

                                if v.stereo then
                                    StartPulseStereo(v.id, v.stereo.time)
                                end
                                v.wasSilenced = false
                                v.realPlaying = true
                            end)
                        end
                    else
                        if not brokenOne[v.id] then
                            brokenOne[v.id] = true
                            v.realPlaying = false
                            v.wasSilenced = true
                            EndPulseSound(v.id, true)
                            EndPulseStereo(v.id, true)
                            DestroySilent(v.id)
                        end
                    end
                end
            end
        end
    end, "destroying and creating all music if player is close / faraway")

    -- updating timeStamp
    CreateThread(function()
        Wait(1100)

        while true do
            Wait(1000)
            for k, v in pairs(soundInfo) do
                if v.playing or v.wasSilenced then
                    local data = getInfo(v.id)
                    if data.timeStamp ~= nil and data.maxDuration ~= nil then
                        if data.timeStamp < data.maxDuration then
                            getInfo(v.id).timeStamp = getInfo(v.id).timeStamp + 1
                        else
                            if data.realPlaying then
                                Destroy(v.id)
                            end
                        end
                    end
                end
            end
        end
    end, "updating timestamp of music (xsound)")

    CreateThread(function()
        Wait(5000)
        local disableMusic = false
        local state = {
            ["missing"] = true,
            ["unknown"] = true,
            ["stopped"] = true,
        }

        if state[GetResourceState(Config.xSoundName)] then
            TriggerEvent('chat:addSuggestion', "/streamermode", "Will enable streamer mode")

            RegisterCommand("streamermode", function(source, args, rawCommand)
                disableMusic = not disableMusic

                if disableMusic then
                    TriggerEvent('chat:addMessage', { args = { "^1[xSound]", Config.Messages["streamer_on"] } })
                else
                    TriggerEvent('chat:addMessage', { args = { "^1[xSound]", Config.Messages["streamer_off"] } })
                end

                TriggerEvent("xsound:streamerMode", disableMusic)
            end, false)
        end
    end, "checking if xsound is on server and if not creating command 'streamermode'")
end