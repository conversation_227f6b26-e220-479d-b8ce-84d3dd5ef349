local playerItemsCount = {}
local cachedDefaultMixers = Deepcopy(Config.MixerList)

function calculateForwardVector(yawDegrees)
    local yawRadians = math.rad(yawDegrees)
    local forwardVector = vec3(-math.sin(yawRadians), 0, math.cos(yawRadians))
    return forwardVector
end

function getPlayerForwardVector(player)
    local playerHeading = GetEntityHeading(player)
    local forwardVector = calculateForwardVector(playerHeading)
    return forwardVector
end

--(item_id, item_name, weight, choices, recipe, extra)
exports.blrp_core:RegisterItem('dj_deck', Config.ItemName, 5, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local source = character.source

    local player = SharedObject.GetPlayerFromId(source)
    local ped = GetPlayerPed(source)
    local heading = GetEntityHeading(ped)
    local pPos = GetEntityCoords(ped) + getPlayerForwardVector(ped)

    local data = {
        mixer = {
            {
                pos = pPos,
                heading = heading,
            },
        },
    }

      player.removeInventoryItem(character, 'dj_deck', 1)

    Config.MixerList[player.identifier] = data
    TriggerClientEvent("xdiskjockey_beta:playPickAnim", source)
    Wait(800)

      TriggerClientEvent("xdiskjockey_beta:addMixerToCache", -1, data, player.identifier)
    end
  }
}, false, {
  category = 'dj'
})

RegisterNetEvent("xdiskjockey_beta:fetchCachedPlayerMixers", function()
    local playerPlacedMixers = Deepcopy(Config.MixerList)

    for k, v in pairs(cachedDefaultMixers) do
        playerPlacedMixers[k] = nil
    end

    TriggerClientEvent("xdiskjockey_beta:fetchCachedPlayerMixers", source, playerPlacedMixers)
end)

RegisterNetEvent("xdiskjockey_beta:pickupMixer", function(mixerData)
    local source = source
    local player = SharedObject.GetPlayerFromId(source)
    local character = exports.blrp_core:character(source)
    if (character.get('phone')) ~= mixerData.identifier and not character.hasGroup('staff') then
      character.notify('This doesn\'t belong to you')
      return
    end

    if (character.get('phone')) ~= mixerData.identifier and character.hasGroup('staff') and not character.request('Use your admin abuse to pick up this deck?', 30) then
      character.notify('This doesn\'t belong to you')
      return
    end

    if not character.hasRoomFor('dj_deck', 1) then
        character.notify('You can\'t carry this right now.')
        return
    end
    player.addInventoryItem(character, 'dj_deck', 1)
    Config.MixerList[mixerData.identifier] = nil

    TriggerClientEvent("xdiskjockey_beta:removeMixerCache", -1, mixerData.identifier)
    TriggerEvent("xdiskjockey_beta:stopAllMusic", mixerData)
end)