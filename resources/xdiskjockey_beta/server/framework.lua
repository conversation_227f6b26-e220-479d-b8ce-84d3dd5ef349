SharedObject = GetSharedObject()

if not SharedObject.Functions and not SharedObject.GetPlayerFromId then
  function PlayerIdentifier(source)
    local identifier = "none"

    identifier = exports.blrp_core:character(source).get('phone')

    return identifier
  end

    SharedObject.GetPlayerFromId = function(source)
        local xPlayer = {}
        local character = exports.blrp_core:character(source)
        ---------
        xPlayer.identifier = character.get('phone')
        ---------
        xPlayer.license = PlayerIdentifier(source)
      ---------
        xPlayer.character = character
      ---------
        xPlayer.job = {
            name = "none",
            label = "none",
            grade = {
                name = "none",
                level = "none"
            }
        }
        ---------
        xPlayer.removeInventoryItem = function(character, itemName, count)
          return character.take(itemName, count)
        end
        ---------
        xPlayer.addInventoryItem = function(character, itemName, count)
          return character.give(itemName, count)
        end
        ---------
        xPlayer.canCarryItem = function(itemName, count)
            return true
        end
        ---------
        return xPlayer
    end
end

function IsPlayerAtJob(source, job)
  local hasJob = false
  -- Standalone
  for k, v in pairs(job) do
    if exports.blrp_core:character(source).hasGroup(v) then
      hasJob = true
    end
  end

  return hasJob
end