Config = {}

Config.Debug = false

Config.Framework = {
    -- 0 = standalone
    -- 1 = esx
    -- 2 = qbcore
    Active = 0,

    -- esx
    ESX_SHARED_OBJECT = "esx:getSharedObject",

    -- es_extended resource name
    ES_EXTENDED_NAME = "es_extended",

    -------

    -- qbcore
    QBCORE_SHARED_OBJECT = "QBCore:GetObject",

    -- qb-core resource name
    QB_CORE_NAME = "qb-core",

    -- will not detect any supported framework if on true.
    DisableDetection = false,
}

-- this is domain name so allow anything that you would like to be allowed
Config.WhitelistedURL = {
    "youtube",
    "youtu",
    "discordapp",
    "blrp.net",
    "open.spotify",
    "spotify",
    "soundcloud",
}

-- if there is any error this will help us to know from where does the error come from
Config.GeneralDebug = false

-- can be left on nil value it will automatically detect your database.
-- but just in case you had more than one mysql framework on your server
-- you will need to choose the type manually

-- 0 = oxmysql
-- 1 = ghmattimysql
-- 2 = mysql-async
Config.MysqlType = nil

-- using ox inv?
Config.ox_inv = false

-- using codem-inv?
Config.codem_inventory = false

-- item called
Config.ItemName = "DJ Mixer"

-- if using standalone how many mixers player will be allowed to have / place
Config.MixerInventoryCount = 1

-- Key to open menu
Config.KeyToOpen = "E"

-- Locale for script
Config.Locale = "en"

-- default start volume
Config.DefaultMixerVolume = 0.5

-- mixer object player place on ground
Config.MixerPlaceObject = "h4_prop_battle_dj_stand"

-- the red marker above the mixer player can spawn offset
Config.MarkerOffsetForObject = vector3(0.0, 0.0, 1.0)

-- the teleport player behind/infront depends if you have changed the mixer place object offset
Config.MixerObjectOffsetTeleport = vector3(0.0, -0.65, 1.0)

-- if you have changed the object in "MixerPlaceObject" the rotation of player might be wrong here just edit
-- the number to greater/lower to rotate the player to correct way
Config.OffsetHeadingForObjectTeleport = 0.0

-- personal mixer distance playing
Config.PersonalMixerDistance = 35.0

-- how close player have to be in order to see the object player spawned via item / command
Config.PersonalMixerObjectDistance = 60.0

-- idle animation
Config.AnimDictMixerIdle = "anim@amb@nightclub@djs@dixon@"
Config.AnimClipMixerIdle = "dixn_dance_cntr_open_dix"

-- DJ mixer list
Config.MixerList = {
  ["Arcade"] = {
    mixer = {
      {
        pos = vector3(-1649.216, -1062.748, 12.160),
        distance = 2.0,

      },
    },
    speaker = {
      {
        pos = vector3(-1654.093, -1070.110, 12.160),
        distance = 15,
      },
    },
    --jobs = { "staff" },
    defaultVolume = 0.2,
  },
  ["BowlingAlley"] = {
    mixer = {
      {
        pos = vector3(754.758, -772.906, 26.337),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(737.403, -774.666, 26.445),
        distance = 25.0,
      },
    },
    defaultVolume = 0.2,
    --jobs = { "staff" },
  },
  ["Belmont"] = {
    mixer = {
      {
        pos = vector3(-1341.352, -1134.459, 4.180407),
        distance = 2.0,

      },
    },
    speaker = {
      {
        pos = vector3(-1339.038, -1134.853, 5.418934),
        distance = 35,
      },
    },
    defaultVolume = 0.02,
    jobs = { "The Belmont", "staff" },
  },
  ["Tuner"] = {
    mixer = {
      {
        pos = vector3(876.3286, -2100.586, 29.47966),
        distance = 2.0,

      },
    },
    speaker = {
      {
        pos = vector3(893.6022, -2114.811, 37.79039),
        distance = 30,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Los Santos Tuners", "staff" },
  },
  ["TequiLaLa"] = {
    mixer = {
      {
        pos = vector3(-560.1456, 282.6844, 85.17428),
        distance = 2.0,

      },
    },
    speaker = {
      {
        pos = vector3(-558.3432, 285.7208, 83.92356),
        distance = 20,
      },
    },
    defaultVolume = 0.02,
    jobs = { "TequiLaLa", "staff" },
  },
  ["vanillaunicorn"] = {
    mixer = {
      {
        pos = vector3(120.72, -1281.12, 29.48),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(120.58, -1281.12, 29.48),
          heading = 119.98,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(117.47, -1290.58, 29.17),
        distance = 20.0,
      },
    },

    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.5,
    jobs = { "Vanilla Unicorn", "staff" },
  },
  -- ["Must be unique name"] = {
  ["SFTPawn"] = {
    mixer = {
      {
        pos = vector3(403.620, 317.917, 103.126),
        distance = 1.5,
      },
    },
    speaker = {
      {
        pos = vector3(410.1002, 322.6127, 105.6337),
        distance = 20,
      },
    },

    defaultVolume = 0.1,
    jobs = { "SFT Pawn", "staff" },
  },
  ["ViceMotor"] = {
    mixer = {
      {
        pos = vector3(58.841, 6511.611, 31.446),
        distance = 1.5,
      },
    },
    speaker = {
      {
        pos = vector3(57.4731, 6520.299, 34.55516),
        distance = 25,
      },
    },

    defaultVolume = 0.1,
    jobs = { "Vice Motorsports", "staff" },
  },
  -- ["Must be unique name"] = {
  ["Casino"] = {
    mixer = {
      {
        pos = vector3(912.1136, 60.36038, 110.6612),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(917.4017, 46.61119, 113.6612),
        distance = 40,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Diamond Casino", "staff" },
  },
  -- ["Must be unique name"] = {
  ["CasinoLobby"] = {
    mixer = {
      {
        pos = vector3(963.5316, 34.03484, 72.57208),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(984.3008, 45.31236, 76.85866),
        distance = 30,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Diamond Casino", "staff" },
  },
  -- ["Must be unique name"] = {
  ["PDM"] = {
    mixer = {
      {
        pos = vector3(-23.126, -1099.169, 27.274),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-37.424, -1096.396, 29.962),
        distance = 23,
      },
    },
    defaultVolume = 0.02,
    jobs = { "PDM Auto", "staff" },
  },
  -- ["Must be unique name"] = {
  ["LSC"] = {
    mixer = {
      {
        pos = vector3(-319.1807, -136.771, 38.01568),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-331.1295, -130.428, 47.68786),
        distance = 30,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Los Santos Customs", "staff" },
  },
  ["BoatHouse"] = {
    mixer = {
      {
        pos = vector3(1522.422, 3788.562, 33.51376),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(1539.26, 3793.656, 37.6432),
        distance = 25,
      },
    },
    defaultVolume = 0.02,
    jobs = { "The Boathouse", "staff" },
  },
  ["MidnightClub"] = {
    mixer = {
      {
        pos = vector3(945.122, -1744.412, 21.025),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(949.867, -1799.097, 10.609),
        distance = 25,
      },
    },
    defaultVolume = 0.5,
    jobs = { "Midnight Club", "staff" },
  },
  ["BurgerShot"] = {
    mixer = {
      {
        pos = vector3(-1189.549, -897.374, 13.798),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-1190.095, -888.942, 13.798),
        distance = 20,
      },
    },
    defaultVolume = 0.1,
    jobs = { "Burgershot", "staff" },
  },
  ["Boo's Catfe"] = {
    mixer = {
      {
        pos = vector3(-581.498, -1058.196, 22.344),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-582.860, -1056.831, 22.347),
        distance = 40,
      },
    },
    defaultVolume = 0.1,
    jobs = { "Boo's Catfe", "staff"},
  },
  ["YellowJack"] = {
    mixer = {
      {
        pos = vector3(1948.801, 3840.282, 31.17883),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(1948.854, 3841.206, 32.179),
        distance = 12,
      },
    },
    defaultVolume = 0.1,
    jobs = { "Yellow Jack", "staff" },

    skipEmitterForThis = false,
  },
  ["Pearls"] = {
    mixer = {
      {
        pos = vector3(-1828.374, -1192.708, 14.313),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-1828.030, -1197.255, 16.751),
        distance = 20,
      },
    },
    defaultVolume = 0.1,
    jobs = { "Pearls", "staff" },
  },
  ["TreehousePaleto"] = {
    mixer = {
      {
        pos = vector3(-889.763, 6038.254, 47.81268),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-896.3744, 6043.324, 51.11034),
        distance = 15,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Civilian", "staff" },
  },
  ["CoolBeans"] = {
    mixer = {
      {
        pos = vector3(-1207.715, -1130.107, 6.833265),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-1201.6, -1132.368, 11.28275),
        distance = 9,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Cool Beans", "staff" },
  },
  ["Mech305"] = {
    mixer = {
      {
        pos = vector3(-1429.941, -454.8697, 34.9097),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-1419.273, -450.0721, 39.21903),
        distance = 20,
      },
    },
    defaultVolume = 0.2,
    jobs = { "305 Autobody", "staff" },
  },
  ["The Vault"] = {
    mixer = {
      {
        pos = vector3(231.318, -1095.062, 131.468),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(240.880, -1091.604, 135.074),
        distance = 20,
      },
    },
    defaultVolume = 0.2,
    jobs = { "The Vault", "staff" },
  },
  ["Angels Autocare"] = {
    mixer = {
      {
        pos = vector3(983.532, -132.564, 78.889),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(965.626, -125.597, 74.353),
        distance = 20,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Angels Autocare", "staff" },
  },
  ["Mikesport"] = {
    mixer = {
      {
        pos = vector3(565.587, 2750.692, 41.84914),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(562.4348, 2750.406, 45.34894),
        distance = 15,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Mikes Sporting Goods", "staff" },
  },
  ["HayesAuto"] = {
    mixer = {
      {
        pos = vector3(481.9914, -1307.928, 28.27607),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(482.0994, -1321.709, 36.8172),
        distance = 30,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Hayes Auto", "staff" },
  },
  ["Frathouse"] = {
    mixer = {
      {
        pos = vector3(-1626.629, 3.762006, 60.79652),
        distance = 2.0,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-1626.181, 3.984, 61.786),
        heading = 141.467,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-1636.299, -6.376266, 69.49437),
        distance = 30,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Civilian", "staff" },
  },
  ["Chihuahua"] = {
    mixer = {
      {
        pos = vector3(831.158, -111.555, 79.775),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(837.3497, -112.8492, 83.31697),
        distance = 15,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Chihuahua Hotdogs", "staff" }, -- if left nil, everyone will be able to open it
  },
  ["Flywheels"] = {
    mixer = {
      {
        pos = vector3(1763.194, 3325.455, 40.44969),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(1767.457, 3328.792, 44.01589),
        distance = 15,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Flywheels", "staff" },
  },
  ["OBTatto"] = { -- Wayward
    mixer = {
      {
        pos = vector3(324.701, 177.028, 98.341),
        distance = 1.0,
      },
    },
    speaker = {
      {
        pos = vector3(322.0396, 181.1004, 103.6654),
        distance = 15,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Obsidian Tattoos", "staff" },
  },
  ["Ace"] = {
    mixer = {
      {
        pos = vector3(761.625,-1286.555,30.466),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(752.899,-1279.540,29.000),
        distance = 30,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Civilian", "staff" },
  },
  ["BCSO"] = {
    mixer = {
      {
        pos = vector3(1845.048, 3672.180, 38.929),
        distance = 1.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(1841.390, 3674.406, 40.061),
        distance = 6.0,
      },
    },
    defaultVolume = 0.2,
    jobs = { "sheriff_rank5", "staff" },
  },
  ["Joe"] = {
    mixer = {
      {
        pos = vector3(-31.81498, -1397.486, 28.50818),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-27.9232, -1396.086, 32.13584),
        distance = 12,
      },
    },

    defaultVolume = 0.2,
    jobs = { "Joes Corner", "staff" },
  },
  ["Ballas"] = {
    mixer = {
      {
        pos = vector3(-3.80543, -1819.522, 25.185),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-8.632432, -1826.298, 27.88872),
        distance = 12,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Ballas", "staff" },
  },
  ["market"] = {
    mixer = {
      {
        pos = vector3(1811.396, 3745.1, 32.47674),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(1813.156, 3758.312, 36.68154),
        distance = 24,
      },
    },

    defaultVolume = 0.2,
    jobs = { 'Yellers Market', "staff" },
  },
  ["Scorpions"] = {
    mixer = {
      {
        pos = vector3(-261.356, 1790.537, 206.067),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-261.356, 1790.537, 206.067),
        distance = 24,
      },
    },
    defaultVolume = 0.2,
    jobs = { 'Scorpions', "staff" },
  },
  ["Dunns"] = {
    mixer = {
      {
        pos = vector3(169.0483, 2796.57, 44.96346),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(173.8565, 2793.197, 44.96346),
        distance = 15.0,
      },
    },
    defaultVolume = 0.2,
    jobs = { 'Dump and Pump Septic', "staff" },
  },--vector3(-283.458, 6140.812, 32.266)
  ["Rocky Road"] = {
    mixer = {
      {
        pos = vector3(-283.458, 6140.812, 32.266),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(-283.458, 6140.812, 32.266),
        distance = 20,
      },
    },
    defaultVolume = 0.2,
    jobs = { 'Rocky Road Towing', "staff" },
  },
  ["Spellbound Occult"] = {
    mixer = {
      {
        pos = vector3(-73.826, -202.744, 147.778),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-71.21556, -201.4282, 150.0674),
        distance = 15,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Spellbound Occult", "staff" },
  },
  ["305 Mafia"] = {
    mixer = {
      {
        pos = vector3(-1566.651, -406.515, 42.388),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-1564.253, -409.4334, 44.26913),
        distance = 15,
      },
    },
    defaultVolume = 0.2,
    jobs = { "305 Mafia", "staff" },
  },
  ["GTY"] = {
    mixer = {
      {
        pos = vector3(2692.409, 3371.759, 57.206),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(2684.067, 3380.869, 62.0236),
        distance = 24,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Go Truck Yourself", "staff" },
  },
  ["Weazel"] = {
    mixer = {
      {
        pos = vector3(-577.935, -922.219, 28.157),
        distance = 1.5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-577.935, -922.219, 28.157),
        heading = 275.111,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(-585.5346, -919.938, 31.2038),
        distance = 24,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Weazel News", "staff" },
  },
  ["Poppy's house"] = {
    mixer = {
      {
        pos = vector3(-1706.725, 372.402, 90.225),
        distance = 2.0,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-1706.725, 372.402, 90.225),
        heading = 136.505,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-1714.678, 369.179, 89.820),
        distance = 25,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Civilian", "staff" },
  },
  ["KushKorner"] = {
    mixer = {
      {
        pos = vector3(-33.252, -1658.923, 29.492),
        distance = 1.5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-33.252, -1658.923, 29.492),
        heading = 231.038,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(-29.19525, -1666.991, 31.80677),
        distance = 25,
      },
    },
    defaultVolume = 0.2,
    jobs = { "The Kush Korner", "staff" },
  },
  ["Paleto Diner"] = {
    mixer = {
      {
        pos = vector3(83.266, 6584.583, 31.430),
        distance = 2.0,
      },
    },
    speaker = {
      {
        pos = vector3(81.06947, 6580.078, 34.21881),
        distance = 10,
      },
    },
    defaultVolume = 0.2,
    jobs = { "Paleto Diner", "staff" },
  },
  -----------------------------
  ["Vinewood Bowl"] = {
    mixer = {
      {
        pos = vector3(684.02, 571.54, 130.46),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(683.95, 571.36, 130.46),
          heading = 159.88,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(672, 536, 135),
        distance = 100.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.5,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  -----------------------------
  ["Sisyphus Theatre"] = {
    mixer = {
      {
        pos = vec3(200.129929, 1165.890991, 227.004898),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(200.02, 1165.9, 227),
          heading = 103.22,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vec3(169.950256, 1157.810913, 233.494263),
        distance = 100.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.5,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  -----------------------------
  ["Bahama Mamas"] = {
    mixer = {
      {
        pos = vector3(-1376.905, -607.716, 31.320),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(-1376.905, -607.716, 31.320),
          heading = 122.25,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(-1385.506, -607.926, 30.764),
        distance = 30.0,
      },
    },

    -- if you want to hear muffled sound if outside interior then set the interior ID here
    -- you can check what interior ID you're in by enabling "Config.InteriorDebug" in this very config.lua

    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.5,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
--   ["Cayo"] = {
--     mixer = {
--       {
--         pos = vector3(4893.789, -4905.208, 3.487),
--         distance = 5,
--
--         -- this will teleport player infront of the mixer so he stands correctly
--         teleportPlayer = {
--           pos = vector3(4893.789, -4905.208, 3.487),
--           heading = 122.25,
--
--           animDict = Config.AnimDictMixerIdle,
--           animClip = Config.AnimClipMixerIdle,
--         },
--       },
--     },
--     speaker = {
--       {
--         outside = true,
--         pos = vector3(4890.968, -4924.728, 9.020897),
--         distance = 40.0,
--       },
--     },
--     -- max value is 1.0
--     -- 1.0 = 100% volume
--     defaultVolume = 0.5,
--     --jobs = {"police","other job"} -- if left nil everyone will be able to open it
--   },
    ["Maze Bank Arena"] = {
    mixer = {
      {
        pos = vector3(-306.459, -1981.629, 22.204),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(-306.459, -1981.629, 22.204),
          heading = 51.856,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(-322.0428, -1965.995, 21.94638),
        distance = 70.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Vespucci Park"] = {
    mixer = {
      {
        pos = vector3(-1138.454, -1734.964, 4.558),
        distance = 5,
      },
    },
    speaker = {
      {
        pos = vector3(-1144.034, -1733.754, 4.558),
        distance = 45.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Playboy Wedding"] = {
    mixer = {
      {
        pos = vector3(-1583.770, 83.258, 59.690),
        distance = 5,
      },
    },
    speaker = {
      {
        pos = vector3(-1583.770, 83.258, 59.690),
        distance = 40.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Playboy Tiki Bar"] = {
    mixer = {
      {
        pos = vector3(-1431.842, 207.730, 57.822),
        distance = 5,
      },
    },
    speaker = {
      {
        pos = vector3(-1463.562, 195.888, 61.536),
        distance = 45.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Pacific Bluffs Resort"] = {
    mixer = {
      {
        pos = vector3(-3023.336, 37.904, 10.118),
        distance = 5,
      },
    },
    speaker = {
      {
        pos = vector3(-3028.686, 38.744, 10.826),
        distance = 35.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Chapel in Harmony"] = {
    mixer = {
      {
        pos = vector3(-297.222, 2750.328, 64.906),
        distance = 5,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
          pos = vector3(-297.166, 2750.358, 64.906),
          heading = 328.126,

          animDict = Config.AnimDictMixerIdle,
          animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        pos = vector3(-297.222, 2750.328, 64.906),
        distance = 30.0,
      },
    },
    -- max value is 1.0
    -- 1.0 = 100% volume
    defaultVolume = 0.3,
    --jobs = {"police","other job"} -- if left nil everyone will be able to open it
  },
  ["Del Perro Pier Yacht"] = {
    mixer = {
      {
        pos = vector3(-2036.873, -1033.748, 8.971),
        distance = 2.0,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-2036.873, -1033.748, 8.971),
        heading = 252.044,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-2029.211, -1036.472, 12.149),
        distance = 40,
      },
    },
    defaultVolume = 0.02,
    jobs = { "Civilian", "staff" },
  },
  ["Camp Morningwood Stage"] = {
    mixer = {
      {
        pos = vector3(-638.890, 2977.084, 26.115),
        distance = 2.0,

        -- this will teleport player infront of the mixer so he stands correctly
        teleportPlayer = {
        pos = vector3(-638.890, 2977.084, 26.115),
        heading = 105.179,

        animDict = Config.AnimDictMixerIdle,
        animClip = Config.AnimClipMixerIdle,
        },
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-641.467, 2960.741, 32.071),
        distance = 55,
      },
    },
    defaultVolume = 0.50,
    jobs = { "Camp Morningwood", "staff" },
  },
  ["Camp Morningwood Counsellor"] = {
    mixer = {
      {
        pos = vector3(-538.230, 2944.483, 16.184),
        distance = 2.0,
      },
    },
    speaker = {
      {
        outside = true,
        pos = vector3(-540.6796, 2943.648, 18.06243),
        distance = 10,
      },
    },
    defaultVolume = 0.50,
    jobs = { "Camp Morningwood", "staff" },
  },
  -----------------------------
  --    ["Must be unique name"] = {
  --        mixer = {
  --            {
  --                pos = vector3(120.72, -1281.12, 29.48),
  --                distance = 5,
  --            },
  --        },
  --        speaker = {
  --            {
  --                pos = vector3(120.72, -1281.12, 29.48),
  --                distance = 25.0,
  --            },
  --        },
  --        -- max value is 1.0
  --        -- 1.0 = 100% volume
  --        defaultVolume = 0.5,
  --        -- jobs = {"job","other job"} -- if left nil everyone will be able to open it
  --    },
  --    -----------------------------
  --    ["Bet it is uniqe name enough"] = {
  --        mixer = {
  --            {
  --                pos = vector3(120.72, -1281.12, 29.48),
  --                distance = 5,
  --            },
  --            -- jobs = {"job","other job"} -- if left nil everyone will be able to open it
  --        },
  --        speaker = {
  --            {
  --                pos = vector3(120.72, -1281.12, 29.48),
  --                distance = 25.0,
  --            },
  --        },
  --        -- max value is 1.0
  --        -- 1.0 = 100% volume
  --        defaultVolume = 0.5,
  --        -- jobs = {"job","other job"} -- if left nil everyone will be able to open it
  --    },
}
-- if not IsDuplicityVersion() then
--   for _, mixerdata in pairs(Config.MixerList) do
--       if not mixerdata.speaker[1].outside then
--         local interiorid = GetInteriorAtCoords(mixerdata.speaker[1].pos)
--         mixerdata.interiorIdentifier = {}
--         mixerdata.interiorIdentifier[interiorid] = true
--       else
--         mixerdata.interiorIdentifier = {}
--         mixerdata.interiorIdentifier[0] = true -- Doesnt work but helps
--       end
--   end
-- end


Config.SpawnMixerTable = {
    {
        model = "sf_prop_sf_dj_desk_02a",
        pos = vector3(683.8, 570.87, 129.46),
        heading = 161.39,
        renderDistance = 100.0,
    },

    {
        model = "sf_prop_sf_dj_desk_02a",
        pos = vector3(199.58, 1165.72, 226.01),
        heading = 105.42,
        renderDistance = 100.0,
    },

    {
      model = "h4_prop_battle_dj_deck_01a_a",
      pos = vector3(-1626.685, 3.550883, 61.70589), -- Frat house
      heading = 140.00,
      renderDistance = 100.0,
    },
}


-- This will disable all teleport markers on map (from this resource)
Config.EnableTeleportMarker = false

Config.TeleportMarker = {
    {
        entrance = {
            markerPosition = vector3(-577.01, 239.84, 81.8),

            markerStyle = {
                style = 27,
                rotation = true,
                faceCamera = false,

                scale = vector3(2, 2, 1),
                color = { r = 255, g = 255, b = 255, a = 125 },

                keysToOpen = { 38 },
            },

            destination = vector3(-1569.4, -3016.94, -74.41),
            heading = 358.87,
        },

        exit = {
            markerPosition = vector3(-1569.43, -3016.83, -75.3),

            markerStyle = {
                style = 27,
                rotation = true,
                faceCamera = false,

                scale = vector3(1, 1, 1),
                color = { r = 255, g = 255, b = 255, a = 125 },

                keysToOpen = { 38 },
            },

            destination = vector3(-577.01, 239.84, 82.65),
            heading = 352.87,
        },
    },
}

-- this will disable all blips on map (from this resource)
Config.EnableBlipsOnMap = false

Config.BlipList = {
    {
        Position = vector3(-577.01, 239.84, 82.65),
        Sprite = 614,
        Options = {
            name = "Galaxy club",
            type = 4,
            scale = 1.0,
            shortRange = true,
        },
    },
}

-- see in worldeffects/config/*.lua
Config.WorldEffects = {}

SoundsEffects = {
    {
        Name = "Countdown from 10 to 0 and hyping on end",
        URL = "https://www.youtube.com/watch?v=iwYDfyCABAc",
    },

    {
        Name = "Count down from 5 to 1 and hyping on end",
        URL = "https://www.youtube.com/watch?v=LZyIgCOeLLU",
    },
    {
      Name = "DJ Airhorn",
      URL = "https://www.youtube.com/watch?v=UaUa_0qPPgc",
    },
}

-- How much ofter the player position is updated ?
Config.RefreshTime = 300

-- how much close player has to be to the sound before starting updating position ?
Config.distanceBeforeUpdatingPos = 40

-- lowpass intensity the lower the number is the more hard it will be to hear the sound
-- like it is far away behind door
-- the higher the number will be the opposite will happen
Config.LowpassIntensity = 500

-- Message list
Config.Messages = {
    ["streamer_on"] = "Streamer mode is on. From now you will not hear any music/sound.",
    ["streamer_off"] = "Streamer mode is off. From now you will be able to listen to music that players might play.",
}

-- if set true you will see on your screen in what interior ID you're in
Config.InteriorDebug = false


-------------------------
-------------------------
-------------------------
-- if you're going to use external xsound alot of effects wont work from xdiskjockey_beta so I dont recommend switching anything to true from below.
-------------------------
-------------------------
-------------------------
-- external xsound?
Config.UseExternalxSound = true

-- if you want to use high_3dsounds
Config.UseHighSound = false

-- if you want to use mx-surround
Config.MXSurround = false

-- name of the lib
Config.xSoundName = "xsound"

if Config.MXSurround then
    Config.UseExternalxSound = true
    Config.xSoundName = "mx-surround"
end

if Config.UseHighSound then
    Config.xSoundName = "high_3dsounds"
    Config.UseExternalxSound = true
end
