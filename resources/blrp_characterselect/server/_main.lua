math.randomseed(os.time())

pCharacterSelect = {}
P.bindInstance('characterselect', pCharacterSelect)

tCharacterSelect = T.getInstance('blrp_characterselect', 'characterselect')

pCharacterSelect.getCharacters = function()
  return MySQL.query.await('SELECT id, active, identifier, firstname, lastname, dateofbirth, sex, home FROM characters WHERE identifier = ? AND deleted_at IS NULL', {
    exports.blrp_core:GetSourceVRPId(source)
  })
end

pCharacterSelect.getCharacterCustomization = function(character_id)
  local datatable = MySQL.scalar.await('SELECT dvalue FROM vrp_user_data WHERE dkey = ?', {
    'vRP:datatable' .. character_id
  }) or '{}'

  datatable = json.decode(datatable)

  local model = `mp_m_freemode_01`
  local clothing = {
    [1] = {0, 0},
    [3] = {8, 0},
    [4] = {17, 0},
    [5] = {0, 0},
    [6] = {1, 0},
    [7] = {0, 0},
    [8] = {15, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {38, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  }

  if datatable.customization then
    if datatable.customization.modelhash ~= nil then
      model = datatable.customization.modelhash
    elseif datatable.customization.model ~= nil then
      model = GetHashKey(datatable.customization.model)
    end

    clothing = datatable.customization
  end

  local skin = MySQL.scalar.await('SELECT skin FROM player_skins WHERE character_id = ?', {
    character_id
  }) or '{}'

  skin = json.decode(skin)

  local tattoos = MySQL.scalar.await('SELECT dvalue FROM vrp_user_data WHERE dkey = ?', {
    'vRP:tattoos' .. character_id
  }) or '{}'

  tattoos = json.decode(tattoos)

  return {
    model = model,
    clothing = clothing,
    skin = skin,
    tattoos = tattoos,
  }
end

pCharacterSelect.deleteCharacter = function(character_id)
  local player = source

  local identifier = exports.blrp_core:GetUserTables().source_identifier[player]

  if not player or not identifier then
    return false
  end

  local character = MySQL.single.await('SELECT * FROM characters WHERE id = ? AND identifier = ? LIMIT 1', {
    character_id, identifier
  })

  if not character then
    return false
  end

  local affected_rows = MySQL.update.await('UPDATE characters SET deleted_at = NOW() WHERE id = ? AND identifier = ? LIMIT 1', {
    character_id, identifier
  })

  local deleted = (affected_rows > 0)

  if deleted then
    exports.blrp_core:LogDiscord('characters', '**' .. identifier .. '** Deleted character: ' .. character.firstname .. ' ' .. character.lastname, identifier)
  end

  return deleted
end
