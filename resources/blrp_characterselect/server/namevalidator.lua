local name_filters = {}

Citizen.CreateThread(function()
  local function fetchFilters()
    name_filters = MySQL.query.await('SELECT firstname, lastname, action FROM core_name_filters')
  end

  while true do
    fetchFilters()

    Citizen.Wait(60 * 60 * 1000) -- 1 hour
  end
end)

local function trim(s)
  return string.match(s,'^()%s*$') and '' or string.match(s,'^%s*(.*%S)')
end

function validateName(player, firstname, lastname)
  local badwords = exports.blrp_core:ScanInputForBadWords(player, 'charactercreation', firstname .. ' ' .. lastname)

  if badwords then
    return false, 'Name contains profanity'
  end

  firstname = trim(string.lower(firstname))
  lastname = trim(string.lower(lastname))

  if #firstname > 25 or #lastname > 25 then
    return false, 'Your first and/or last name cannot be greater than 25 characters'
  end

  -- Prevent numbers
  if string.match(firstname, '%d+') or string.match(lastname, '%d+') then
    return false, 'Your name cannot contain numbers'
  end

  local filter_action = nil

  for _, filter in pairs(name_filters) do
    if
      (filter.firstname and not filter.lastname and firstname == filter.firstname) or -- First name only filter
      (filter.lastname and not filter.firstname and lastname == filter.lastname) or -- Last name only filter
      (filter.firstname and filter.lastname and firstname == filter.firstname and lastname == filter.lastname) -- Full name filter
    then
      filter_action = filter.action
    end
  end

  if filter_action == 'reject' then
    return false, 'You cannot use this name combination'
  end

  if filter_action == 'ban' then
    local user_id, _ = exports.vrp:getUserIdByIdentifiers(player, GetPlayerIdentifiers(player), true)

    local ban_reason_full = user_id .. ' db must appeal'
    local ban_note = 'Bannable name / firstname = ' .. firstname .. ' / lastname = ' .. lastname

    MySQL.update.await('UPDATE vrp_users SET banned = true, ban_reason = ?, banned_by_admin_id = 0 WHERE id = ?', {
      ban_reason_full,
      user_id
    })

    MySQL.insert.await('INSERT INTO vrp_user_notes (user_id, added_by, note) VALUES (?, 0, ?), (?, 0, ?)', {
      user_id, ban_reason_full,
      user_id, ban_note
    })

    DropPlayer(player, '[Banned] ' .. ban_reason_full)
  end

  return true, 'Valid name'
end

exports('ValidateName', validateName)
