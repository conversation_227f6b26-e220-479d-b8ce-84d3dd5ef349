const app = Vue.createApp({
  data() {
    return {
      error: 'Error message placeholder',
      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      switching: false,
      characters: [],
      displayWelcome: true,
      displayDev: false,

      creating<PERSON><PERSON>cter: false,

      newCharacterError: '',
      newCharacter: {
        firstname: '',
        lastname: '',
        height: null,
        month: '',
        day: '',
        year: '',
        sex: '',
        home: '',
      },

      deleteConfirm: '',
      deleting<PERSON>haracter: '',
      deletingCharacterId: 0,
    }
  },

  mounted() {
    window.addEventListener('message', (event) => {
      if(!event.data || !event.data.action) {
        return;
      }

      if(event.data.action == 'devintro') {
        root.displayWelcome = false;
        root.displayDev = true;
      }

      if(event.data.action == 'loadcharacters') {
        $('body').show();

        root.characters = event.data.characters;

        //This tells the client that the Dom/Javascript loaded and the call was successfully executed
        fetch(`https://${GetParentResourceName()}/loadedCharacters`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
          },
          body: JSON.stringify({}) 
        });

      }

      if(event.data.action == 'previewcharacter') {
        root.characters.forEach(character => {
          character.active = character.id == event.data.character_id ? 1 : 0;
        })
      }
    });
  },

  methods: {
    mainPanel(event) {
      root.displayWelcome = false;
      root.error = '';

      $(event.target).closest('.pane').hide();
      $('#pane-main').show();
      $('#pane-info').show();
    },

    newCharacterPane() {
      root.newCharacterError = '';
      root.newCharacter = {
        firstname: '',
        lastname: '',
        height: null,
        month: '',
        day: '',
        year: '',
        sex: '',
        home: '',
      }

      $('#pane-main').hide();
      $('#pane-create').show();
    },

    ccInput() {
      root.newCharacterError = '';
    },

    createCharacter() {
      root.creatingCharacter = true;

      fetch(`https://${GetParentResourceName()}/createCharacter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          character: root.newCharacter
        })
      }).then(response => response.json()).then(response => {
        root.creatingCharacter = false;

        if(!response.validated) {
          root.newCharacterError = response.message;
          return;
        }

        $('body').hide();
      });
    },

    selectCharacter() {
      root.characters.every(character => {
        if(character.active) {
          $('body').hide();

          fetch(`https://${GetParentResourceName()}/selectCharacter`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
              id: character.id
            })
          });

          return false;
        }

        return true;
      })
    },

    refreshCharacters(force) {
      root.characters = [];

      fetch(`https://${GetParentResourceName()}/characters`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          force: force
        })
      }).then(response => response.json()).then(characters => {
        root.characters = characters

        $('.pane:not(#pane-info)').hide();
        $('#pane-main').show();
      });
    },

    previewCharacter(character) {
      if(root.switching) {
        return;
      }

      root.characters.every(listCharacter => {
        if(character.id == listCharacter.id && !listCharacter.active) {
          root.switching = true;

          root.characters.forEach(c => {
            c.active = c.id == listCharacter.id ? 1 : 0;
          })

          fetch(`https://${GetParentResourceName()}/previewcharacter`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
              id: character.id
            })
          }).then(() => {
            root.switching = false;
          });

          return false;
        }

        return true;
      })
    },

    deleteCharacterPane() {
      if(root.characters.length <= 1) {
        root.error = 'You cannot delete your last character';

        $('#pane-main').hide();
        $('#pane-error').show();

        return;
      }

      root.deleteConfirm = '';
      root.deletingCharacter = '';
      root.deletingCharacterId = 0;

      root.characters.every(character => {
        if(character.active) {
          root.deletingCharacter = `${character.firstname} ${character.lastname}`
          root.deletingCharacterId = character.id
          return false;
        }

        return true;
      });

      if(root.deletingCharacter == '' || root.deletingCharacterId == 0) {
        return;
      }

      $('#pane-main').hide();
      $('#pane-delete').show();
    },

    deleteCharacter() {
      if(root.deleteConfirm.toLowerCase() != 'delete') {
        return;
      }

      fetch(`https://${GetParentResourceName()}/deleteCharacter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          id: root.deletingCharacterId
        })
      }).then(response => response.json()).then(response => {
        if(!response) {
          root.error = 'Error deleting character';

          $('.pane:not(#pane-info)').hide();
          $('#pane-error').show();

          return;
        }

        root.refreshCharacters(true);
      });
    },
  }
});

const root = app.mount('#app');
