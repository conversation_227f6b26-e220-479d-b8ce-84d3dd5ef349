:root {
  --button-bg: #008dcd;
  --button-bg-select: #008104;
  --button-bg-delete: #a90d0d;

  --character-bg-active: rgba(36, 38, 60, 0.8);

  --scrollbar-track: #161923f2;
  --scrollbar-thumb: #008dcd;

  --font-color: #ededed;

  --text-important: #ffa500;
  --text-important-red: #ff0000;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;

  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--font-color);
  font-weight: 500;
}

*::-webkit-scrollbar {
  width: 5px;
}

*::-webkit-scrollbar-track {
  background-color: var(--scrollbar-track);
  border-top-right-radius: 5px;
}

*::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-top-right-radius: 5px;
}

::placeholder {
  color: var(--font-color);
}

html, body {
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;
  position: relative;
}

#app {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

#list {
  width: 100px;
  height: 100px;
}

#info {
  width: 100px;
  height: 100px;

}

/* Inputs */

input, select {
  min-height: 3vh;
  padding: 5px;

  background-color: #161923;
  border: 1px solid #a90d2c;

  border-radius: none;

  margin-bottom: 5px;
}

input:focus, select:focus {
  background-color: #161923;
  outline: 1px solid #a90d0d;
  border: 1px solid #a90d0d;

  border-radius: none;
}

/* Pane bottom anchored */

.anchor-bottom {
  border-top: 1px solid #161923f2;
  margin-top: auto;
}

.anchor-bottom button:first-child {
  border-bottom-left-radius: 5px;
}

.anchor-bottom button:last-child {
  border-bottom-right-radius: 5px;
}

.anchor-bottom button:not(:first-child) {
  border-left: 1px solid #161923f2;
}

/* Panes global */

.pane:not(#pane-info) {
  min-width: 20%;
  max-width: 20%;

  min-height: 50%;
  max-height: 50%;

  background-color: #161923f2;
  border-radius: 5px;
  box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
  /* padding: 30px; */
  transition: 400ms;
}

.pane-text {
  margin-bottom: 10px;
}

/* Pane - welcome */

#pane-dev {
  min-width: 40% !important;
  max-width: 40% !important;

  min-height: unset !important;
  max-height: unset !important;
}

#pane-welcome {
  min-width: 40% !important;
  max-width: 40% !important;

  min-height: unset !important;
  max-height: unset !important;
}

#welcome-wrap {
  background-image: url('../blrp_logo.png');
  background-attachment: fixed;
  background-size: 20% auto;
  background-position: center;
  background-repeat: no-repeat;

  padding: 10px 20px;
}

/* Pane - error */

#pane-error {
  min-height: 10% !important;
}

/* Pane - delete */

#pane-delete {
  min-height: 10% !important;
}

#pane-delete .pane-text:first-child {
  margin-top: 10px;
}

/* Pane - create */

#pane-create div:first-child {
  padding: 5px 20px;
}

/* Control buttons */

button {
  border: none;
  text-transform: uppercase;
  cursor: pointer;
  background-color: var(--button-bg);
  height: 3.0vh;
}

button:hover {
  filter: brightness(75%);
}

button:active {
  filter: brightness(50%);
}

button#close-dev {
  background-color: var(--button-bg-delete);
  width: 50%;
}

button#delete-confirm {
  background-color: var(--button-bg-delete);
  width: 50%;
}

button.select {
  background-color: var(--button-bg-select);
  width: 50%;
}

.d-flex-column {
  display: flex;
  flex-direction: column;
}

#characters, #info {
  padding: 5px 20px;

  overflow-y: auto !important;
  overflow-x: hidden !important;

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.character {
  position: relative;
  width: 100%;
  min-height: 5vh;
  height: auto;
  margin-top: 1.0vh;
  border-bottom: 4px solid #74aa3200;
  transition: .1s linear;
  border-radius: .3vh;
  cursor: pointer;
  display: flex;
  align-items: center;

  text-transform: uppercase;
  letter-spacing: .1vh;

  padding: 10px;
}

.character:hover, .character.active {
  background-color: var(--character-bg-active);
  box-shadow: rgb(37 17 17 / 25%) 0px 30px 60px -12px inset, rgb(154 54 54 / 30%) 0px 18px 36px -18
}

#info {
  text-align: center;
}

.loading {
  text-align: center;
  text-transform: uppercase;
}

/* Error div */

#error-title {
  padding-top: 10px;
  color: var(--button-bg);

  text-align: center;
  text-transform: uppercase;
}

#error-message {
  padding-top: 10px;
  margin-bottom: 20px;

  text-align: center;
  text-transform: uppercase;
}

/* Text classes */

.text-important, .text-important > * {
  color: var(--text-important) !important;
}

.text-important-red, .text-important-red > * {
  color: var(--text-important-red) !important;
  background: black;
  border: 1px solid white;
}
