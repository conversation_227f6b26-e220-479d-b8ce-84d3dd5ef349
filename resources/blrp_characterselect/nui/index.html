<html>
  <head>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
  </head>

  <body style="display: none">
    <div id="app">
      <div class="pane d-flex-column" id="pane-dev" v-if="displayDev">
        <div class="d-flex-column" id="welcome-wrap">
          <span class="pane-text" style="text-align: center;">YOU ARE CONNECTING TO THE DEVELOPMENT SERVER</span>
          <span class="pane-text" style="text-align: center;">THE FOLLOWING GUIDELINES APPLY AT ALL TIMES</span>

          <ul style="margin-left: 30px; margin-bottom: 10px">
            <li>If you are testing features, follow the instructions provided by the developer leading the testing</li>
            <li>Do not explore the map</li>
            <li>Do not explore the admin shop</li>
            <li class="text-important-red">Do not repeat, allude to, explain, or otherwise tell anyone outside of the dev testing session (including staff members) about any information or mechanics learned while testing</li>
          </ul>
        </div>

        <div class="anchor-bottom">
          <button id="close-dev" style="width: 100%" v-on:click="mainPanel($event)">I ACKNOWLEDGE THE ENTIRETY OF THE ABOVE</button>
        </div>
      </div>

      <div class="pane d-flex-column" id="pane-welcome" v-if="displayWelcome">
        <div class="d-flex-column" id="welcome-wrap">
          <span class="pane-text" style="text-align: center; font-size: larger">Welcome to BadlandsRP!</span>

          <span class="pane-text">A microphone is required to play on this server</span>

          <ul style="margin-left: 30px; margin-bottom: 10px">
            <li>You are expected to put effort into RP at all times</li>
            <li>Interactions should remain respectful with an emphasis on communication and story over gunplay</li>
            <li>Please familiarize yourself with our Server Rules on badlandsrp.com</li>
            <li>Press T and type /help while in game for a list of keybinds and commands</li>
            <li class="text-important">Press Left ALT to open your "third eye" context menu. This menu provides context options to many objects in game (ATMs, Banks, Vehicles, and many more).</li>
            <li class="text-important">The <i class="fa-regular fa-eye"></i> symbol, when displayed related to an area, indicates that you should use your third eye to interact with the area</li>
          </ul>

          <span class="pane-text">For more information about life in Badlands, visit our forums at badlandsrp.com</span>

          <span class="pane-text" style="text-align: center; color: #d81212;margin-top: 15px">Seizure Warning: Flashes and visual effects are used</span>
        </div>

        <div class="anchor-bottom">
          <button id="close-welcome" style="width: 100%" v-on:click="mainPanel($event)">Let's get started!</button>
        </div>
      </div>

      <!-- left panes -->
      <div class="pane d-flex-column" id="pane-main" style="display: none;"> <!-- -->
        <div id="characters">
          <div class="character" v-if="!characters.length">You have no characters!</div>
          <div class="character" v-if="!characters.length">Create one using <i style="padding-left:5px" class="fa-regular fa-user-plus fa-fw"></i></div>
          <div class="character" v-for="character in characters" :class="{ active: (character.active == 1) }" v-on:click="previewCharacter(character)">
            {{ character.firstname }} {{ character.lastname }}
          </div>
        </div>

        <div class="anchor-bottom">
          <button style="width: 17%" v-on:click="refreshCharacters"><i class="fa-regular fa-rotate fa-fw"></i></button>
          <button style="width: 17%" v-on:click="newCharacterPane"><i class="fa-regular fa-user-plus fa-fw"></i></button>
          <button style="width: 16%" v-on:click="deleteCharacterPane"><i class="fa-regular fa-trash-can fa-fw"></i></button>
          <button class="select" v-on:click="selectCharacter"><i class="fa-regular fa-arrow-right fa-fw"></i></button>
        </div>
      </div>

      <div class="pane d-flex-column" id="pane-error" style="display: none;"> <!-- -->
        <div id="error-title">ERROR</div>
        <div id="error-message">{{ error }}</div>

        <div class="anchor-bottom">
          <button id="error-back" style="width: 100%" v-on:click="mainPanel($event)"><i class="fa-regular fa-arrow-left fa-fw"></i></button>
        </div>
      </div>

      <div class="pane d-flex-column" id="pane-delete" style="display: none;"> <!-- -->
        <div class="d-flex-column" style="padding: 5px 20px;">
          <div class="pane-text">Deleting character: {{ deletingCharacter }}</div>

          <div class="pane-text">This action CANNOT BE UNDONE!</div>

          <div class="pane-text">Are you sure you wish to delete this character? Type DELETE in the box below to confirm.</div>

          <input v-model="deleteConfirm" id="delete-confirm" type="text" />
        </div>

        <div class="anchor-bottom">
          <button id="delete-back" style="width: 50%" v-on:click="mainPanel($event)"><i class="fa-regular fa-arrow-left fa-fw"></i></button>
          <button id="delete-confirm" v-on:click="deleteCharacter"><i class="fa-regular fa-trash-can fa-fw"></i></button>
        </div>
      </div>

      <div class="pane d-flex-column" id="pane-create" style="display: none;"> <!-- -->
        <div class="d-flex-column">
          <span style="text-transform: uppercase; margin-bottom: 10px; margin-top: 10px; text-align: center;">Create Character</span>

          <input v-model="newCharacter.firstname" @input="ccInput" type="text" placeholder="First Name" />
          <input v-model="newCharacter.lastname" @input="ccInput" type="text" placeholder="Last Name" />

          <span style="font-weight: normal; font-size: smaller; margin-bottom: 5px; color: #bababa">Your character's name should be realistic, not a nickname, and not have a resemblance to a real world person</span>

          <input v-model="newCharacter.height" @input="ccInput" type="number" min="140" max="200" placeholder="Height (140 - 200 cm)">

          <select v-model="newCharacter.month" @input="ccInput">
            <option value="" disabled selected hidden>Month</option>
            <option v-for="i in 12" :value="i">{{ months[i - 1] }}</option>
          </select>

          <select v-model="newCharacter.day" @input="ccInput">
            <option value="" disabled selected hidden>Day</option>
            <option v-for="i in 31" :value="i">{{ i }}</option>
          </select>

          <select v-model="newCharacter.year" @input="ccInput">
            <option value="" disabled selected hidden>Year</option>
            <option v-for="i in 100" :value="new Date().getFullYear() - i">{{ new Date().getFullYear() - i }}</option>
          </select>

          <select v-model="newCharacter.sex" @input="ccInput">
            <option value="" disabled selected hidden>Gender</option>
            <option value="m">Male</option>
            <option value="f">Female</option>
          </select>

          <select v-model="newCharacter.home" @input="ccInput">
            <option value="" disabled selected hidden>Home Location</option>
            <option value="downbad">Alta St, Los Santos (Downbad Apartments)</option>
            <option value="billingsgate">Jamestown St, Los Santos (Billingsgate Motel)</option>
            <option value="perrera">Bay City Ave, Los Santos (Perrera Beach Motel)</option>
            <option value="sandy">Sandy Shores Motel</option>
            <option value="paleto">Paleto Bay Apartments</option>
          </select>

          <span style="text-align:center; margin-bottom: 5px; color: #d81212">{{ newCharacterError }}</span>
        </div>

        <div class="anchor-bottom">
          <button id="delete-back" style="width: 50%" v-on:click="mainPanel($event)"><i class="fa-regular fa-arrow-left fa-fw"></i></button>
          <button class="select" v-on:click="createCharacter"><i class="fa-regular fa-arrow-right fa-fw"></i></button>
        </div>
      </div>

      <!-- Right pane - filler only -->
      <div class="pane" id="pane-info" style="display: none;"> <!-- -->&nbsp;</div>

      <!--
        <div class="pane" id="pane-info">
          <div class="loading" style="display: flex;justify-content: center;align-items: center;height: 100%;" v-if="switching">
            <div>LOADING CHARACTER</div>
          </div>

          <div v-if="!switching">
            not loading {{ switching }}
          </div>
        </div>
      -->
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.slim.min.js" integrity="sha256-u7e5khyithlIdTpu22PHhENmPcRdFiHRjhAuHcs05RI=" crossorigin="anonymous"></script>
    <script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
    <script src="js/app.js"></script>
  </body>
</html>
