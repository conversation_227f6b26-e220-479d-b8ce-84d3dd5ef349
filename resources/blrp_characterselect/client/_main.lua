math.randomseed(GetGameTimer())

tCharacterSelect = {}
T.bindInstance('characterselect', tCharacterSelect)

pCharacterSelect = P.getInstance('blrp_characterselect', 'characterselect')
pVRP = P.getInstance('vrp', 'vrp')

local render_camera = nil
local spawn_location = nil
local spawned_ped = nil
local character_selected = false

local in_zoom_transition = false
local first_spawn_complete = false

local characters = nil
local yankton_wx = false
local loaded_characters = false

function getCharacterSelectLocations()
  if GetResourceState('blrp_cayo') == 'started' then
    return config.character_select_locations_cayo
  end

  if GetResourceState('blrp_yankton') == 'started' then
    yankton_wx = true
    return config.character_select_locations_yankton
  end

  return config.character_select_locations
end

exports('FirstSpawnComplete', function()
  return first_spawn_complete
end)

exports('IsNearSpawnPosition', function(coords)
  if not coords then
    coords = GetEntityCoords(PlayerPedId())
  end

  for _, spawn_location in pairs(getCharacterSelectLocations()) do
    if #(coords - (spawn_location.xyz + vector3(0, 0, config.character_select_z_offset))) <= 10.0 then
      return true
    end
  end

  return false
end)

tCharacterSelect.teleport = function(coords)
  print('Teleporting player to coords (1)', coords)

  SetEntityCoords(PlayerPedId(), coords)

	while IsEntityWaitingForWorldCollision(PlayerPedId()) do
    print('waiting for WorldCollision')
    SetEntityCoords(PlayerPedId(), coords)
    Citizen.Wait(50)
	end

  Citizen.Wait(300)

  print('Teleporting player to coords (2)', coords)

  SetEntityCoords(PlayerPedId(), coords)
end

tCharacterSelect.zoomTransition = function()
  in_zoom_transition = true

  local camera_position = config.zoom_camera_locations[math.random(1, #config.zoom_camera_locations)]

  local player_coords = GetEntityCoords(PlayerPedId())

  -- Handle people on Cayo Perico
  if GetResourceState('blrp_cayo') == 'started' and #(vector2(4840.571, -5174.425) - player_coords.xy) < 2000.0 then
    camera_position = vector4(3815.705, -4052.686, 1300.0, 222.917)
  end

  -- Handle people in North Yankton
  if GetResourceState('blrp_yankton') == 'started' and #(vector2(4467.000, -4991.000) - player_coords.xy) < 2000.0 then
    camera_position = vector4(3968.809, -3970.389, 1300.0, 180.0)
  end

  SetCamActive(render_camera, false)
  DestroyCam(render_camera, true)
  RenderScriptCams(false, false, 1, true, true)
  render_camera = nil

  local camera = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA', camera_position.x, camera_position.y, camera_position.z, -37.173, 0.0, camera_position.w, 80.0, true, 2)

  RenderScriptCams(true, 0, 0, true, false)

  -- Blocks until the skin has initialized
  exports.blrp_character:InitializeSkin()

  -- Arbitrary blocking time so that the person gets the zoomed out overview of the map before zooming in
  Citizen.Wait(2500)

  if DoesCamExist(camera) then
    DestroyCam(camera, false)
  end

  RenderScriptCams(false, 5000, 5000, true, false)

  -- Block until just before the fly in is done
  Citizen.Wait(4000)

  -- Reset the draw origin, just in case (allowing HUD elements to re-appear correctly)
  ClearDrawOrigin()

  exports.blrp_hud:SetUIVisible(true)

  TriggerServerEvent('instance:leaveInstance', 'initspawn', true)

  if spawned_ped then
    DeleteEntity(spawned_ped)
  end

  in_zoom_transition = false
  first_spawn_complete = true
end

exports('ZoomTransition', tCharacterSelect.zoomTransition)

function RequestModelInternal(model)
  RequestModel(model)

  while not HasModelLoaded(model) do
    Citizen.Wait(0)
  end
end

function previewCharacter(character_id)
  FreezeEntityPosition(PlayerPedId(), true) -- TODO: remove - dev - for hot reboot

  local customization

  if not character_id then
    customization = {
      model = `mp_m_freemode_01`,
      clothing = {
        [1] = {0, 0},
        [3] = {8, 0},
        [4] = {17, 0},
        [5] = {0, 0},
        [6] = {1, 0},
        [7] = {0, 0},
        [8] = {15, 0},
        [9] = {0, 0},
        [10] = {0, 0},
        [11] = {38, 0},
        ['p0'] = {-1, 0},
        ['p1'] = {-1, 0},
        ['p2'] = {-1, 0},
        ['p6'] = {-1, 0},
        ['p7'] = {-1, 0},
      },
      skin = {},
      tattoos = {},
    }
  else
    customization = pCharacterSelect.getCharacterCustomization({ character_id })
  end

  local model = customization.model

  RequestModelInternal(model)

  if spawned_ped then
    DeleteEntity(spawned_ped)
  end

  local success = pcall(function()
    spawned_ped = CreatePed(4, model, spawn_location.x, spawn_location.y, spawn_location.z - 0.97, spawn_location.w, false, true)
  end)

  if not success then
    print('Did not spawn ped preview successfully, falling back on mp_m_freemode_01', spawn_location)

    RequestModelInternal(`mp_m_freemode_01`)

    spawned_ped = CreatePed(4, `mp_m_freemode_01`, spawn_location.x, spawn_location.y, spawn_location.z - 0.97, spawn_location.w, false, true)

    SetEntityVisible(spawned_ped, false)
  end

  SetEntityInvincible(spawned_ped, true)
  SetBlockingOfNonTemporaryEvents(spawned_ped, true)

  -- Prevent spawned ped falling through the ground
  FreezeEntityPosition(spawned_ped, true)

  Citizen.CreateThread(function()
    local timeout = GetGameTimer() + 10000
    local timed_out = false

    while not HasCollisionLoadedAroundEntity(spawned_ped) do
      if GetGameTimer() > timeout then
        timed_out = true
        break
      end

      Wait(0)
    end

    if not timed_out and DoesEntityExist(spawned_ped) then
      -- FreezeEntityPosition(spawned_ped, false)
    end
  end)

  -- Set skin customization and clothing only if MP model (M or F)
	if model == `mp_m_freemode_01` or model == `mp_f_freemode_01` then
    local skin = {}

    for k, v in pairs(config.ped_skin_default) do
      if customization.skin[k] then
        v = customization.skin[k]
      end

      skin[k] = v
    end

		if male then
			SetPedComponentVariation(spawned_ped, 11, 16, 0, 0)
			SetPedComponentVariation(spawned_ped, 8, 0, 240, 0)
		else

		end

		SetPedHeadBlendData(spawned_ped, skin.mom, skin.dad, 0, skin.mom, skin.dad, 0, (skin.face_md_weight / 100 + 0.0), (skin.skin_md_weight / 100 + 0.0), 0.0, false)

	  -- Facial Features
	  SetPedFaceFeature(spawned_ped, 0,  (skin.nose_1 / 100)         + 0.0)  -- Nose Width
	  SetPedFaceFeature(spawned_ped, 1,  (skin.nose_2 / 100)         + 0.0)  -- Nose Peak Height
	  SetPedFaceFeature(spawned_ped, 2,  (skin.nose_3 / 100)         + 0.0)  -- Nose Peak Length
	  SetPedFaceFeature(spawned_ped, 3,  (skin.nose_4 / 100)         + 0.0)  -- Nose Bone Height
	  SetPedFaceFeature(spawned_ped, 4,  (skin.nose_5 / 100)         + 0.0)  -- Nose Peak Lowering
	  SetPedFaceFeature(spawned_ped, 5,  (skin.nose_6 / 100)         + 0.0)  -- Nose Bone Twist
	  SetPedFaceFeature(spawned_ped, 6,  (skin.eyebrows_5 / 100)     + 0.0)  -- Eyebrow height
	  SetPedFaceFeature(spawned_ped, 7,  (skin.eyebrows_6 / 100)     + 0.0)  -- Eyebrow depth
	  SetPedFaceFeature(spawned_ped, 8,  (skin.cheeks_1 / 100)       + 0.0)  -- Cheekbones Height
	  SetPedFaceFeature(spawned_ped, 9,  (skin.cheeks_2 / 100)       + 0.0)  -- Cheekbones Width
	  SetPedFaceFeature(spawned_ped, 10, (skin.cheeks_3 / 100)       + 0.0)  -- Cheeks Width
	  SetPedFaceFeature(spawned_ped, 11, (skin.eye_squint / 100)     + 0.0)  -- Eyes squint
	  SetPedFaceFeature(spawned_ped, 12, (skin.lip_thickness / 100)  + 0.0)  -- Lip Fullness
	  SetPedFaceFeature(spawned_ped, 13, (skin.jaw_1 / 100)          + 0.0)  -- Jaw Bone Width
	  SetPedFaceFeature(spawned_ped, 14, (skin.jaw_2 / 100)          + 0.0)  -- Jaw Bone Length
	  SetPedFaceFeature(spawned_ped, 15, (skin.chin_1 / 100)         + 0.0)  -- Chin Height
	  SetPedFaceFeature(spawned_ped, 16, (skin.chin_2 / 100)         + 0.0)  -- Chin Length
	  SetPedFaceFeature(spawned_ped, 17, (skin.chin_3 / 100)         + 0.0)  -- Chin Width
	  SetPedFaceFeature(spawned_ped, 18, (skin.chin_4 / 100)         + 0.0)  -- Chin Hole Size
	  SetPedFaceFeature(spawned_ped, 19, (skin.neck_thickness / 100) + 0.0)  -- Neck Thickness

	  -- Appearance
	  SetPedComponentVariation(spawned_ped, 2, skin.hair_1, skin.hair_2, 2)                  -- Hair Style
	  SetPedHairColor(spawned_ped, skin.hair_color_1, skin.hair_color_2)                     -- Hair Color
	  SetPedHeadOverlay(spawned_ped, 2, skin.eyebrows_1, skin.eyebrows_2 / 100 + 0.0)        -- Eyebrow Style + Opacity
	  SetPedHeadOverlayColor(spawned_ped, 2, 1, skin.eyebrows_3, skin.eyebrows_4)            -- Eyebrow Color
	  SetPedHeadOverlay(spawned_ped, 1, skin.beard_1, skin.beard_2 / 100 + 0.0)              -- Beard Style + Opacity
	  SetPedHeadOverlayColor(spawned_ped, 1, 1, skin.beard_3, skin.beard_4)                  -- Beard Color

	  SetPedHeadOverlay(spawned_ped, 0, skin.blemishes_1, skin.blemishes_2 / 100 + 0.0)      -- Skin blemishes + Opacity
	  SetPedHeadOverlay(spawned_ped, 12, skin.bodyb_3, skin.bodyb_4 / 100 + 0.0)             -- Skin blemishes body effect + Opacity

	  SetPedHeadOverlay(spawned_ped, 11, skin.bodyb_1, skin.bodyb_2 / 100 + 0.0)             -- Body Blemishes + Opacity

	  SetPedHeadOverlay(spawned_ped, 3, skin.age_1, skin.age_2 / 100 + 0.0)                  -- Age + opacity
	  SetPedHeadOverlay(spawned_ped, 6, skin.complexion_1, skin.complexion_2 / 100 + 0.0)    -- Complexion + Opacity
	  SetPedHeadOverlay(spawned_ped, 9, skin.moles_1, skin.moles_2 / 100 + 0.0)              -- Moles/Freckles + Opacity
	  SetPedHeadOverlay(spawned_ped, 7, skin.sun_1, skin.sun_2 / 100 + 0.0)                  -- Sun Damage + Opacity
	  SetPedEyeColor(spawned_ped, skin.eye_color)                                            -- Eyes Color
	  SetPedHeadOverlay(spawned_ped, 4, skin.makeup_1, skin.makeup_2 / 100 + 0.0)            -- Makeup + Opacity
	  SetPedHeadOverlayColor(spawned_ped, 4, 0, skin.makeup_3, skin.makeup_4)                -- Makeup Color
	  SetPedHeadOverlay(spawned_ped, 5, skin.blush_1, skin.blush_2 / 100 + 0.0)              -- Blush + Opacity
	  SetPedHeadOverlayColor(spawned_ped, 5, 2,	skin.blush_3)                                -- Blush Color
	  SetPedHeadOverlay(spawned_ped, 8, skin.lipstick_1, skin.lipstick_2 / 100 + 0.0)        -- Lipstick + Opacity
	  SetPedHeadOverlayColor(spawned_ped, 8, 2, skin.lipstick_3, skin.lipstick_4)            -- Lipstick Color

    -- Clothing customization

    exports.blrp_clothingstore:SetCustomization(customization.clothing, false, false, spawned_ped)

    -- Tattoos
    for tat_overlay, tat_collection in pairs(customization.tattoos) do
      AddPedDecorationFromHashes(spawned_ped, GetHashKey(tat_collection), GetHashKey(tat_overlay))
    end
	end

  if not render_camera then
    local camera_coords = vector4((GetEntityCoords(spawned_ped) + (GetEntityForwardVector(spawned_ped) * 2.0)) + vector3(0, 0, 0.5), spawn_location.w + 180.0)

    render_camera = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA', camera_coords.x, camera_coords.y, camera_coords.z, -12.0, 0.0, camera_coords.w, 60.00, false, 0)
    SetCamActive(render_camera, true)
    RenderScriptCams(true, false, 1, true, true)
  end
end

function selectCharacter(character_id)
  character_id = tonumber(character_id)

  character_selected = true

  SetNuiFocus(false, false)

  TriggerServerEvent('vrp:updateIdentity', character_id)

  FreezeEntityPosition(spawned_ped, false)
  TaskGoToCoordAnyMeans(spawned_ped, GetEntityCoords(spawned_ped) + (GetEntityForwardVector(spawned_ped) * 5.0), 1.0, 0, 0, 786603, 0xbf800000)
end

function startCharacterSelect(fade)
  if GlobalState.is_dev then
    SendNUIMessage({
      action = 'devintro',
    })
  end

  exports.cd_easytime:PauseSync(true)

  TriggerServerEvent('instance:enterInstance', 'initspawn', true, false, 'strict', false)

  exports.blrp_hud:SetUIVisible(false)

  if fade then
    DoScreenFadeOut(1000)

    while not IsScreenFadedOut() do
      Citizen.Wait(0)
    end
  end

  character_selected = false

  Citizen.CreateThread(function()
    while not character_selected do
      Citizen.Wait(0)

      if not character_selected then
        ClearPedWetness(PlayerPedId())

        if yankton_wx then
          NetworkOverrideClockTime(23, 0, 0)
          ClearOverrideWeather()
          ClearWeatherTypePersist()
          SetWeatherTypePersist('XMAS')
          SetWeatherTypeNow('XMAS')
          SetWeatherTypeNowPersist('XMAS')
        else
          NetworkOverrideClockTime(18, 0, 0)
          ClearOverrideWeather()
          ClearWeatherTypePersist()
          SetWeatherTypePersist('EXTRASUNNY')
          SetWeatherTypeNow('EXTRASUNNY')
          SetWeatherTypeNowPersist('EXTRASUNNY')
        end
      end
    end

    exports.cd_easytime:PauseSync(false)
  end)

  local c_select_locations = getCharacterSelectLocations()

  spawn_location = c_select_locations[math.random(1, #c_select_locations)]

  SetEntityCoords(PlayerPedId(), spawn_location.xyz + vector3(0, 0, config.character_select_z_offset))

  print('Character select @ L343')

  characters = pCharacterSelect.getCharacters()

  print('Character select @ L347')

  if #characters == 0 then
    previewCharacter(false)
  else
    for k, character in pairs(characters) do
      if #characters == 1 or character.active or k == #characters then
        characters[k].active = true

        previewCharacter(character.id)

        break
      end
    end
  end

  print('Character select @ L363')

  ShutdownLoadingScreen()

  print('Character select @ L367')

  ShutdownLoadingScreenNui()

  print('Character select @ L371')

  SetNuiFocus(true, true)

  print('Character select @ L375')

  SendNUIMessage({
    action = 'loadcharacters',
    characters = characters,
  })


  Citizen.Wait(1000)

  while not loaded_characters do
    print("Retrying to grab characters due to JS not being loaded yet.")
      SendNUIMessage({
      action = 'loadcharacters',
      characters = characters,
      })
    Citizen.Wait(1000)
  end

  print('Character select @ L382')

  if fade then
	  DoScreenFadeIn(1000)
  end
end

exports('StartCharacterSelect', startCharacterSelect)

RegisterNUICallback('characters', function(data, callback)
  local _characters = characters

  if not _characters or (data and data.force) then
    _characters = pCharacterSelect.getCharacters()
  end

  for k, character in pairs(_characters) do
    if #_characters == 1 or character.active or k == #_characters then
      _characters[k].active = true
      previewCharacter(character.id)
      break
    end
  end

  callback(_characters)
end)

RegisterNUICallback('loadedCharacters', function(_, cb)
  print("loadedCharacters POST received")
  loaded_characters = true;
  print('I was called and the front end loaded for me.')
  cb("ok")
end)



RegisterNUICallback('previewcharacter', function(data, callback)
  if not data or not data.id or not tonumber(data.id) then
    callback(false)
    return
  end

  previewCharacter(tonumber(data.id))

  callback(true)
end)

RegisterNUICallback('selectCharacter', function(data, callback)
  callback(true)

  if not data or not data.id then
    return
  end

  selectCharacter(data.id)
end)

RegisterNUICallback('deleteCharacter', function(data, callback)
  if not data or not data.id then
    callback(false)

    return
  end

  callback(pCharacterSelect.deleteCharacter({ data.id }))
end)

RegisterNUICallback('createCharacter', function(data, callback)
  local validated, message = pVRP.createCharacter({ data })

  callback({
    validated = validated,
    message = message
  })

  if not validated then
    return
  end

  character_selected = true

  SetNuiFocus(false, false)
end)

Citizen.CreateThread(function()
  Citizen.Wait(1000)

  startCharacterSelect()

  while not character_selected do
    Citizen.Wait(100)

    ShutdownLoadingScreen()
    ShutdownLoadingScreenNui()
  end

  print('Loading screen should be shut down')
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(250)

    local player_ped = PlayerPedId()

    if not character_selected and spawn_location and player_ped and player_ped ~= 0 and #(GetEntityCoords(player_ped) - spawn_location.xyz) > (config.character_select_z_offset + 50.0) then
      print('Setting player coords to spawn_location + 10')
      SetEntityCoords(PlayerPedId(), spawn_location.xyz + vector3(0, 0, config.character_select_z_offset))
    end

    local instance_name = LocalPlayer.state.bucket_name

    if not character_selected and (not instance_name or not string.match(instance_name, 'initspawn')) then
      Citizen.Wait(1000)
      TriggerServerEvent('instance:enterInstance', 'initspawn', true, false, 'strict', false)
    end
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if not character_selected then
      DisableControlAction(1, 18, true)
      DisableControlAction(1, 24, true)
      DisableControlAction(1, 69, true)
      DisableControlAction(1, 92, true)
      DisableControlAction(1, 106, true)
      DisableControlAction(1, 122, true)
      DisableControlAction(1, 135, true)
      DisableControlAction(1, 142, true)
      DisableControlAction(1, 144, true)
      DisableControlAction(1, 176, true)
      DisableControlAction(1, 223, true)
      DisableControlAction(1, 229, true)
      DisableControlAction(1, 237, true)
      DisableControlAction(1, 257, true)
      DisableControlAction(1, 329, true)

      DisableControlAction(1, 14, true)
      DisableControlAction(1, 16, true)
      DisableControlAction(1, 41, true)
      DisableControlAction(1, 43, true)
      DisableControlAction(1, 81, true)
      DisableControlAction(1, 97, true)
      DisableControlAction(1, 180, true)
      DisableControlAction(1, 198, true)
      DisableControlAction(1, 39, true)
      DisableControlAction(1, 50, true)

      DisableControlAction(1, 22, true)
      DisableControlAction(1, 55, true)
      DisableControlAction(1, 76, true)
      DisableControlAction(1, 102, true)
      DisableControlAction(1, 114, true)
      DisableControlAction(1, 143, true)
      DisableControlAction(1, 179, true)
      DisableControlAction(1, 193, true)
      DisableControlAction(1, 203, true)
      DisableControlAction(1, 216, true)
      DisableControlAction(1, 255, true)
      DisableControlAction(1, 298, true)
      DisableControlAction(1, 321, true)
      DisableControlAction(1, 328, true)
      DisableControlAction(1, 331, true)
    end
  end
end)

exports('isCharacterSelected', function()
  return character_selected
end)
