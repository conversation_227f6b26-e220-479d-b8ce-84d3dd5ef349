local pillBoxBedModel = GetHashKey('hedwig_pillbox_hospitalbed')
local beds = {
    { x = 330.774, y = -584.755, z = 44.124, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 327.570, y = -583.628, z = 44.124, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 321.238, y = -581.443, z = 44.124, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 318.402, y = -580.369, z = 44.124, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 330.774, y = -584.755, z = 44.124, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },

    { x = 316.799, y = -584.620, z = 44.124, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 319.788, y = -585.776, z = 44.124, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 322.634, y = -586.838, z = 44.124, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 325.617, y = -587.946, z = 44.124, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },
    { x = 328.394, y = -588.872, z = 44.124, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillbox", allowCheckIn = true },

    -- pillbox lower

    { x = 336.039, y = -591.546, z = 29.698, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 339.751, y = -593.012, z = 29.698, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 343.178, y = -594.556, z = 29.698, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 346.760, y = -595.662, z = 29.698, h = 160.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },

    { x = 352.915, y = -579.805, z = 29.698, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 349.336, y = -578.412, z = 29.698, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 345.742, y = -577.140, z = 29.698, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },
    { x = 341.801, y = -575.828, z = 29.698, h = 340.0, taken = false, model = pillBoxBedModel, hospital = "pillboxlower", allowCheckIn = true },


    --{ x = 315.3428, y = -566.4491, z = 42.3077, h = 340.0000, taken = false, model = -1519439119, hospital = "pillbox", allowCheckIn = false },
    --{ x = 321.0701, y = -568.4045, z = 42.2889, h = 340.0000, taken = false, model = -1519439119, hospital = "pillbox", allowCheckIn = false },
    --{ x = 326.7703, y = -571.0762, z = 42.2882, h = 340.0000, taken = false, model = -1519439119, hospital = "pillbox", allowCheckIn = false },
    --{ x = 336.9982, y = -575.2344, z = 42.2717, h = 340.000, taken = false, model = -289946279, hospital = "pillbox", allowCheckIn = false },
    --{ x = 348.621, y = -579.464, z = 42.271, h = 340.000, taken = false, model = -289946279, hospital = "pillbox", allowCheckIn = false },
    --{ x = 361.3602, y = -581.2983, z = 43.11, h = 250.000, taken = false, model = 1631638868, hospital = "pillbox", allowCheckIn = false },
    --{ x = 359.5369, y = -586.2299, z = 43.11, h = 250.000, taken = false, model = 1631638868, hospital = "pillbox", allowCheckIn = false },
    --{ x = 367.0353, y = -581.8009, z = 42.4223, h = 250.0935, taken = false, model = 642527033, hospital = "pillbox", allowCheckIn = false },
    --{ x = 364.9597, y = -585.9413, z = 43.11, h = 250.000, taken = false, model = 1631638868, hospital = "pillbox", allowCheckIn = false },
    --{ x = 363.8029, y = -589.1195, z = 43.11, h = 250.000, taken = false, model = 1631638868, hospital = "pillbox", allowCheckIn = false },
    --{ x = 357.5506, y = -598.1600, z = 42.8449, h = 160.0, taken = false, model = -1091386327, hospital = "pillbox", allowCheckIn = false },
    --{ x = 354.180, y = -592.9996, z = 42.8415, h = 250.000, taken = false, model = -1091386327, hospital = "pillbox", allowCheckIn = false },
    --{ x = 354.4447, y = -600.1856, z = 43.11, h = 250.00, taken = false, model = 1631638868, hospital = "pillbox", allowCheckIn = false },
    --{ x = 346.4822, y = -590.3358, z = 42.8416, h = 70.000, taken = false, model = -1091386327, hospital = "pillbox", allowCheckIn = false },

    { x = 1741.220, y = 3624.570, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },
    { x = 1740.161, y = 3626.825, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },
    { x = 1738.507, y = 3629.286, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },
    { x = 1736.918, y = 3632.282, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },
    { x = 1735.442, y = 3634.938, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },
    { x = 1733.846, y = 3637.610, z = 35.694, h = 121.6, taken = false, model = 835950247, hospital = 'sandy', allowCheckIn = true },


    { x = -267.868, y = 6317.705, z = 32.264, h = 46.139, taken = false, model = 1631638868, hospital = "paleto", allowCheckIn = true },
    { x = -265.927, y = 6319.708, z = 32.264, h = 46.139, taken = false, model = 1631638868, hospital = "paleto", allowCheckIn = true },
    { x = -262.040, y = 6317.671, z = 32.264, h = 224.691, taken = false, model = 1631638868, hospital = "paleto", allowCheckIn = true },
    { x = -260.223, y = 6319.495, z = 32.264, h = 224.691, taken = false, model = 1631638868, hospital = "paleto", allowCheckIn = true },

    { x = 1762.030, y = 2597.652, z = 45.65854, h = 92.009, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },
    { x = 1762.033, y = 2594.657, z = 45.65854, h = 92.009, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },
    { x = 1761.951, y = 2591.497, z = 45.65854, h = 92.009, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },
    { x = 1771.890, y = 2591.885, z = 45.65854, h = -89.926, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },
    { x = 1771.885, y = 2594.892, z = 45.65854, h = -89.926, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },
    { x = 1771.824, y = 2597.941, z = 45.65854, h = -89.926, taken = false, model = 2117668672, hospital = "prison", allowCheckIn = true },

    { x = 4922.606, y = -5286.837, z = 5.229872, h = 90.0, model = 1631638868, hospital = 'cayo', allowCheckIn = true },
    { x = 4922.606, y = -5280.408, z = 5.229872, h = 90.0, model = 2117668672, hospital = 'cayo', allowCheckIn = true },
    { x = 4928.039, y = -5283.415, z = 5.229872, h = 270.0, model = 1631638868, hospital = 'cayo', allowCheckIn = true },
    { x = 4928.039, y = -5289.808, z = 5.229872, h = 270.0, model = 2117668672, hospital = 'cayo', allowCheckIn = true },

    { x = 5932.068, y = -5201.536, z = 86.640, h = 171.467, taken = false, model = 1631638868, hospital = 'lawton', allowCheckIn = true },
    { x = 5936.236, y = -5199.933, z = 86.640, h = 263.078, taken = false, model = 1631638868, hospital = 'lawton', allowCheckIn = true },
    { x = 5934.480, y = -5210.170, z = 86.648, h = 178.480, taken = false, model = 1631638868, hospital = 'lawton', allowCheckIn = true },

    { x = -860.7997, y = 2883.615, z = 21.303, h = 24.0, taken = false, model = 1631638868, hospital = 'morningwood', allowCheckIn = true },
    { x = -857.341, y = 2885.217, z = 21.303, h = 24.0, taken = false, model = 1631638868, hospital = 'morningwood', allowCheckIn = true },
    { x = -853.870, y = 2886.766, z = 21.303, h = 24.0, taken = false, model = 1631638868, hospital = 'morningwood', allowCheckIn = true },
    { x = -850.547, y = 2888.206, z = 21.303, h = 24.0, taken = false, model = 1631638868, hospital = 'morningwood', allowCheckIn = true },
}

local bedsTaken = {}

AddEventHandler('playerDropped', function()
    if bedsTaken[source] ~= nil then
        beds[bedsTaken[source]].taken = false
    end
end)

RegisterServerEvent('mythic_hospital:server:RequestBedOnBehalf')
AddEventHandler('mythic_hospital:server:RequestBedOnBehalf', function(hospital, player)
    local targetSource = player
    local source_character = exports.blrp_core:character(source)
    local target_character = exports.blrp_core:character(targetSource)

    if hospital ~= nil and targetSource ~= nil then
        for k, v in pairs(beds) do
            if not v.taken and v.hospital == hospital and v.allowCheckIn then
                v.taken = true
                bedsTaken[targetSource] = k
                local totalBill = CalculateBill(GetCharsInjuries(targetSource), Config.InjuryBase)
                if source_character.hasPermissionCore('emergency.support') then
                    TriggerClientEvent('mythic_hospital:client:SendToBed', targetSource, k, v)
                else
                  local injuredAccepted = target_character.request("Someone is trying to check you in to hospital, agree?")
                  if injuredAccepted then
                    local accepeted = source_character.request("Pay Medical Fee of $" .. totalBill .. "?", 15)
                    if accepeted then
                      local result = source_character.tryPayment(totalBill)
                      if result then
                        TriggerEvent('blrp_banking:server:siphonAmount', 'HospitalCheckIn', totalBill)
                        TriggerClientEvent('mythic_hospital:client:SendToBed', targetSource, k, v)

                        source_character.log('ACTION', 'Checked player into hospital bed / identifier = ' .. exports.blrp_core:character(targetSource).get('identifier'))
                      end
                    else
                      v.taken = false
                      if bedsTaken[targetSource] ~= nil then
                        bedsTaken[targetSource] = nil
                      end
                    end
                  else
                    source_character.log('ACTION', 'Attempted to check player into hospital bed, player rejected / identifier = ' .. exports.blrp_core:character(targetSource).get('identifier'))
                    v.taken = false
                    if bedsTaken[targetSource] ~= nil then
                      bedsTaken[targetSource] = nil
                    end
                  end
                end

                return
            end
        end

        TriggerClientEvent('mythic_notify:client:SendAlert', source, { type = 'error', text = 'No Beds Available' })
    end
end)

RegisterNetEvent('mythic_hospital:server:lifeflightForceIntoBed', function(hospital, player)
    for k, v in pairs(beds) do
        if not v.taken and v.hospital == hospital and v.allowCheckIn then
            TriggerClientEvent('mythic_hospital:client:SendToBed', player, k, v)
            return
        end
    end
end)

RegisterServerEvent('mythic_hospital:server:RequestBed')
AddEventHandler('mythic_hospital:server:RequestBed', function(hospital)
    local source_character = exports.blrp_core:character(source)

    for k, v in pairs(beds) do
        if not v.taken and v.hospital == hospital and v.allowCheckIn then
            v.taken = true
            bedsTaken[source] = k
            local src = source
            local totalBill = CalculateBill(GetCharsInjuries(src), Config.InjuryBase)
            local accepted = source_character.request("Pay Medical Fee of $" .. totalBill .. "?", 15)

            if accepted then
                local result = source_character.tryPayment(totalBill)
                if result then
                    TriggerEvent('blrp_banking:server:siphonAmount', 'HospitalCheckIn', totalBill)
                    TriggerClientEvent('mythic_hospital:client:SendToBed', src, k, v)
                    exports.blrp_core:character(src).log('ACTION', 'Checked self into hospital bed')
                end
            else
                v.taken = false
                if bedsTaken[source] ~= nil then
                    bedsTaken[source] = nil
                end
            end
            return
        end
    end

    TriggerClientEvent('mythic_notify:client:SendAlert', source, { type = 'error', text = 'No Beds Available' })
end)

RegisterServerEvent('mythic_hospital:server:RPRequestBed')
AddEventHandler('mythic_hospital:server:RPRequestBed', function(plyCoords)
    local foundbed = false
    for k, v in pairs(beds) do
        local distance = #(vector3(v.x, v.y, v.z) - plyCoords)
        if distance < 3.0 then
            if not v.taken then
                v.taken = true
                foundbed = true
                TriggerClientEvent('mythic_hospital:client:RPSendToBed', source, k, v)
                return
            else
                TriggerEvent('chatMessage', '', { 0, 0, 0 }, 'That Bed Is Taken')
            end
        end
    end

    if not foundbed then
        TriggerEvent('chatMessage', '', { 0, 0, 0 }, 'Not Near A Hospital Bed')
    end
end)

RegisterServerEvent('mythic_hospital:server:EnteredBed')
AddEventHandler('mythic_hospital:server:EnteredBed', function()
    local src = source
    --local totalBill = CalculateBill(GetCharsInjuries(src), Config.InjuryBase)


    --if BillPlayer(src, totalBill) then
    --    TriggerClientEvent('mythic_notify:client:SendAlert', src, { text = 'You\'ve Been Treated & Billed', type = 'inform', style = { ['background-color'] = '#760036' }})
    TriggerClientEvent('mythic_hospital:client:FinishServices', src, false, true)
    --else
    --    TriggerClientEvent('mythic_notify:client:SendAlert', src, { text = 'You were revived, but did not have the funds to cover further medical services', type = 'inform', style = { ['background-color'] = '#760036' }})
    --    TriggerClientEvent('mythic_hospital:client:FinishServices', src, false, false)
    --end
end)

RegisterServerEvent('mythic_hospital:server:LeaveBed')
AddEventHandler('mythic_hospital:server:LeaveBed', function(id)
    if not id then
        return
    end
    if not beds[id] then
        beds[id] = {}
    end
    beds[id].taken = false
end)

RegisterServerEvent('mythic_hospital:server:CheckBleedInjury')
AddEventHandler('mythic_hospital:server:CheckBleedInjury', function(player)
    if player ~= nil then
        local injuries = GetCharsInjuries(player)
        if injuries ~= nil then
            for k, v in pairs(injuries.limbs) do
                if v.isDamaged then
                    return true
                end
            end

            if injuries.isBleeding > 0 then
                return true
            end
        end
        return false
    end
end)
