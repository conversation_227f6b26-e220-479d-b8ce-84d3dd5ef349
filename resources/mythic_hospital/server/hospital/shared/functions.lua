--local Proxy = module("vrp", "lib/Proxy")
--local Log = module("vrp", "lib/Log")
--local Tunnel = module("vrp", "panopticon/sv_pano_tunnel")

--vRP = Proxy.getInterface("vRP")
--vRPclient = Tunnel.getInterface("vRP","hospital")
THclient = T.getInstance("mythic_hospital", "hospital")
vRPhs = {}

P.bindInstance('hospital', vRPhs)

--Tunnel.bindInterface("hospital",vRPhs)
--Proxy.addInterface("hospital",vRPhs)
--Tunnel.initiateProxy()

function CalculateBill(injuries, base)
    local totalBill = base
    if injuries ~= nil then
        for k, v in pairs(injuries.limbs) do
            if v.isDamaged then
                totalBill = totalBill + (Config.InjuryBase * v.severity)
            end
        end

        if injuries.isBleeding > 0 then
            totalBill = totalBill + (Config.InjuryBase * injuries.isBleeding)
        end
    end

    return totalBill
end
