RegisterNetEvent("mythic_hospital:items:gauze")
AddEventHandler("mythic_hospital:items:gauze", function()
    TriggerEvent('mythic_hospital:client:FieldTreatBleed')
end)

RegisterNetEvent("mythic_hospital:items:suture")
AddEventHandler("mythic_hospital:items:suture", function()
    TriggerEvent('mythic_hospital:client:RemoveBleed')
end)

RegisterNetEvent("mythic_hospital:items:bandage")
AddEventHandler("mythic_hospital:items:bandage", function(healerId)
  local maxHealth = GetEntityMaxHealth(PlayerPedId())
  local health = GetEntityHealth(PlayerPedId())
  local healAmount = math.floor(maxHealth / 16)
  local newHealth = math.min(maxHealth, health + healAmount)
  newHealth = exports.blrp_core:maxHealth(newHealth)
  SetEntityHealth(PlayerPedId(), newHealth)
  TriggerServerEvent("blrp_core:server:log_heal", healAmount, healerId)
end)

RegisterNetEvent("mythic_hospital:items:firstaid")
AddEventHandler("mythic_hospital:items:firstaid", function()
  local maxHealth = GetEntityMaxHealth(PlayerPedId())
  local health = GetEntityHealth(PlayerPedId())
  local newHealth = math.min(maxHealth, math.floor(health + maxHealth / 8))
  newHealth = exports.blrp_core:maxHealth(newHealth)
  SetEntityHealth(PlayerPedId(), newHealth)
end)

RegisterNetEvent("mythic_hospital:items:dressing")
AddEventHandler("mythic_hospital:items:dressing", function()
  TriggerEvent('mythic_hospital:client:ResetLimbs')
end)

RegisterNetEvent("mythic_hospital:items:medkit")
AddEventHandler("mythic_hospital:items:medkit", function()

  local newHealth = exports.blrp_core:maxHealth(GetEntityMaxHealth(PlayerPedId()))
  SetEntityHealth(PlayerPedId(), newHealth)
  TriggerEvent('mythic_hospital:client:RemoveBleed')
  TriggerEvent('mythic_hospital:client:FieldTreatLimbs')
end)

RegisterNetEvent("mythic_hospital:items:vicodin")
AddEventHandler("mythic_hospital:items:vicodin", function()
    TriggerEvent('mythic_hospital:client:UsePainKiller', 1)
end)

RegisterNetEvent("mythic_hospital:items:hydrocodone")
AddEventHandler("mythic_hospital:items:hydrocodone", function()
  TriggerEvent('mythic_hospital:client:UsePainKiller', 2)
end)

RegisterNetEvent("mythic_hospital:items:morphine")
AddEventHandler("mythic_hospital:items:morphine", function()
    TriggerEvent('mythic_hospital:client:UsePainKiller', 6)
end)
