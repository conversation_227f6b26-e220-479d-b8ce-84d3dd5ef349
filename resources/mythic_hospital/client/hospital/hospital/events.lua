RegisterNetEvent('mythic_hospital:client:RPCheckPos')
AddEventHandler('mythic_hospital:client:RPCheckPos', function()
  if bedOccupyingData == nil then
    TriggerServerEvent('mythic_hospital:server:RPRequestBed', GetEntityCoords(PlayerPedId()))
  else
    TriggerEvent('mythic_hospital:client:FinishServices', false, true)
  end
end)

RegisterNetEvent('mythic_hospital:client:RPSendToBed')
AddEventHandler('mythic_hospital:client:RPSendToBed', function(id, data)
    bedOccupying = id
    bedOccupyingData = data

    SetBedCam()
		TriggerEvent('core:client:reset_ko_count')

    Citizen.CreateThread(function()
      while bedOccupyingData ~= nil do
        Citizen.Wait(1)
        PrintHelpText('Getting up before fully healed will interrupt treatment. ~n~Press ~INPUT_CONTEXT~ to get up')
        if IsControlJustReleased(0, 38) then
          if not exports.blrp_core:me().isInComa() then
            LeaveBed()
          end
        end
      end
    end)
end)

RegisterNetEvent('mythic_hospital:client:SendToBed')
AddEventHandler('mythic_hospital:client:SendToBed', function(id, data)
    pPolice.stopEscorting({})
    bedOccupying = id
    bedOccupyingData = data

    SetBedCam()
		TriggerEvent('core:client:reset_ko_count')

    Citizen.CreateThread(function()
      while bedOccupyingData ~= nil do
        Citizen.Wait(0)
        PrintHelpText('Getting up before fully healed will interrupt treatment. ~n~Press ~INPUT_CONTEXT~ to get up')
        if IsControlJustReleased(0, 38) then
          if not exports.blrp_core:me().isInComa() then
            TriggerServerEvent('core:server:setLifeAlertFlag', false)
            TriggerEvent('mythic_hospital:client:FinishServices', false, true)
          end
        end
      end
    end)
    Citizen.CreateThread(function ()
      Citizen.Wait(5)
      exports.blrp_core.me().notify('Doctors Are Treating You. Getting up before fully healed will interrupt treatment.')
      while bedOccupyingData ~= nil do
        Citizen.Wait(4000)
        currentHealth = GetEntityHealth(PlayerPedId())
        newHealth = currentHealth + 2
        if newHealth > 200 then
          newHealth = 200
        end

        newHealth = exports.blrp_core:maxHealth(newHealth)
        SetEntityHealth(PlayerPedId(),newHealth)

        if newHealth > 199 and exports.blrp_core:me().isInComa() then
          -- TODO log revive event
            TriggerEvent('vrp:client:isRevived')
        end
      end
        pVRP.updateHealth({ GetEntityHealth(PlayerPedId()) })
    end)
end)

RegisterNetEvent('mythic_hospital:client:ForceLeaveBed')
AddEventHandler('mythic_hospital:client:ForceLeaveBed', function()
    LeaveBed()
end)
