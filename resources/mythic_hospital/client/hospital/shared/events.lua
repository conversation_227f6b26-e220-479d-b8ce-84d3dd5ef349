RegisterNetEvent('mythic_hospital:client:FinishServices')
AddEventHandler('mythic_hospital:client:FinishServices', function(h, wasLucky)
  if h and usedHiddenRev then
    return
  end

  local player = PlayerPedId()

  --if IsPedDeadOrDying(player) then
  --  local playerPos = GetEntityCoords(player, true)
  --  NetworkResurrectLocalPlayer(playerPos, true, true, false)
  --end

  if GetEntityHealth(PlayerPedId()) == GetEntityMaxHealth(player) then
    ClearPedBloodDamage(player)
    SetPlayerSprint(PlayerId(), true)
    ResetAll()
  end

  if h then
    usedHiddenRev = true
    DoScreenFadeIn(1000)
  else
    LeaveBed()
    if bedOccupyingData ~= nil and bedOccupying ~= nil then
      TriggerServerEvent("mythic_hospital:server:LeaveBed", bedOccupying)
    end
  end
end)

RegisterNetEvent('mythic_hospital:client:ResetPlayer')
AddEventHandler('mythic_hospital:client:ResetPlayer', function()
  ClearPedBloodDamage(PlayerPedId())
  SetPlayerSprint(PlayerId(), true)
  ResetAll()
end)
