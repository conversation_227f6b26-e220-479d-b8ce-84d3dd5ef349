function IsDamagingEvent(damageDone, weapon)
    math.randomseed(GetGameTimer())
    local luck = math.random(100)
    local multi = damageDone / Config.HealthDamage

    return luck < (Config.HealthDamage * multi) or (damageDone >= Config.ForceInjury or multi > Config.MaxInjuryChanceMulti or Config.ForceInjuryWeapons[weapon])
end

function IsInjuryCausingLimp()
    for k, v in pairs(BodyParts) do
        if v.causeLimp and v.isDamaged then
            return true
        end
    end

    return false
end

exports('IsInjuryCausingLimp', IsInjuryCausingLimp)

function IsInjuredOrBleeding()
    if isBleeding > 0 then
        return true
    else
        for k, v in pairs(BodyParts) do
            if v.isDamaged then
                return true
            end
        end
    end

    return false
end

exports('IsInjuredOrBleeding', IsInjuredOrBleeding)

function GetDamagingWeapon(ped)
    for k, v in pairs(Config.Weapons) do
        if HasPedBeenDamagedByWeapon(ped, k, 0) then
            ClearEntityLastDamageEntity(ped)
            return v
        end
    end

    return nil
end

function ResetAll()
    isBleeding = 0
    bleedTickTimer = 0
    advanceBleedTimer = 0
    fadeOutTimer = 0
    blackoutTimer = 0
    onDrugs = 0
    wasOnDrugs = false
    onPainKiller = 0
    wasOnPainKillers = false
    injured = {}

    for k, v in pairs(BodyParts) do
        v.isDamaged = false
        v.severity = 0
    end

    TriggerServerEvent('mythic_hospital:server:SyncInjuries', {
        limbs = BodyParts,
        isBleeding = tonumber(isBleeding)
    })

    ProcessRunStuff(PlayerPedId())
    DoLimbAlert()
    DoBleedAlert()

    TriggerServerEvent('mythic_hospital:server:SyncInjuries', {
        limbs = BodyParts,
        isBleeding = tonumber(isBleeding)
    })

end

isInComa_cached = false

Citizen.CreateThread(function()
    while true do
      Citizen.Wait(500)
      isInComa_cached = exports.blrp_core:me().isInComa()
    end
end)

function ProcessRunStuff(ped)
    if not isInComa_cached and IsInjuryCausingLimp() and not (onPainKiller > 0)  then
        RequestAnimSet("move_m@injured")
        while not HasAnimSetLoaded("move_m@injured") do
            Citizen.Wait(0)
        end
        SetPedMovementClipset(ped, "move_m@injured", 1 )
        SetPlayerSprint(PlayerId(), false)

        if wasOnPainKillers then
            -- SetPedToRagdoll(ped, 1500, 2000, 3, true, true, false)
            wasOnPainKillers = false
            exports['mythic_notify']:SendAlert('inform', Config.Strings.PainKillersExpired, 5000, { ['background-color'] = '#760036' })
        end
    else
        SetPedMoveRateOverride(ped, 1.0)
        ResetPedMovementClipset(ped, 1.0)

        if not wasOnPainKillers and (onPainKiller > 0) then wasOnPainKillers = true end

        if onPainKiller > 0 then
            onPainKiller = onPainKiller - 1
        end
    end
end

function ProcessDamage(ped)
    if not isInComa_cached or not (onDrugs > 0) then
        for k, v in pairs(injured) do
            if (v.part == 'LLEG' and v.severity > 1) or (v.part == 'RLEG' and v.severity > 1) or (v.part == 'LFOOT' and v.severity > 2) or (v.part == 'RFOOT' and v.severity > 2) then
                if legCount >= Config.LegInjuryTimer then
                    if not IsPedRagdoll(ped) and IsPedOnFoot(ped) then
                        local chance = math.random(100)
                        if (IsPedRunning(ped) or IsPedSprinting(ped)) then
                            if chance <= Config.LegInjuryChance.Running then
                              --  exports['mythic_notify']:SendAlert('inform', Config.Strings.InjurNoRun, 5000, { ['background-color'] = '#760036' })
                               -- ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.08) -- change this float to increase/decrease camera shake
                              --  SetPedToRagdollWithFall(ped, 1500, 2000, 1, GetEntityForwardVector(ped), 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
                            end
                        else
                            if chance <= Config.LegInjuryChance.Walking then
                              --  exports['mythic_notify']:SendAlert('inform', Config.Strings.InjurStumble, 5000, { ['background-color'] = '#760036' })
                               -- ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.08) -- change this float to increase/decrease camera shake
                              --  SetPedToRagdollWithFall(ped, 1500, 2000, 1, GetEntityForwardVector(ped), 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
                            end
                        end
                    end
                    legCount = 0
                else
                    legCount = legCount + 1
                end
            elseif (v.part == 'LARM' and v.severity > 1) or (v.part == 'LHAND' and v.severity > 1) or (v.part == 'LFINGER' and v.severity > 2) or (v.part == 'RARM' and v.severity > 1) or (v.part == 'RHAND' and v.severity > 1) or (v.part == 'RFINGER' and v.severity > 2) then
                if armcount >= Config.ArmInjuryTimer then
                    local chance = math.random(100)

                    if (v.part == 'LARM' and v.severity > 1) or (v.part == 'LHAND' and v.severity > 1) or (v.part == 'LFINGER' and v.severity > 2) then
                        local isDisabled = 15
                        Citizen.CreateThread(function()
                            while isDisabled > 0 do
                                if IsPedInAnyVehicle(ped, true) then
                                    DisableControlAction(0, 63, true) -- veh turn left
                                end

                                if IsPlayerFreeAiming(PlayerId()) then
                                    DisablePlayerFiring(PlayerId(), true) -- Disable weapon firing
                                end

                                isDisabled = isDisabled - 1
                                Citizen.Wait(1)
                            end
                        end)
                    else
                        local isDisabled = 15
                        Citizen.CreateThread(function()
                            while isDisabled > 0 do
                                if IsPedInAnyVehicle(ped, true) then
                                    DisableControlAction(0, 63, true) -- veh turn left
                                end

                                if IsPlayerFreeAiming(PlayerId()) then
                                    DisableControlAction(0, 25, true) -- Disable weapon firing
                                end

                                isDisabled = isDisabled - 1
                                Citizen.Wait(1)
                            end
                        end)
                    end

                    armcount = 0
                else
                    armcount = armcount + 1
                end
            elseif (v.part == 'HEAD' and v.severity > 2) then
                if headCount >= Config.HeadInjuryTimer then
                    local chance = math.random(100)

                    if chance <= Config.HeadInjuryChance then
                        --exports['mythic_notify']:SendAlert('inform', Config.Strings.Blackout, 5000, { ['background-color'] = '#760036' })
                        -- SetFlash(0, 0, 100, 10000, 100)

                        -- DoScreenFadeOut(100)
                        -- while not IsScreenFadedOut() do
                        --     Citizen.Wait(0)
                        -- end

                        if not IsPedRagdoll(ped) and IsPedOnFoot(ped) and not IsPedSwimming(ped) then
                          --  ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.08) -- change this float to increase/decrease camera shake
                          --  SetPedToRagdoll(ped, 5000, 1, 2)
                        end

                        --Citizen.Wait(5000)
                        --DoScreenFadeIn(250)
                    end
                    headCount = 0
                else
                    headCount = headCount + 1
                end
            end
        end

        if wasOnDrugs then
            SetPedToRagdoll(ped, 1500, 2000, 3, true, true, false)
            wasOnDrugs = false
            exports['mythic_notify']:SendAlert('inform', Config.Strings.AdrenalineExpired, 5000, { ['background-color'] = '#760036' })
        end
    else
        onDrugs = onDrugs - 1

        if not wasOnDrugs then
            wasOnDrugs = true
        end
    end
end

-- TODO anything calling this?
function DebugAlerts(ped, bone, weapon, damageDone)
    print(weapon)
    exports['mythic_notify']:SendAlert('inform', 'Bone: ' .. Config.Bones[bone], 10000, { ['background-color'] = '#1e1e1e' })
    if (Config.MinorInjurWeapons[weapon] ~= nil) then
        exports['mythic_notify']:SendAlert('inform', 'Minor Weapon : ' .. weapon, 10000, { ['background-color'] = '#1e1e1e' })
    else
        exports['mythic_notify']:SendAlert('inform', 'Major Weapon : ' .. weapon, 10000, { ['background-color'] = '#1e1e1e' })
    end
    exports['mythic_notify']:SendAlert('inform', 'Crit Area: ' .. tostring(Config.CriticalAreas[Config.Bones[bone]] ~= nil), 10000, { ['background-color'] = '#1e1e1e' })
    exports['mythic_notify']:SendAlert('inform', 'Stagger Area: ' .. tostring(Config.StaggerAreas[Config.Bones[bone]] ~= nil and (Config.StaggerAreas[Config.Bones[bone]].armored or GetPedArmour(ped   ) <= 0)), 10000, { ['background-color'] = '#1e1e1e' })
    exports['mythic_notify']:SendAlert('inform', 'Dmg Done: ' .. damageDone, 10000, { ['background-color'] = '#1e1e1e' })
end

function CheckDamage(ped, bone, weapon, damageDone)
    if weapon == nil then return end
    if Config.Bones[bone] ~= nil and not IsEntityDead(ped) then
        if Config.Debug then
            DebugAlerts(ped, bone, weapon, damageDone)
        end

        ApplyImmediateEffects(ped, bone, weapon, damageDone)

        if not BodyParts[Config.Bones[bone]].isDamaged then
            BodyParts[Config.Bones[bone]].isDamaged = true
            BodyParts[Config.Bones[bone]].severity = 1
            table.insert(injured, {
                part = Config.Bones[bone],
                label = BodyParts[Config.Bones[bone]].label,
                severity = BodyParts[Config.Bones[bone]].severity
            })
        else
            if BodyParts[Config.Bones[bone]].severity < 4 then
                BodyParts[Config.Bones[bone]].severity = BodyParts[Config.Bones[bone]].severity + 1

                for k, v in pairs(injured) do
                    if v.part == Config.Bones[bone] then
                        v.severity = BodyParts[Config.Bones[bone]].severity
                    end
                end
            end
        end

        TriggerServerEvent('mythic_hospital:server:SyncInjuries', {
            limbs = BodyParts,
            isBleeding = tonumber(isBleeding)
        })

        ProcessRunStuff(ped)
        DoLimbAlert()
        DoBleedAlert()
    else
        if not IsEntityDead(ped) then
            print('Bone Not In Index - Report This! - ' .. bone)
        end
    end
end

function ApplyImmediateEffects(ped, bone, weapon, damageDone)
    local armor = GetPedArmour(ped)

    if Config.MinorInjurWeapons[weapon] and damageDone < Config.DamageMinorToMajor then
        if Config.CriticalAreas[Config.Bones[bone]] then
            if armor <= 0 then
                ApplyBleed(1)
            end
        end
        --if Config.StaggerAreas[Config.Bones[bone]] ~= nil and (Config.StaggerAreas[Config.Bones[bone]].armored or armor <= 0) then
        --    if math.random(100) <= math.ceil(Config.StaggerAreas[Config.Bones[bone]].minor) then
        --        SetPedToRagdoll(ped, 1500, 2000, 3, true, true, false)
        --    end
        --end
    elseif Config.MajorInjurWeapons[weapon] or (Config.MinorInjurWeapons[weapon] and damageDone >= Config.DamageMinorToMajor) then
        if Config.CriticalAreas[Config.Bones[bone]] ~= nil then
            if armor > 0 and Config.CriticalAreas[Config.Bones[bone]].armored then
                if math.random(100) <= math.ceil(Config.MajorArmoredBleedChance) then
                    ApplyBleed(1)
                end
            else
                ApplyBleed(1)
            end
        else
            if armor > 0 then
                if math.random(100) < (Config.MajorArmoredBleedChance) then
                    ApplyBleed(1)
                end
            else
                if math.random(100) < (Config.MajorArmoredBleedChance * 2) then
                    ApplyBleed(1)
                end
            end
        end

        --if Config.StaggerAreas[Config.Bones[bone]] ~= nil and (Config.StaggerAreas[Config.Bones[bone]].armored or armor <= 0) then
        --    if math.random(100) <= math.ceil(Config.StaggerAreas[Config.Bones[bone]].major) then
        --        SetPedToRagdoll(ped, 1500, 2000, 3, true, true, false)
        --    end
        --end
    end
end

function ApplyBleed(level)
    if isBleeding ~= 3 then
        if isBleeding + level > 3 then
            isBleeding = 3
        else
            isBleeding = isBleeding + level
        end

        DoBleedAlert()
    end
end

function DoLimbAlert()
    local player = PlayerPedId()
    if not IsEntityDead(player) then
        if #injured > 0 then
            local limbDamageMsg = ''
            if #injured <= Config.AlertShowInfo then
                for k, v in pairs(injured) do
                    limbDamageMsg = string.format(Config.Strings.LimbAlert, v.label, Config.WoundStates[v.severity])
                    if k < #injured then
                        limbDamageMsg = limbDamageMsg .. Config.Strings.LimbAlertSeperator
                    end
                end
            else
                limbDamageMsg = Config.Strings.LimbAlertMultiple
            end

            --exports['mythic_notify']:PersistentAlert('start', limbNotifId, 'inform', limbDamageMsg, { ['background-color'] = '#b55255d9' })
        else
            --exports['mythic_notify']:PersistentAlert('end', limbNotifId)
        end
    else
        --exports['mythic_notify']:PersistentAlert('end', limbNotifId)
    end
end

function DoBleedAlert()
    local player = PlayerPedId()
    if not IsEntityDead(player) and isBleeding > 0 then
        exports['mythic_notify']:PersistentAlert('start', "bleedNotifId", 'inform', string.format(Config.Strings.BleedAlert, Config.BleedingStates[isBleeding]), { ['background-color'] = '#b55255d9' })
    else
        exports['mythic_notify']:PersistentAlert('end', "bleedNotifId")
    end
end
