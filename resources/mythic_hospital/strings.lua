Config = Config or {}

Config.Strings = {
    HospitalCheckIn = 'Press ~r~[E] ~s~To Check Yourself In',
    HospitalCheckInBehalf = 'Press ~r~[Q] ~s~To Check Patient In',
    HospitalCheckInAction = 'Checking In',
    HiddenCheckIn = 'Press ~r~[E] ~s~To Revive',
    HiddenCheckInAction = 'Press ~r~[E] ~s~To Revive',
    HiddenCooldown = 'The medic is not here',
    NotHurt = 'You do not need medical attention',
    BeingTreated = 'Doctors Are Treating You',
    GetUp = 'Get Up',

    Blackout = 'You Suddenly Black Out',
    InjurNoRun = 'You\'re Having A Hard Time Running',
    InjurStumble = 'You\'re Having A Hard Using Your Legs',

    UsePainKillers = 'You feel the pain subside temporarily',
    PainKillersExpired = 'You\'ve Realized Doing Drugs Does Not Fix All Your Problems',
    UseAdrenaline = 'You\'re Able To Ignore Your Body Failing',
    AdrenalineExpired = 'You\'ve Realized Doing Drugs Does Not Fix All Your Problems',

    LimbAlert = 'Your %s feels %s',
    LimbAlertSeperator = ' | ',
    LimbAlertMultiple = 'You Feel Multiple Pains',

    BleedAlert = 'You Are %s',
    --[[
    TeleportEnter = 'Press ~r~[E] ~s~to enter Pillbox Hospital',
    TeleportRoof = 'Press ~r~[E] ~s~to go to Pillbox roof',
    TeleportLower = 'Press ~r~[E] ~s~to go to lower Pillbox Entrance',
    --]]
}
