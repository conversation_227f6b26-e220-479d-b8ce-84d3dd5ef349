Config = Config or {}
Config.Debug = false

-- Keys
Config.Keys = {}
Config.Keys.GetUp = 38 -- Key Used To Get Out Of Bed When Using /bed Command
Config.Keys.Revive = 54 -- Key Used To Revive Or Teleport

--[[
    GENERAL SETTINGS | THESE WILL AFFECT YOUR ENTIRE SERVER SO BE SURE TO SET THESE CORRECTLY
    MaxHp : Maximum HP Allowed, set to -1 if you want to disable mythic_hospital from setting this
        NOTE: Anything under 100 and you are dead
    RegenRate :
]]
Config.MaxHp = 200
Config.RegenRate = 0.0

--[[
    HiddenRevChance : The % Chance That Using The Hidden Revive Spot Will Result In A Full Revive With All Limb Damage & Bleeds Removed
    HiddenCooldown : The time, in minutes, for how long a player must wait before using the hidden revive spot again
]]
Config.HiddenRevChance = 65
Config.HiddenCooldown = 45

--[[
    Pricing
]]
Config.InjuryBase = 500
Config.HiddenInjuryBase = 10000

--[[
    AlertShowInfo :
]]
Config.AlertShowInfo = 2

--[[

]]
Config.Hospital = {
  ["pillbox"] = {
    Location = { x = 310.071, y = -582.516, z = 43.268, h = 180.********** },
    CheckInBehalf = { x = 310.330, y = -587.346, z = 43.268 },
    ShowBlip = false,
    Blip = {
      name = "Pillbox Medical Center",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = 308.06161499023,
      y = -595.19683837891,
      z = 43.291839599609
    }
  },
  ["pillboxlower"] = {
    Location = { x = 353.355, y = -592.209, z = 28.847, h = 180.********** },
    CheckInBehalf = { x = 352.183, y = -588.567, z = 28.847 },
    ShowBlip = false,
    Blip = {
      name = "Pillbox Medical Center",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = 308.06161499023,
      y = -595.19683837891,
      z = 43.291839599609
    }
  },
  ["sandy"] = {
    Location = { x = 1767.577, y = 3639.920, z = 34.853, h = 180.********** },
    CheckInBehalf = { x = 1765.256, y = 3641.928, z = 34.853 },
    ShowBlip = false,
    Blip = {
      name = "Sandy Shores Medical Center",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = 1836.**********,
      y = 3680.**********,
      z = 34.270038604736
    }
  },
  ["paleto"] = {
    Location = { x=-255.27424621582, y=6329.**********, z=32.408863067627, h = 180.********** },
    CheckInBehalf = {x = -257.73870849609, y = 6329.**********, z = 32.408863067627},
    ShowBlip = false,
    Blip = {
      name = "Paleto Medical Center",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = -255.27424621582,
      y = 6329.**********,
      z = 32.408863067627
    }
  },
  ["prison"] = {
    Location = { x = 1768.791015625, y = 2570.**********, z = 45.729797363281, h = 180.********** },
    CheckInBehalf = { x = 1767.**********, y = 2572.**********, z = 45.729797363281 },
    ShowBlip = false,
    Blip = {
      name = "Bolingbroke Clinic",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = 1768.791015625,
      y = 2570.**********,
      z = 45.729797363281
    }
  },
--   ["cayo"] = {
--     Location = { x = 4926.842, y = -5293.268, z = 5.682, h = 180.0 },
--     CheckInBehalf = { x = 4923.160, y = -5291.637, z = 5.682, h = 180.0 },
--     ShowBlip = false,
--     Blip = {
--       name = "Cayo Perico Field Hospital",
--       color = 38,
--       id = 153,
--       scale = 0.7,
--       short = false,
--       x = 1768.791015625,
--       y = 2570.**********,
--       z = 45.729797363281
--     }
--   },
--  ["lawton"] = {
--    Location = { x = 5938.430, y = -5190.754, z = 85.726 },
--    CheckInBehalf = { x = 5935.891, y = -5192.613, z = 85.726 },
--    ShowBlip = false,
--    Blip = {
--      name = "Lawton Junction Medical Center",
--      color = 38,
--      id = 153,
--      scale = 0.7,
--      short = false,
--      x = 1768.791015625,
--      y = 2570.**********,
--      z = 45.729797363281,
--    },
--  },
  ["morningwood"] = {
    Location = { x = -853.317, y = 2881.988, z = 21.741 },
    CheckInBehalf = { x = -856.465, y = 2880.594, z = 21.741 },
    ShowBlip = false,
    Blip = {
      name = "Lago Zancudo Medical Bunker",
      color = 38,
      id = 153,
      scale = 0.7,
      short = false,
      x = -856.947,
      y = 2880.780,
      z = 21.741,
    },
  },
}
--[[
    Hidden: Location of the hidden location where you can heal and no alert of GSW's will be made.
]]

Config.Hidden = {
    Location = { x = 1969.**********, y = 3815.**********, z = 33.428680419922 },
    ShowBlip = false,
}
Config.Hidden.Blip = { name = 'Hidden Medic', color = 12, id = 153, scale = 1.0, short = false, x = Config.Hidden.Location.x, y = Config.Hidden.Location.y, z = Config.Hidden.Location.z }

Config.HospitalCheckin = {
  {x = 309.676, y = -582.167, z = 43.268, h = 67.479, location = "pillbox"},
  {x = 353.355, y = -592.209, z = 28.847, location = "pillboxlower"},
  {x = 1767.577, y = 3639.920, z = 34.853, h = 67.479, location = "sandy"},
  {x = -370.40383911133, y = 6108.**********, z = 31.850894927979, h = 67.479, location = "paleto"},
  {x = 1679.**********, y = 2580.**********, z = 45.911521911621, h = 180.**********, location = "prison"},
  {x = 5180.886, y = -5148.673, z = 3.450, h = 80.440, location = "cayo"},
  {x = 5938.430, y = -5190.754, z = 85.726, h = 33.614, location = "lawton" },
}

Config.Teleports = {
    { x = 298.57528686523, y = -599.33715820313, z = 43.292068481445, h = 338.03997802734, destination = 3, range = 2, text = Config.Strings.TeleportLower },
    { x = 309.68832397461, y = -602.75939941406, z = 43.291839599609, h = 67.832542419434, destination = 4, range = 2, text = Config.Strings.TeleportRoof },
    { x = 357.58139038086, y = -590.75146484375, z = 28.788959503174, h = 245.51211547852, destination = 1, range = 5, text = Config.Strings.TeleportEnter },
    { x = 338.8362121582, y = -583.79595947266, z = 74.165649414063, h = 247.53303527832, destination = 2, range = 2, text = Config.Strings.TeleportEnter },
}
