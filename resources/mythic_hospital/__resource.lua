resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'

description 'Mythic Framework Hospital & Damage System'

version '2.0.0'

client_scripts {
	"@vrp/lib/utils.lua",
	--"@vrp/client/Tunnel.lua",
	--"@vrp/client/Proxy.lua",
	"@vrp/lib/clientUtils.lua",

	'@blrp_rpc/tunnel/client.lua',
	'@blrp_rpc/proxy/client.lua',

	-- Config Files
	'strings.lua',
	'config.lua',
	'definitions.lua',
	'functional_config.lua',
	'client/scaleform.lua',

	'client/shared/defines.lua',
	'client/shared/functions.lua',
	'client/shared/threads.lua',

	-- Wound Files
	'client/wound/functions.lua',
	'client/wound/events.lua',
	'client/wound/threads.lua',

	-- Hospital Files
	'client/hospital/shared/events.lua',
	'client/hospital/hospital/functions.lua',
	'client/hospital/hospital/teleports.lua',
	'client/hospital/hospital/events.lua',
	'client/hospital/hospital/threads.lua',
	'client/hospital/hidden/events.lua',
	'client/hospital/hidden/threads.lua',

	'client/items.lua',
}

server_scripts {
	"@vrp/lib/utils.lua",

  '@blrp_rpc/tunnel/server.lua',
  '@blrp_rpc/proxy/server.lua',

	'strings.lua',
	'config.lua',
	'server/billing.lua',
	'server/wound.lua',

	'server/hospital/shared/defines.lua',
	'server/hospital/shared/functions.lua',
	'server/hospital/hospital/events.lua',
	'server/hospital/hidden/events.lua',
	'server/hospital/hidden/threads.lua',
}

dependencies {
	'vrp',
	'customscripts',
	'mythic_progbar',
	'mythic_notify',
}

exports {
	'IsInjuredOrBleeding',
	'DoLimbAlert',
	'DoBleedAlert',
	'ResetAll'
}

server_exports {
    'GetCharsInjuries',
}
