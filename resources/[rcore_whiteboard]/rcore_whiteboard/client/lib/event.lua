AddEventHandler('rcore_whiteboard:editWhiteboard', function(entity, entity_data)
    local whiteboard, index = getSpawnedWhiteboardByCoords(GetEntityCoords(entity))

    local can = checkPermission(whiteboard.whiteboard.id)
    dbg.debug('Can edit whiteboard %s %s', whiteboard.whiteboard.id, can)
    if not can then
        showNotification(_U('non_permission'))
        return
    end

    editedWhiteboardIndex = index
    openEditor(getLoadedData(whiteboard.whiteboard.id))
end)