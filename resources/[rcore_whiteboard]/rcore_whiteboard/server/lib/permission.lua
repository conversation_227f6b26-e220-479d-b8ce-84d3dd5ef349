function canChangeWhiteboard(playerId, boardId)
    local board = findWhiteboardById(boardId)
    local character = exports.blrp_core:character(playerId)
    if board then
        if board.readOnly then
            dbg.security('Board %s is readonly %s tried to edit it', playerId)
            return false
        end

        --For admins
--         if Ace.Can(playerId, Permissions.EDIT_ALL) then
--             dbg.security('Override permission for %s - has EDIT_ALL permission', playerId)
--             return true
--         end

        if board.ace then
            if Ace.Can(playerId, whiteboardEditAce(boardId)) then
                dbg.security('Override permission for %s - has %s permission', playerId, whiteboardEditAce(boardId))
                return true
            end
        end


        if character then
            local is_admin = character.hasPermissionCore('admin.edit_whiteboard')
            if board.jobs and type(board.jobs) == "table" and table.len(board.jobs) > 0 then
              for _, group in pairs(board.jobs) do
                if character.hasOrInheritsGroup(group) then
                  return true
                end
              end
              if is_admin and character.request('ATTENTION: You are editing this whiteboard with staff permissions. Proceed?', 30) then
                character.log('ADMIN', 'Admin edit on whiteboard ' .. board.id)
                return true
              end
            else
                --Has no jobs parameter or its empty
                return true
            end

        else
            dbg.critical('Cannot find job for %s', playerId)
        end
    else
        dbg.critical('Cannot find whiteboard %s', boardId)
    end
    character.notify('You don\'t have permission to access this whiteboard')
    return false
end

RegisterNetEvent(triggerName('can'), function(boardId)
    local playerId = source
    local can = canChangeWhiteboard(playerId, boardId)
    dbg.debug('Can change whiteboard %s %s', boardId, can)
    TriggerClientEvent(triggerName('can'), playerId, boardId, can)
end)
