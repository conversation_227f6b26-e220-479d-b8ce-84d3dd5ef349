function sendCustomDiscordMessage(webhook, name, message, color, footer, image)
    footer = footer or "rcore_report | rcore.cz"
    color = color or SConfig.DiscordColors.Grey

    if not webhook or webhook == "WEBHOOK_HERE" then
        return
    end

    local embeds = {
        {
            ["title"] = name,
            ["description"] = message,
            ["type"] = "rich",
            ["color"] = color,
            ["footer"] = {
                ["text"] = footer,
            },
            ["image"] = {
                ["url"] = image,
            }
        }
    }

    PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({ username = name, embeds = embeds }), { ['Content-Type'] = 'application/json' })
end

local function findDiscordIdentifier(source)
    local discordId
    for _, id in ipairs(GetPlayerIdentifiers(source)) do
        if string.match(id, "discord:") then
            discordId = string.gsub(id, "discord:", "")
        end
    end
    return discordId
end

local function getNiceName(playerId)
    local discordLicense = findDiscordIdentifier(playerId)
    if discordLicense then
        return string.format('%s <@%s>', GetPlayerName(playerId), discordLicense)
    end

    return nil
end

AddEventHandler(triggerName('updated'), function(boardId, playerId)
    local character = exports.blrp_core:character(playerId)
    dbg.debug('Whiteboard %s updated by %s', boardId, playerId)
    local board = findWhiteboardById(boardId)
    if not board then
        dbg.critical('Cannot find whiteboard %s', boardId)
        return
    end

    local player = getNiceName(playerId)

    local message = string.format('**%s** (%s) edited whiteboard **%s** at coords %s', player, character.get('identifier'), board.id, GetEntityCoords(GetPlayerPed(playerId)))
    sendCustomDiscordMessage(SConfig.Webhook, 'rcore_whiteboard', message, 0xff5050, 'rcore_whiteboard | rcore.cz')
    character.log(message)
end)