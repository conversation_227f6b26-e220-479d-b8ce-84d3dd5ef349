{"version": 3, "sources": ["../src/toolbars/DrawerToolbar.css", "../src/toolbars/ui-plugins/ToolbarComboBox.css", "../src/toolbars/ui-plugins/ToolbarTooltip.css", "../src/plugins/feature-canvas-properties/CanvasProperties.css", "../src/plugins/feature-fullscreen/FullscreenModeButton.css", "../src/plugins/feature-image-crop/ImageCropPlugin.css", "../src/plugins/feature-minimize-button/MinimizeButton.css", "../src/plugins/feature-overcanvas-popup/OvercanvasPopup.css", "../src/plugins/feature-resize/Resize.css", "../src/plugins/feature-shape-contextmenu/ShapeContextMenu.css", "../src/plugins/feature-zoom/Zoom.css", "../src/plugins/option-brushSize/BrushSize.css", "../src/plugins/option-brushSize/BrushSize.touch.css", "../src/plugins/option-color/Colorpicker.css", "../src/plugins/option-color/OpacityControl.css", "../src/plugins/option-colorpicker-html5/ColorpickerHtml5.css", "../src/plugins/option-line-width/LineWidth.css", "../src/plugins/option-opacity/Opacity.css", "../src/plugins/option-stroke-width/StrokeWidth.css", "../src/plugins/option-text-styles/TextStyles.css", "../src/plugins/options-shape-border/ShapeBorder.css", "../src/plugins/shape-arrow/Arrow.css", "../src/plugins/shape-line/Line.css", "../src/plugins/shape-polygon/Polygon.css", "../src/plugins/shape-triangle/Triangle.css", "../src/Drawer.css"], "names": [], "mappings": ";;;AAAA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AACzB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC7B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,UAAU,CAAC,CAAC,OAAO,CAAC;AACxB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1B,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACnD,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;AACzC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;AAC5B,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AACjD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACnD,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AACZ,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACtD,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7C,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC3C,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/D,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAC;AACtB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACxF,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACtF,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACzF,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvF,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACnC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;AAC9B,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,GAAG;AACrB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACzB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9F,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC1B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAClB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC5B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAClC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AACnB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7C,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7C,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACnE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACrE,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;AACzB,IAAI,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACxC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AACzB,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACtC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACpE,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACxF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACrE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AACtB,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACtE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AACxB;AACA,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AACnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC3D,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC3C,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AAChD,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACnB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACvD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACzC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACtC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACrC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACjC,CAAC;AACD;AACA;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AAChC,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACzC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACtC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACrC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;AACjC,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AAChC,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrD,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACxD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1E,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AAC9E,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC;AACX,IAAI,KAAK,CAAC,CAAC,CAAC;AACZ,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AACjF,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,IAAI,CAAC,CAAC,CAAC;AACX,IAAI,KAAK,CAAC,CAAC,CAAC;AACZ,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/E,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,IAAI,CAAC,CAAC,CAAC;AACX,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AAChF,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,KAAK,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACjC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AACzD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAC;AACtB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AAClF,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtF,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACnD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC1E,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC1C,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;AAClG,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC;AAC/B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AACpC,CAAC;AACD;AACA,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB;AACA,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;AACpC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA;AACA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrD,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;AACjD,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChD,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE;AACtF,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5B,CAAC;AACD;AACA,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtD,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1G,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5B,CAAC;AACD;AACA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvF,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClD,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtD,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzG,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AACnD,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1E,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtD,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AACjD,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,CAAC;AACD;AACA,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtD,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9H,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACjD,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACrF,IAAI,OAAO,GAAG,IAAI,EAAE;AACpB,CAAC;AACD,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnF,IAAI,OAAO,GAAG,IAAI,EAAE;AACpB,CAAC;AACD;AACA,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC/C,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnF,IAAI,OAAO,GAAG,IAAI,EAAE;AACpB,CAAC;AACD,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACjF,IAAI,OAAO,GAAG,IAAI,EAAE;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;AAChC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB;AACA,IAAI,UAAU,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9C,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3B,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACnC,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA,mCAAmC;AACnC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjD,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,CAAC;AACD;AACA;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AACtC,CAAC;AACD,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvF,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClJ,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzJ,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjH,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpF,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/F,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7E,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5B,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACjD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AAChC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5C,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;AACrC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC7B;AACA,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACnD,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5B;AACA,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB;AACA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3E,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,CAAC;AACD,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClF,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9D,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;AACtC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1D,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AACzB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB;AACA,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC3F,IAAI,UAAU,CAAC,CAAC,WAAW,CAAC;AAC5B;AACA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;AAC9E,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC;AAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB;AACA,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;AACjC,IAAI,KAAK,CAAC,CAAC,WAAW,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClE,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClE,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,C;AChuBA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B;AACA,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB;AACA,IAAI,UAAU,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9C,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC3B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,MAAM;AACN;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7C;AACA,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC1E;AACA,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC5D;AACA,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxE;AACA,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC7D,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzE;AACA,CAAC;AACD;AACA,MAAM;AACN;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd;AACA,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG;AAClC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;AACnB,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvE,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB;AACA,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACxB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACtE,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC1E,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACtC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACnC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AACnB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC;AACA,CAAC;AACD;AACA,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC;AACnG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AACnG,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,aAAa;AACb;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAChD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC3D,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AACtD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC/D,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC1D,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACjF,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,C;ACnOA,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACtB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC1B,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AAC9B,IAAI,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC;AACnC,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACrB,IAAI,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,OAAO,CAAC,CAAC,GAAG;AAChB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACxB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,IAAI,MAAM,CAAC,CAAC,CAAC;AACb;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAChC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,IAAI,GAAG,CAAC,CAAC,CAAC;AACV;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnE,IAAI,KAAK,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,CAAC,CAAC,CAAC;AACV;AACA;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAChE,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAChC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AAChC;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AAChC;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACjE,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AAChC;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpE,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AAChC;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AACjC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC;AAC5D,C;AChIA,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;AAC/B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAClC,C;ACLA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AAC/B,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACtB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACxB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACzB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AAChC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAChC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACpB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;AAClE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AACzD,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;AACpE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;AACnE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AACzD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC7B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACtD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC7B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACnD,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC;AAC1C,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC7C,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAChC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC/D,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC1B,C;ACrCA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC3B,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC3B,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACnB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACrD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAC/B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AAC7B,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACrB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC;AAC1B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;AAC5B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE;AACrC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE;AAClC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE;AACjC,IAAI,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE;AAC7B,CAAC;AACD;AACA,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACzD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACtD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACrD,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACjD,IAAI,KAAK,CAAC,CAAC,OAAO,CAAC;AACnB,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC/B,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;AAC7B,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACzC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACrC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AACpC,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG;AAChC,CAAC;AACD;AACA,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA,uCAAuC;AACvC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY;AACvC,uCAAuC;AACvC,EAAE,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACtF,uCAAuC;AACvC;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACZ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;AACrC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAChF,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC;AAClC,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,EAAE;AACF,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACjB,CAAC;AACD,EAAE;AACF;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;AACtC,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AACzB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,WAAW,CAAC;AAC5B,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC3F,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;AACzG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;AACrD,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACnC,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,qCAAqC;AACrC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpB,qCAAqC;AACrC,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC,IAAI;AACjB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC,IAAI;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACX,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACrB,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACzD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACtD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACrD,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACjD,CAAC;AACD;AACA,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACd,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;AACtC,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;AACnC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACd,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;AACjD,IAAI,QAAQ,CAAC,QAAQ,CAAC;AACtB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,CAAC;AACD;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,C;AClYA,CAAC,SAAS,CAAC,CAAC;AACZ,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC;AACnF,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACtB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AAC7B,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AACjE,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;AACjD,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;AACxC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACpC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACjD,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrF,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;AACvB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,C;AC1CA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAChB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AACtC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACzD,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,IAAI;AACJ,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM;AACf,CAAC,EAAE;AACH,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AACzD,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC1D,IAAI,OAAO,CAAC,CAAC,GAAG;AAChB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACxB;AACA,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvE,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AACzB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5E,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACnC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5E,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;AAC7D,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5D,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxE,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7E,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/E,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7E,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC;AAC3D,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/E,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC;AAC3D,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACzE,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC1B;AACA,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9E,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAChF,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9E,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC;AAC3D,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAChF,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC;AAC3D,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1E,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AACzB;AACA,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/E,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjF,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/E,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5D,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjF,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5D,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,CAAC;AACD;AACA,oBAAoB;AACpB,CAAC;AACD,CAAC;AACD,EAAE;AACF;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB;AACA,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B;AACA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AACnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC3B;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AACtB;AACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC1B,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAC5B;AACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAClC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAC1B;AACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd;AACA,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;AACnD,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxE,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1F,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAClG,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvF,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC/G,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,C;AC9NA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;AACb,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;AACZ,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACtF,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtF,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,OAAO,CAAC,CAAC,GAAG;AAChB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG;AAClB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvF,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAChB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtF,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,C;AC9CA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AAC7C,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC3B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACzB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACvB,IAAI,MAAM,CAAC,OAAO,CAAC;AACnB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,C;ACtCA,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACf,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;AACb,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACzD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACtD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACrD,IAAI,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG;AACjD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACtB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;AACA,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,UAAU,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AACrC,CAAC;AACD;AACA,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,C;ACpBA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzD,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAClC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;AAClE,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACrF,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;AACrH,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACjG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;;ACxCD,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AACjC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAClC,C;ACHA,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACtC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACxB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAClC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,CAAC;AACD;AACA,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD,EAAE;AACF;AACA;AACA,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC3C,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AACzB,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACf,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC;AACd,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;AACf,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAChD,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC1B,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;AACtB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;AAChC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACjB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACf;AACA,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B;AACA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;AACzB,IAAI,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB;AACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACxC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AACzB,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC;AACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAClC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAC1B;AACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5B;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9D,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;AACnB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1F,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AACjC,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACnF,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC/G,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACvD,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC;AACnB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,GAAG;AAC3E,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC5C,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AACxB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AACzC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC7E,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;AACxB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC1C,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AACtB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AACnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC3B;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC3C,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC3E,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AACtB;AACA,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AACnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC3B;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,C;ACnLA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAClC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,CAAC;AACD;AACA,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACrB,CAAC;AACD,EAAE;AACF;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACvD,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,GAAG,CAAC;AACb,IAAI,GAAG,CAAC,IAAI,CAAC;AACb,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACxD,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACzE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7D,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACvB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AAC9D,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3D,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpE,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACzE,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACvG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,C;ACrFA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3E,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACd,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5D,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,C;AC3BA,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvD,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpE,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvB,CAAC;AACD;AACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACzD,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,C;ACtBA;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3C,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzE,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3E,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACtB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACvF,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AACD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACrH,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,C;ACtCA,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACzD,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxE,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvB,CAAC;AACD;AACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,C;ACtBA,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACxB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1D,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC1E,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AAChD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AACjC,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC7E,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC;AACjB,CAAC;AACD;AACA,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC3F,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;AACf,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,C;AC7CA,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACnF,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB;AACA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACnB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B;AACA,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;AAChC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC;AACjC,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;AACf,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC;AACpB,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAC3B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;AAC5B,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AAC/B,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC;AACjC,IAAI,MAAM,CAAC,CAAC,OAAO,CAAC;AACpB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACnC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9B,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClD,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5D,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/C,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAChD,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1E,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,C;ACjFA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AACtC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AAClC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AACnC,IAAI,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AAC9B,CAAC;;ACND,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AACrC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AAClC,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,CAAC;AACD,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,OAAO,CAAC,CAAC,IAAI;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,C;ACpBA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrB,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACX,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAClB;AACA,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;AACzC,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACtB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC5C,C;;ACVA,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;AACvB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;AACpC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AACtC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,SAAS,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE;AAC9B,CAAC;AACD;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,QAAQ,CAAC,QAAQ,CAAC;AACtB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACtB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC;AAChB,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AACvB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AAChD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACnB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACzB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACxB,IAAI,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7B,C;ACxBA,IAAI,CAAC,CAAC;AACN,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;AAChC,CAAC;AACD;AACA,CAAC,MAAM,CAAC,CAAC;AACT,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC7B,IAAI,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS;AACjC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAChB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC;AACjB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AAClC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE;AAChB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC3B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAClB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,UAAU,CAAC,CAAC,WAAW,CAAC;AAC5B,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/B,IAAI,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACrC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1B,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AACzB,CAAC;AACD;AACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AAClE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACpF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACpE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACtF,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE;AACzC,IAAI,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;AAChC,CAAC;AACD;AACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACnB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC5B,CAAC;AACD;AACA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;AACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;AACnC,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACjD,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AACjB,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;AACrB,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC;AACrB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AAChD,IAAI,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACrC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1B,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AACzB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,UAAU,CAAC,CAAC,WAAW,CAAC;AAC5B,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,CAAC;AACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC3B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AACtB,CAAC;AACD;AACA,GAAG;AACH,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;AAC7B,CAAC,EAAE;AACH,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;AACrD,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE;AACf,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE;AACf,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACvB,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;AACrC,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAChC,IAAI,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACf,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACd,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC;AACtB,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/C,CAAC;AACD;AACA;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD;AACA,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;AAC7B", "file": "drawerJs.css", "sourcesContent": [".editable-canvas-toolbar,\n.editable-canvas-toolbar ul {\n    margin: 0;\n    padding: 0;\n    position: relative;\n    z-index: 1060 !important;\n    box-sizing: border-box;\n    background: inherit;\n    user-select: none;\n}\n\n\n.editable-canvas-toolbar {\n    background: white;\n    box-shadow: 0px -10px 40px rgba(0, 0, 0, 0.04);\n    border: 1px solid rgb(240, 240, 240);\n}\n\n.editable-canvas-toolbar ul,\n.editable-canvas-toolbar .toolbar-dropdown-block{\n    z-index: 202 !important;\n}\n\n.editable-canvas-toolbar .toolbar-content-wrapper {\n    display: inline-block;\n    position: relative;\n    left: 0;\n    top:  0;\n    transition: left 0.2s ease-out, top 0.2s ease-out;\n}\n\n.editable-canvas-toolbar.toolbar-horizontal {\n    width: 100%;\n}\n\n.editable-canvas-toolbar.toolbar-vertical {\n    height: 100%;\n}\n\n.popup-content-wrapper .popup-content .editable-canvas-toolbar{\n    overflow: visible;\n}\n\n\n.editable-canvas-toolbar.toolbar-multiline.toolbar-horizontal .toolbar-content-wrapper {\n    width: 100%;\n    height: auto;\n    white-space: normal;\n}\n\n.editable-canvas-toolbar.toolbar-multiline.toolbar-vertical .toolbar-content-wrapper {\n    width: 100%;\n    height: auto;\n    white-space: normal;\n}\n\n\n.editable-canvas-toolbar.toolbar-scrollable.toolbar-horizontal .toolbar-content-wrapper {\n    width: auto;\n    height: 100%;\n    white-space: nowrap;\n}\n\n.editable-canvas-toolbar.toolbar-scrollable.toolbar-vertical .toolbar-content-wrapper {\n    width: 100%;\n    height: auto;\n    white-space: normal;\n}\n\n.editable-canvas-toolbar > ul > li,\n.editable-canvas-toolbar > li,\nli.toolbar-item {\n    display: inline-block;\n    vertical-align: top;\n    list-style: none;\n    text-align: start;\n    background: white;\n    white-space: nowrap;\n}\n\n.toolbar-button {\n    list-style-type: none;\n    width: 32px;\n    min-width: 32px;\n    /*height: 32px;*/\n    display: inline-block;\n    vertical-align: top;\n    text-align: center;\n    background: white;\n}\n\n.toolbar-button a {\n    color: #333;\n    height: 100%;\n    width: 100%;\n    display: inline-block;\n    line-height: 32px;\n    text-align: center;\n}\n\n.toolbar-button a:focus {\n    outline: none;\n}\n\n.toolbar-button a i {\n    pointer-events: none;\n}\n\n.drawer-instance-container:not(.touch) .toolbar-button:not(.dragging):not(.disabled) a:hover {\n    color: white;\n    background: #1f78d8;\n}\n\n.toolbar-button.disabled:not(.dragging) a {\n    color: white;\n    background-color: #dcdcdc;\n}\n\n.toolbar-button a.active {\n    outline: none;\n    background-color: #1f78d8;\n    color: #fff;\n}\n\n.submenu-wrapper {\n    position: absolute;\n    width: 35px;\n    z-index: 1100;\n    box-shadow: 0 0 10px grey;\n    background-color: white;\n    pointer-events: auto;\n}\n\n.submenu-wrapper .toolbar-button {\n    width: 35px;\n}\n\n.toolbar-content-wrapper .submenu-wrapper {\n    display: none;\n}\n\n.group-items-container {\n    padding: 0;\n    white-space: normal;\n}\n\n\n.editable-canvas-toolbar .btn-delete-canvas a {\n    color: #B32525;\n}\n\n\n.editable-canvas-toolbar.toolbar-scrollable {\n    overflow: hidden;\n}\n\n.editable-canvas-toolbar.toolbar-horizontal {\n    width: 100%;\n    display: block;\n}\n\n.editable-canvas-toolbar.toolbar-vertical .toolbar-content-wrapper,\n.editable-canvas-toolbar.toolbar-horizontal .toolbar-content-wrapper{\n    display: -ms-flexbox;\n    display: -webkit-flex;\n    display: flex;\n    -webkit-justify-content: flex-start;\n    -ms-flex-pack: start;\n    justify-content: flex-start;\n    -webkit-align-content: flex-start;\n    -ms-flex-line-pack: start;\n    align-content: flex-start;\n    -webkit-align-items: flex-start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n}\n\n.editable-canvas-toolbar.toolbar-multiline .toolbar-content-wrapper,\n.popup-content-wrapper .popup-content .editable-canvas-toolbar .toolbar-content-wrapper{\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n}\n\n.editable-canvas-toolbar.toolbar-scrollable .toolbar-content-wrapper{\n    -webkit-flex-wrap: nowrap;\n    -ms-flex-wrap: nowrap;\n    flex-wrap: nowrap;\n}\n\n\n.editable-canvas-toolbar.toolbar-horizontal .toolbar-content-wrapper {\n    -webkit-flex-direction: row;\n    -ms-flex-direction: row;\n    flex-direction: row;\n\n}\n\n.editable-canvas-toolbar.toolbar-vertical .toolbar-content-wrapper {\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n}\n\n.editable-canvas-toolbar.toolbar-horizontal.empty-toolbar {\n    height: 0;\n}\n\n.editable-canvas-toolbar.toolbar-vertical {\n    width: 35px;\n    display: inline-block;\n    vertical-align: top;\n}\n\n.editable-canvas-toolbar.toolbar-vertical.empty-toolbar {\n    width: 0;\n}\n\n.editable-canvas-toolbar.toolbar-horizontal>li {\n    height: 100%;\n}\n\n.toolbars-wrapper {\n    position: absolute;\n    pointer-events: none;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n}\n\n.toolbar-placeholder {\n    position: absolute;\n    padding-left: 0;\n    z-index: 1000;\n    margin: 0;\n    box-sizing: border-box;\n    pointer-events: auto;\n}\n\n.toolbar-placeholder:not(.toolbar-placeholder-inside) {\n    z-index: 1001;\n}\n\n.toolbar-placeholder-top {\n    height: auto;\n    right: 0;\n    left: 0;\n    top: -2px;\n    -webkit-transform: translateY(-100%);\n    -moz-transform: translateY(-100%);\n    -ms-transform: translateY(-100%);\n    transform: translateY(-100%);\n}\n\n\n.toolbar-placeholder-bottom {\n    height: auto;\n    right: 0;\n    left: 0;\n    bottom: -12px;\n    -webkit-transform: translateY(100%);\n    -moz-transform: translateY(100%);\n    -ms-transform: translateY(100%);\n    transform: translateY(100%);\n}\n\n.toolbar-placeholder-left {\n    width: auto;\n    left: -2px;\n    bottom: 0;\n    top: 0;\n    -webkit-transform: translateX(-100%);\n    -moz-transform: translateX(-100%);\n    -ms-transform: translateX(-100%);\n    transform: translateX(-100%);\n}\n\n.toolbar-placeholder-right {\n    width: auto;\n    right: -2px;\n    bottom: 0;\n    top: 0;\n    -webkit-transform: translateX(100%);\n    -moz-transform: translateX(100%);\n    -ms-transform: translateX(100%);\n    transform: translateX(100%);\n}\n\n.toolbar-placeholder-top.toolbar-placeholder-inside {\n    top: 0;\n    -webkit-transform: none;\n    -moz-transform: none;\n    -ms-transform: none;\n    transform: none;\n    margin-top: 0;\n}\n\n.toolbar-placeholder-bottom.toolbar-placeholder-inside {\n    bottom: 0;\n    -webkit-transform: none;\n    -moz-transform: none;\n    -ms-transform: none;\n    transform: none;\n    margin-bottom: 0;\n    z-index: 1002;\n}\n\n.toolbar-placeholder-left.toolbar-placeholder-inside {\n    left: 0;\n    -webkit-transform: none;\n    -moz-transform: none;\n    -ms-transform: none;\n    transform: none;\n    margin-left: 0;\n    z-index: 1001;\n}\n\n.toolbar-placeholder-right.toolbar-placeholder-inside {\n    right: 0;\n    -webkit-transform: none;\n    -moz-transform: none;\n    -ms-transform: none;\n    transform: none;\n    margin-right: 0;\n    z-index: 1001;\n}\n\n.toolbar-placeholder.toolbar-placeholder-inside {\n    overflow: hidden;\n    pointer-events: none;\n    height: 100%;\n    width: 100%;\n}\n\n.toolbar-placeholder.toolbar-placeholder-inside .editable-canvas-toolbar {\n    overflow: visible;\n    position: absolute;\n    pointer-events: auto;\n}\n\n.toolbar-placeholder-top.toolbar-placeholder-inside  .editable-canvas-toolbar{\n    top: 0;\n    left:0;\n    right:0;\n}\n\n.toolbar-placeholder-bottom.toolbar-placeholder-inside  .editable-canvas-toolbar{\n    bottom: 0;\n    left:0;\n    right:0;\n}\n\n.toolbar-placeholder-left.toolbar-placeholder-inside  .editable-canvas-toolbar{\n    bottom: 0;\n    left:0;\n    top: 0;\n}\n\n.toolbar-placeholder-right.toolbar-placeholder-inside  .editable-canvas-toolbar{\n    bottom: 0;\n    right:0;\n    top: 0;\n}\n\n.toolbar-placeholder-overcanvas {\n    z-index: 1100;\n    top: 0;\n    left: 0;\n}\n\n.toolbar-placeholder-overcanvas .editable-canvas-toolbar{\n    box-shadow: none;\n    border: none;\n    overflow: initial;\n}\n\n.toolbar-placeholder-overcanvas .editable-canvas-toolbar .toolbar-content-wrapper{\n    position: static;\n}\n\n.toolbar-placeholder-overcanvas .editable-canvas-toolbar .toolbar-content-wrapper li {\n    position: absolute;\n    border: 1px solid #E5E5E5;\n    width: 35px;\n}\n\n.toolbar-placeholder .toolbar-placeholder-overlay {\n    display: none;\n    position: absolute;\n    list-style-type: none;\n    background-color: #fff;\n    opacity: 0.8;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1200;\n}\n\n.toolbar-placeholder.placeholder-overlayed  .toolbar-placeholder-overlay {\n    display: block;\n}\n\n.toolbar-placeholder.placeholder-disabled,\n.toolbar-placeholder.placeholder-disabled *:not(.popup-overlay):not(.toolbar-placeholder-overlay){\n    cursor: default !important;\n    pointer-events: none !important;\n}\n\n.toolbar-label {\n    display: inline-block;\n    vertical-align: top;\n\n    width: auto;\n    height: 100%;\n    margin-right: 10px;\n    line-height: 35px;\n    /* padding-top: 9px; */\n}\n\n.toolbar-button.scroll-to-begin-btn,\n.toolbar-button.scroll-to-end-btn {\n    display: none;\n    position: absolute;\n    z-index: 1000;\n}\n\n\n/* show scroll buttons if .show-scroll class is on */\n.show-scroll .toolbar-button.scroll-to-begin-btn,\n.show-scroll .toolbar-button.scroll-to-end-btn {\n    display: block;\n}\n\n/* free space on left and right edge for scroll buttons, if .show-scroll class is on*/\n.toolbar-horizontal.toolbar-scrollable.show-scroll {\n    padding:  0 35px 0 35px;\n}\n\n/* for overCanvas mode, if .show-scroll class is on */\n.toolbar-placeholder-right.toolbar-placeholder-inside .toolbar-horizontal.toolbar-scrollable.show-scroll {\n    padding:  0 50px 0 35px;\n}\n\n/* free space on top and bottom edge for scroll buttons, if .show-scroll class is on */\n.toolbar-vertical.toolbar-scrollable.show-scroll {\n    padding:  35px 0 35px 0;\n}\n\n/* for overCanvas mode, if .show-scroll class is on */\n.toolbar-placeholder-bottom.toolbar-placeholder-inside .toolbar-vertical.toolbar-scrollable.show-scroll {\n    padding:  35px 0 50px 0;\n}\n\n/* horizontal toolbar scroll buttons positioning */\n.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-begin-btn {\n    left : 0;\n    top: 0;\n}\n.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn {\n    right : 0;\n    top: 0;\n}\n\n/* for overCanvas mode, if .show-scroll class is on */\n.toolbar-placeholder-bottom.toolbar-placeholder-inside .toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn {\n    right : 15px;\n    top: 0;\n}\n\n/* vertical toolbar scroll buttons positioning */\n.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-begin-btn {\n    left : 0;\n    top: 0;\n}\n.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn {\n    left : 0;\n    bottom: 0;\n}\n\n/* for overCanvas mode, if .show-scroll class is on */\n.toolbar-placeholder-right.toolbar-placeholder-inside .toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn {\n    left : 0;\n    bottom: 15px;\n}\n\n/* scroll buttons icons for horizontal toolbar */\n.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-begin-btn i:before {\n    content:\"\\f053\";\n}\n.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn i:before {\n    content:\"\\f054\";\n}\n\n/* scroll buttons icons for vertical toolbar */\n.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-begin-btn i:before {\n    content:\"\\f077\";\n}\n.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn i:before {\n    content:\"\\f078\";\n}\n\n.toolbar-item-wrapper {\n    display: inline-block;\n    list-style: none;\n    text-align: left;\n    vertical-align: top;\n    position: relative;\n}\n\n.toolbar-item-label {\n    display: inline-block;\n    vertical-align: top;\n    width: auto;\n    height: 100%;\n    margin-right: 10px;\n    line-height: 35px;\n    /* padding-top: 9px; */\n}\n\n.toolbar-item-icon {\n    display: none;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-position: center;\n    line-height: 32px;\n    text-align: center;\n    background-color: #fff;\n    cursor: pointer;\n\n    transition: background-color 400ms linear;\n}\n\n.toolbar-item-icon:before {\n    transition: color 400ms linear;\n}\n\n.toolbar-item-wrapper .toolbar-dropdown-block {\n    display: inline-block;\n    position: relative;\n}\n\n\n\n/*********************************/\n.toolbar-item-wrapper.toolbar-button-item {\n    width: 32px;\n    height: 32px;\n    border: 1px solid #E5E5E5;\n    padding: 0;\n    margin: 0 5px;\n}\n\n.toolbar-item-wrapper.toolbar-button-item:before{\n    width: 32px;\n    height: 32px;\n    border: 1px solid #E5E5E5;\n    padding: 0;\n    margin: 0 5px;\n    color: #333;\n}\n\n\n.toolbar-item-wrapper.toolbar-button-item.prevent-highlight .toolbar-item-icon {\n    background-color: #fff !important;\n}\n.toolbar-item-wrapper.toolbar-button-item.prevent-highlight .toolbar-item-icon:before {\n    color: #333 !important;\n}\n\n.toolbar-item-wrapper.toolbar-button-item:not(.option-value-inherited):not(.option-value-invalid):not(.option-value-multiple) .toolbar-item-icon {\n    background-color: #1f78d8;\n}\n\n.toolbar-item-wrapper.toolbar-button-item:not(.option-value-inherited):not(.option-value-invalid):not(.option-value-multiple) .toolbar-item-icon:before {\n    color: #fff;\n}\n\n.toolbar-item-wrapper.toolbar-button-item.option-value-inherited:not(.option-value-multiple) .toolbar-item-icon {\n    background-color: #fff;\n}\n\n.toolbar-item-wrapper.toolbar-button-item.option-value-multiple .toolbar-item-icon {\n    background-color: #dcdcdc;\n}\n\n\n.toolbar-item-wrapper.toolbar-button-item .toolbar-dropdown-block {\n    position: absolute;\n    left: -1px;\n    top: 100%;\n}\n\n.toolbar-placeholder-bottom .toolbar-item-wrapper.toolbar-button-item .toolbar-dropdown-block {\n    position: absolute;\n    left: -1px;\n    bottom: 100%;\n    top: auto;\n}\n\n.toolbar-item-wrapper.toolbar-button-item.collapsed .toolbar-dropdown-block {\n    display: none;\n}\n\n.toolbar-item-wrapper.toolbar-button-item .toolbar-item-label {\n    display: none;\n}\n\n.toolbar-item-wrapper.toolbar-button-item .toolbar-item-icon {\n    display: block;\n}\n\n.btn-toggle-canvas.active a,\n.btn-toggle-canvas.dragging a {\n    outline: none;\n    background-color: #1f78d8;\n    color: #fff;\n}\n\n.editable-canvas-toolbar.tool-minimized-toolbar {\n    display: none;\n}\n\n.toolbar-item-description {\n    display: inline-block;\n    vertical-align: top;\n}\n\n.toolbar-item-description input{\n    vertical-align: top;\n}\n\n.editable-canvas-toolbar input[type=range] {\n    /*removes default webkit styles*/\n    -webkit-appearance: none;\n\n    /*fix for FF unable to apply focus style bug */\n    border: 1px solid white;\n\n    /*required for proper track sizing in FF*/\n    width: 300px;\n\n    padding: 0;\n    height: 20px;\n    top: 7px;\n    overflow: hidden;\n}\n\n.editable-canvas-toolbar input[type=range]::-webkit-slider-runnable-track {\n    width: 300px;\n    height: 5px;\n    background: #ddd;\n    border: none;\n    border-radius: 3px;\n}\n.editable-canvas-toolbar  input[type=range]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #1f78d8;\n    margin-top: -5px;\n}\n.editable-canvas-toolbar  input[type=range]:focus {\n    outline: none;\n}\n.editable-canvas-toolbar  input[type=range]:focus::-webkit-slider-runnable-track {\n    background: #ccc;\n}\n\n.editable-canvas-toolbar input[type=range]::-moz-range-track {\n    width: 300px;\n    height: 5px;\n    background: #ddd;\n    border: none;\n    border-radius: 3px;\n}\n\n.editable-canvas-toolbar  input[type=range]::-moz-range-thumb {\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #1f78d8;\n}\n\n/*hide the outline behind the border*/\n.editable-canvas-toolbar input[type=range]:-moz-focusring{\n    outline: 1px solid white;\n    outline-offset: -1px;\n}\n\n.editable-canvas-toolbar  input[type=range]::-ms-track {\n    width: 300px;\n    height: 5px;\n\n    /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */\n    background: transparent;\n\n    /*leave room for the larger thumb to overflow with a transparent border */\n    border-color: transparent;\n    border-width: 6px 0;\n\n    /*remove default tick marks*/\n    color: transparent;\n}\n\n.editable-canvas-toolbar input[type=range]::-ms-fill-lower {\n    background: #777;\n    border-radius: 10px;\n}\n\n.editable-canvas-toolbar input[type=range]::-ms-fill-upper {\n    background: #ddd;\n    border-radius: 10px;\n}\n\n.editable-canvas-toolbar input[type=range]::-ms-thumb {\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #1f78d8;\n}\n\n.editable-canvas-toolbar input[type=range]:focus::-ms-fill-lower {\n    background: #888;\n}\n\n.editable-canvas-toolbar input[type=range]:focus::-ms-fill-upper {\n    background: #ccc;\n}", ".toolbar-combobox-wrapper {\n    border: 1px solid #E5E5E5;\n    width: auto;\n    min-width: 120px;\n    outline: none;\n    color: #444;\n    margin: 2px auto;\n    display: inline-block;\n    box-sizing: border-box;\n\n    position: relative;\n}\n\n.toolbar-placeholder-bottom .toolbar-combobox-wrapper {\n    position: absolute;\n    bottom: 100%;\n    top: auto;\n}\n\n.toolbar-combobox-wrapper > .selected {\n    float: left;\n    height: 28px;\n    line-height: 20px;\n    display: table;\n    width: 100%;\n    cursor: pointer;\n\n    transition: background-color 400ms linear;\n}\n\n.toolbar-combobox-wrapper > .selected span {\n    display: table-cell;\n    vertical-align: middle;\n    padding: 0 13px;\n    -webkit-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    font-size: 14px;\n}\n\n/****/\n\n.toolbar-combobox-wrapper.prevent-highlight {\n\n}\n\n.toolbar-combobox-wrapper.option-value-inherited > .selected {\n    background-color: #dcdcdc;\n}\n\n.toolbar-combobox-wrapper.option-value-inherited.button-mode > .selected {\n\n}\n\n.toolbar-combobox-wrapper.option-value-invalid > .selected {\n\n}\n\n.toolbar-combobox-wrapper.option-value-invalid.button-mode > .selected {\n\n}\n\n.toolbar-combobox-wrapper.option-value-multiple > .selected {\n    background-color: #efb5b5;\n}\n\n.toolbar-combobox-wrapper.option-value-multiple.button-mode > .selected {\n\n}\n\n/****/\n\n.toolbar-combobox-wrapper.hightlight-red > .selected .ui-button {\n    background: white;\n}\n\n.toolbar-combobox-wrapper .dropdown-box {\n    border-top: none;\n    position: relative;\n    box-sizing: border-box;\n    z-index: 2;\n    background-color: #fff;\n    padding: 5px 0;\n    top: 29px;\n\n    /*border: 1px solid #E5E5E5;*/\n    /*left: -1px;*/\n    /*right: -1px;*/\n}\n\n.toolbar-combobox-wrapper:not(.button-mode) .toolbar-item-description {\n    display: inline-block;\n}\n\n.toolbar-combobox-wrapper .inputbox {\n    clear: both;\n    margin: 5px 5px 0;\n    display: none;\n}\n\n.toolbar-combobox-wrapper.edit-mode .inputbox {\n    display: block;\n}\n\n.toolbar-combobox-wrapper .inputbox input[type=text] {\n    border: 1px solid #E5E5E5;\n    width: 100%;\n    padding: 5px 5px;\n    outline: none;\n}\n\n.toolbar-combobox-wrapper .option-list {\n    clear: both;\n    list-style: none;\n    padding: 0;\n    -webkit-margin-before: 0;\n    -webkit-margin-after: 0;\n    -webkit-margin-start: 0;\n    -webkit-margin-end: 0;\n    -webkit-padding-start: 0;\n    margin-top: 5px;\n\n    width: 100%;\n    max-height: 195px;\n    position: relative;\n    overflow-y: visible;\n    overflow-x: auto;\n    display: inline-block;\n}\n\n.toolbar-combobox-wrapper .option-list .option-item {\n    margin: 2px 5px;\n    line-height: 30px;\n    cursor: pointer;\n    padding: 0 8px;\n    font-size: 12px;\n    color: #333;\n    display: block;\n}\n\n.toolbar-combobox-wrapper .option-list .option-item .option-item-text{\n    display: inline-block;\n    white-space: nowrap;\n}\n\n\n.toolbar-combobox-wrapper .option-list .option-item:first-letter {\n    text-transform: uppercase;\n}\n\n.toolbar-combobox-wrapper .option-list .option-item:not(.selected):hover {\n    background-color: #EEEEEE;\n}\n\n.toolbar-combobox-wrapper .option-list .option-item.selected {\n    background-color: #d9d9d9;\n}\n\n.toolbar-combobox-wrapper .ui-button {\n    border-left: 1px solid #E5E5E5;\n    float: right;\n    display: table-cell;\n    cursor: pointer;\n    height: 100%;\n    color: #999999;\n    width: 40px;\n    text-align: center;\n    background-color: #fff;\n}\n\n.toolbar-combobox-wrapper .ui-button i {\n    margin-top: 7px;\n}\n\n.toolbar-combobox-wrapper:focus {\n    border-color: #999999;\n}\n\n.toolbar-combobox-wrapper:focus .dropdown-box {\n    border-color: #999999;\n}\n\n.toolbar-combobox-wrapper:focus .ui-button {\n    border-left: none;\n}\n\n.toolbar-combobox-wrapper.focus {\n    border-color: #999999;\n}\n\n.toolbar-combobox-wrapper.focus .dropdown-box {\n    border-color: #999999;\n}\n\n.toolbar-combobox-wrapper.focus .ui-button {\n    border-left: none;\n}\n\n.toolbar-combobox-wrapper.button-mode {\n\n}\n\n.toolbar-item-wrapper:not(.toolbar-button-item)  .toolbar-combobox-wrapper .dropdown-box.collapsed,\n.toolbar-item-wrapper:not(.toolbar-button-item)  .toolbar-combobox-wrapper.collapsed .dropdown-box{\n    display: none;\n}\n\n/***********/\n\n.toolbar-button-item .toolbar-combobox-wrapper {\n    margin: 0 0 -1px;\n}\n\n.toolbar-button-item .toolbar-combobox-wrapper > .selected,\n.combobox-cloned.toolbar-combobox-wrapper > .selected{\n    display: none;\n}\n\n.toolbar-button-item .toolbar-combobox-wrapper > .dropdown-box,\n.combobox-cloned.toolbar-combobox-wrapper > .dropdown-box{\n    top: 0;\n    display: block;\n}\n\n.toolbar-button-item .collapsed .toolbar-combobox-wrapper:not(.combobox-cloned) {\n    display: none;\n}", ".tooltip-transparent {\n    opacity: 0 !important;\n    display: block !important;\n    visibility: visible !important;\n}\n\n\n.editable-canvas-tooltip {\n    position: absolute;\n    background: black;\n    border-radius: 4px;\n    color: white;\n    font-size: 12px;\n    padding: 5px 8px;\n    transition: opacity 0.2s;\n    opacity: 0;\n    z-index: 100001;\n    display: block;\n    pointer-events: none\n}\n\n.editable-canvas-tooltip.active {\n    opacity: 1;\n}\n\n.editable-canvas-tooltip:after {\n    content: \"\";\n    width: 0;\n    height: 0;\n    border-style: solid;\n    position: absolute;\n}\n\n.editable-canvas-tooltip[positionX=left][positionY=top] {\n    border-bottom-right-radius: 0;\n}\n\n.editable-canvas-tooltip[positionX=left][positionY=top]:after {\n    left:100%;\n    bottom:0;\n\n    border-width: 10px 0 0 10px;\n    border-color: transparent transparent transparent black;\n}\n\n.editable-canvas-tooltip[positionX=left][positionY=bottom] {\n    border-top-right-radius: 0;\n}\n\n.editable-canvas-tooltip[positionX=left][positionY=bottom]:after {\n    left:100%;\n    top:0;\n\n    border-width: 10px 10px 0 0;\n    border-color: black transparent transparent transparent;\n}\n\n.editable-canvas-tooltip[positionX=right][positionY=bottom] {\n    border-top-left-radius: 0;\n}\n\n.editable-canvas-tooltip[positionX=right][positionY=bottom]:after {\n    right:100%;\n    top:0;\n\n\n    border-width: 0 10px 10px 0;\n    border-color: transparent black transparent transparent;\n}\n\n.editable-canvas-tooltip[positionX=right][positionY=top] {\n    border-bottom-left-radius: 0;\n}\n\n.editable-canvas-tooltip[positionX=right][positionY=top]:after {\n    right: 100%;\n    bottom: 0;\n\n    border-width: 0 0 10px 10px;\n    border-color: transparent transparent black transparent;\n}\n\n.editable-canvas-tooltip[positionX=right][positionY=center]:after {\n    top: 50%;\n    right: 100%;\n    -webkit-transform: translateY(-50%);\n    -moz-transform: translateY(-50%);\n    -ms-transform: translateY(-50%);\n    transform: translateY(-50%);\n\n    border-width: 5px 10px 5px 0;\n    border-color: transparent black transparent transparent;\n}\n\n.editable-canvas-tooltip[positionX=left][positionY=center]:after {\n    top: 50%;\n    left: 100%;\n    -webkit-transform: translateY(-50%);\n    -moz-transform: translateY(-50%);\n    -ms-transform: translateY(-50%);\n    transform: translateY(-50%);\n\n    border-width: 5px 0 5px 10px;\n    border-color: transparent transparent transparent black;\n}\n\n.editable-canvas-tooltip[positionX=center][positionY=top]:after {\n    top: 100%;\n    left: 50%;\n    -webkit-transform: translateX(-50%);\n    -moz-transform: translateX(-50%);\n    -ms-transform: translateX(-50%);\n    transform: translateX(-50%);\n\n    border-width: 10px 5px 0 5px;\n    border-color: black transparent transparent transparent;\n}\n\n.editable-canvas-tooltip[positionX=center][positionY=bottom]:after {\n    bottom: 100%;\n    left: 50%;\n    -webkit-transform: translateX(-50%);\n    -moz-transform: translateX(-50%);\n    -ms-transform: translateX(-50%);\n    transform: translateX(-50%);\n\n    border-width: 0 5px 10px 5px;\n    border-color: transparent transparent black transparent;\n}", "label.background-transparency {\n    display: inline !important;\n    width: auto;\n    font-weight: normal;\n    padding-left: 10px !important;\n}", "#redactor-drawer-box.fullscreen {\n    position: fixed !important;\n    top: 0 !important;\n    left: 0 !important;\n    right: 0 !important;\n    bottom: 0 !important;\n    width: auto !important;\n    height: auto !important;\n    background: #fff !important;\n    transition: none !important;\n    z-index: 100000;\n}\n\n#redactor-drawer-box.fullscreen .toolbar-button.btn-delete-canvas,\n#redactor-drawer-box.fullscreen .toolbar-button.btn-move,\n#redactor-drawer-box.fullscreen .toolbar-button.btn-minimize-canvas,\n#redactor-drawer-box.fullscreen .toolbar-button.btn-restore-canvas,\n#redactor-drawer-box.fullscreen .redactor-drawer-resizer{\n    display: none !important;\n}\n\n#redactor-drawer-box.fullscreen .fullscreenOverOther {\n    z-index: 2000 !important;\n}\n\n#redactor-drawer-box.fullscreen .canvas-container {\n    width: 100% !important;\n    height: 100% !important;\n}\n\n#redactor-drawer-box.animation.fullscreen,\n#redactor-drawer-box.fullscreen-in-progress {\n    transition: none !important;\n}\n\n#redactor-drawer-box.fullscreen-in-progress .toolbars-wrapper {\n    opacity: 0 !important;\n}", ".crop-container {\n    position: absolute;\n    left: 0;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 20000;\n    background: #fff;\n}\n\n.crop-container .cr-image {\n    z-index: -1;\n    position: absolute;\n    top: 0;\n    left: 0;\n    transform-origin: 0 0;\n    max-height: none;\n    max-width: none;\n}\n\n.crop-container .cr-image {\n    z-index: -2;\n    position: absolute;\n    top: 0;\n    left: 0;\n    transform-origin: 0 0;\n    max-height: none;\n    max-width: none;\n    opacity: 0;\n}\n\n.crop-container .cr-boundary {\n    position: relative;\n    overflow: hidden;\n    margin: 0 auto;\n    z-index: 1;\n    width: 100%;\n    height: 100%;\n}\n\n.crop-container .cr-viewport {\n    position: absolute;\n    border: 2px solid #fff;\n    margin: auto;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    left: 0;\n    box-shadow: 0 0 2000px 2000px rgba(0, 0, 0, 0.5);\n    z-index: 100;\n    pointer-events: none;\n}\n\n.crop-container .cr-original-image {\n    display: none;\n}\n\n.crop-container .cr-vp-circle {\n    border-radius: 50%;\n}\n\n.crop-container .cr-overlay {\n    z-index: 1;\n    position: absolute;\n    cursor: move;\n}\n\n.crop-container .cr-slider-wrap {\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1;\n    margin: 15px auto;\n    text-align: center;\n}\n\n.croppie-result {\n    position: relative;\n    overflow: hidden;\n}\n\n.croppie-result img {\n    position: absolute;\n}\n\n.crop-container .cr-image,\n.crop-container .cr-overlay,\n.crop-container .cr-viewport {\n    -webkit-transform: translateZ(0);\n    -moz-transform: translateZ(0);\n    -ms-transform: translateZ(0);\n    transform: translateZ(0);\n}\n\n.cr-viewport-resizer {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    -webkit-transform: translateX(-50%) translateY(-50%);\n    -moz-transform: translateX(-50%) translateY(-50%);\n    -ms-transform: translateX(-50%) translateY(-50%);\n    transform: translateX(-50%) translateY(-50%);\n    width: inherit;\n    height: inherit;\n}\n\n.cr-viewport-resizer.resizing {\n    border: 2px dotted black;\n    background: rgba(255, 255, 255, 0.2);\n    margin: 0;\n    box-sizing: content-box;\n}\n\n.cr-viewport-resizer-handler {\n    position: absolute;\n    left: 100%;\n    top: 100%;\n    width: 15px;\n    height: 15px;\n    margin: -10px 0 0 -10px;\n    border: 2px solid #ccc;\n    background: #5c5c5c;\n    cursor: nwse-resize;\n    pointer-events: auto;\n}\n\n.cr-controls-wrapper {\n    position: absolute;\n    z-index: 200;\n    right: 5px;\n    top: 50%;\n    -webkit-transform: translateY(-50%);\n    -moz-transform: translateY(-50%);\n    -ms-transform: translateY(-50%);\n    transform: translateY(-50%);\n}\n\n.cr-controls-wrapper .btn {\n    display: block;\n    margin: 5px auto;\n}\n\n/*************************************/\n/***** STYLING RANGE INPUT ***********/\n/*************************************/\n/*http://brennaobrien.com/blog/2014/05/style-input-type-range-in-every-browser.html */\n/*************************************/\n\n.cr-slider {\n    -webkit-appearance: none;\n    /*removes default webkit styles*/\n    /*border: 1px solid white; *//*fix for FF unable to apply focus style bug */\n    width: 300px;\n    /*required for proper track sizing in FF*/\n    max-width: 100%;\n    padding-top: 8px;\n    padding-bottom: 8px;\n    background-color: transparent;\n    cursor: pointer;\n}\n\n.cr-slider::-webkit-slider-runnable-track {\n    width: 100%;\n    height: 3px;\n    background: rgba(0, 0, 0, 0.5);\n    border: 0;\n    border-radius: 3px;\n}\n\n.cr-slider::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #ddd;\n    margin-top: -6px;\n}\n\n.cr-slider:focus {\n    outline: none;\n}\n\n/*\n.cr-slider:focus::-webkit-slider-runnable-track {\nbackground: #ccc;\n}\n*/\n\n.cr-slider::-moz-range-track {\n    width: 100%;\n    height: 3px;\n    background: rgba(0, 0, 0, 0.5);\n    border: 0;\n    border-radius: 3px;\n}\n\n.cr-slider::-moz-range-thumb {\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #ddd;\n    margin-top: -6px;\n}\n\n/*hide the outline behind the border*/\n.cr-slider:-moz-focusring {\n    outline: 1px solid white;\n    outline-offset: -1px;\n}\n\n.cr-slider::-ms-track {\n    width: 100%;\n    height: 5px;\n    background: transparent;\n    /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */\n    border-color: transparent; /*leave room for the larger thumb to overflow with a transparent border */\n    border-width: 6px 0;\n    color: transparent; /*remove default tick marks*/\n}\n\n.cr-slider::-ms-fill-lower {\n    background: rgba(0, 0, 0, 0.5);\n    border-radius: 10px;\n}\n\n.cr-slider::-ms-fill-upper {\n    background: rgba(0, 0, 0, 0.5);\n    border-radius: 10px;\n}\n\n.cr-slider::-ms-thumb {\n    border: none;\n    height: 16px;\n    width: 16px;\n    border-radius: 50%;\n    background: #ddd;\n    margin-top: 1px;\n}\n\n.cr-slider:focus::-ms-fill-lower {\n    background: rgba(0, 0, 0, 0.5);\n}\n\n.cr-slider:focus::-ms-fill-upper {\n    background: rgba(0, 0, 0, 0.5);\n}\n\n/*******************************************/\n\n/***********************************/\n/* Rotation Tools */\n/***********************************/\n.cr-rotate-controls {\n    position: absolute;\n    bottom: 5px;\n    left: 5px;\n    z-index: 1;\n}\n\n.cr-rotate-controls button {\n    border: 0;\n    background: none;\n}\n\n.cr-rotate-controls i:before {\n    display: inline-block;\n    font-style: normal;\n    font-weight: 900;\n    font-size: 22px;\n}\n\n.cr-rotate-l i:before {\n    content: '↺';\n}\n\n.cr-rotate-r i:before {\n    content: '↻';\n}\n\n.image-crop-wrapper {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: #fff;\n    z-index: 201;\n    overflow: hidden;\n}\n\n.cr-image {\n    max-width: 100%;\n    max-height: 100%;\n    top: 50%;\n    left: 50%;\n    position: absolute;\n    -webkit-transform: translateX(-50%) translateY(-50%);\n    -moz-transform: translateX(-50%) translateY(-50%);\n    -ms-transform: translateX(-50%) translateY(-50%);\n    transform: translateX(-50%) translateY(-50%);\n}\n\n.cr-image-fullsize {\n    opacity: 0;\n}\n\n.imager-crop-container {\n    width: 100%;\n    height: 100%;\n    position: absolute;\n    left: 0;\n    top: 0;\n    z-index: 1300;\n}\n\n.imager-crop-canvas {\n    width: 100%;\n    height: 100%;\n    position: absolute;\n    left: 0;\n    top: 0;\n}\n\n.crop-corner {\n    position: absolute;\n    width: 14px;\n    height: 14px;\n    border: 1px solid rgb(60, 58, 58);\n    background: rgb(255, 255, 255);\n    z-index: 1301;\n}\n\n.crop-top-left {\n    left: 0;\n    top: 0;\n    cursor: nwse-resize;\n}\n\n.crop-top-right {\n    right: 0;\n    top: 0;\n    cursor: nesw-resize;\n}\n\n.crop-bottom-right {\n    right: 0;\n    bottom: 0;\n    cursor: nwse-resize;\n}\n\n.crop-bottom-left {\n    left: 0;\n    bottom: 0;\n    cursor: nesw-resize;\n}\n\n.crop-border {\n    /*background: url(../../assets/border.gif);*/\n    position:absolute;\n}\n\n.crop-border.crop-border-top {\n    width: 100%;\n    height: 1px;\n    top: 4px;\n}\n\n.crop-border.crop-border-right {\n    width: 1px;\n    height: 100%;\n    right: 2px;\n}\n\n.crop-border.crop-border-bottom {\n    width: 100%;\n    height: 1px;\n    bottom: 2px;\n}\n\n.crop-border.crop-border-left {\n    width: 1px;\n    height: 100%;\n    left: 4px;\n}", ".minimized {\n    border: none !important;\n    height: auto !important;\n    width: auto !important;\n}\n\n.minimized .toolbar-placeholder.toolbar-placeholder-top.toolbar-placeholder-inside,\n.minimized .toolbars-wrapper {\n    position: static;\n}\n\n/* Elements to hide */\n.minimized .canvas-container,\n.minimized .editable-canvas-toolbar:not(.tool-minimized-toolbar),\n.minimized .toolbar-button.btn-fullscreen-canvas,\n.minimized span.redactor-drawer-resizer{\n    display: none;\n}\n\n/* Hide toolbar while normal mode */\n.editable-canvas-toolbar.tool-minimized-toolbar {\n    display: none;\n}\n\n.minimized .editable-canvas-toolbar.tool-minimized-toolbar {\n    display: inline-block;\n}\n\n.minimized .editable-canvas-toolbar.tool-minimized-toolbar .toolbar-content-wrapper {\n    white-space: nowrap;\n    display: inline-block;\n}\n\n/* Caption of canvas */\n.canvas-caption {\n    list-style-type: none;\n    line-height: 32px;\n    margin: 0 5px;\n    display: inline-block;\n    text-align: center;\n    vertical-align: middle;\n    background: white;\n}", ".popup-wrapper {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1106;\n}\n\n.popup-wrapper .popup-content-wrapper{\n    position: absolute;\n    background-color: #fff;\n    z-index: 1;\n    max-width: 300px;\n    border: 1px solid #ccc;\n}\n\n.popup-wrapper .popup-content-wrapper.popup-transparent {\n    opacity: 0;\n}\n\n/***\n * Arrow styles\n */\n.popup-wrapper .popup-content-wrapper .popup-arrow {\n    width: 10px;\n    height: 10px;\n    z-index: 111;\n    position: absolute;\n    margin-left: -5px;\n    margin-top: -5px;\n}\n\n.popup-wrapper .popup-content-wrapper .popup-arrow:after,\n.popup-wrapper .popup-content-wrapper .popup-arrow:before{\n    content: \"\";\n    display: block;\n    border: none;\n    position: absolute;\n    border-width: 0;\n    border-style: solid;\n\n    margin-left: -5px;\n    margin-top: -5px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow {\n    bottom: auto !important;\n    top: 100% !important;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:after,\n.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:before {\n    margin-top: 5px;\n    border-width: 10px 10px 0 10px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:after{\n    border-color: #fff transparent transparent   transparent;\n    margin-top: 3px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:before {\n    border-color: #ccc  transparent transparent transparent;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow {\n    right: auto !important;\n    left: 100% !important;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:after,\n.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:before {\n    margin-left: 5px;\n    border-width: 10px 0 10px 10px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:after{\n    border-color: transparent transparent transparent #fff;\n    margin-left: 3px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:before {\n    border-color: transparent transparent transparent #ccc;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow {\n    right: 100% !important;\n    left: auto !important;\n\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:after,\n.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:before {\n    margin-left: 0;\n    border-width: 10px 10px 10px 0;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:after{\n    margin-left: 2px;\n    border-color: transparent #fff transparent transparent;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:before {\n    border-color: transparent #ccc transparent transparent;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow {\n    margin-top: 0;\n    bottom: 100% !important;\n    top: auto !important;\n\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:after,\n.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:before {\n    border-width: 0 10px 10px 10px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:after{\n    border-color:  transparent transparent #fff transparent;\n    margin-top: 2px;\n}\n\n.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:before {\n    border-color:  transparent transparent #ccc transparent;\n    margin-top: 0px;\n}\n\n/*******************\n*\n*\n*/\n\n.popup-wrapper .popup-content{\n    padding: 10px;\n\n    box-sizing: border-box;\n\n    display: flex;\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n\n    -webkit-flex-wrap: nowrap;\n    -ms-flex-wrap: nowrap;\n    flex-wrap: nowrap;\n\n    -webkit-justify-content: center;\n    -ms-flex-pack: center;\n    justify-content: center;\n\n    -webkit-align-content: center;\n    -ms-flex-line-pack: center;\n    align-content: center;\n\n    -webkit-align-items: center;\n    -ms-flex-align: center;\n    align-items: center;\n}\n\n.popup-wrapper .popup-overlay{\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n\n    background-color: #D5D5D5;\n    opacity: 0.6;\n}\n\n.popup-wrapper .popup-close-btn {\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 32px;\n    height: 32px;\n    z-index: 1007;\n    cursor: pointer;\n    text-align: center;\n    line-height: 32px;\n}\n\n.popup-wrapper .popup-content .toolbar-placeholder{\n    position: static;\n}\n\n.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range {\n    width: 100%;\n    min-width: 100%;\n    padding: 0;\n    position: relative;\n    height: 35px;\n}\n\n.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range input[type=range] {\n    float: right;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n}\n\n.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-item-description {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 110px;\n    padding: 0 0 0 8px;\n}\n\n\n\n.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-label {\n    float: left;\n    text-align: left;\n    padding: 0;\n}\n\n.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-label.toolbar-label-indicator {\n    float: right;\n    padding: 0;\n}", ".redactor-drawer-resizer {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    left: 0%;\n    top: 0%;\n}\n\n.redactor-drawer-resizer .resizer-box {\n    position: absolute;\n    right: 0px;\n    bottom: 0px;\n    height: 15px;\n    width: 15px;\n    z-index: 1003;\n    background: rgb(92, 92, 92);\n    cursor: nwse-resize;\n}\n\n.redactor-drawer-resizer .resizer-box.touch {\n    height: 25px;\n    width: 25px;\n}\n\n.toolbar-placeholder-bottom.toolbar-placeholder-inside .editable-canvas-toolbar:after,\n.toolbar-placeholder-right.toolbar-placeholder-inside .editable-canvas-toolbar:after {\n    position: absolute;\n    background: #fff;\n    display: block;\n    content: \"\";\n    z-index: 1105;\n    /*opacity:0;*/\n}\n\n.toolbar-placeholder-bottom.toolbar-placeholder-inside .editable-canvas-toolbar:after {\n    right: -1px;\n    top: 0px;\n    bottom: 0px;\n    width: 17px;\n}\n\n.toolbar-placeholder-right.toolbar-placeholder-inside .editable-canvas-toolbar:after {\n    right: 0px;\n    left: 0px;\n    bottom: -1px;\n    height: 17px;\n}", ".editable-canvas-shape-context-menu {\n    position: absolute;\n    background: white;\n    box-shadow: 0 10px 50px rgba(0,0,0,0.15);\n    z-index: 99999;\n    width: 200px;\n    height:auto !important;\n    text-align: left;\n    padding:0;\n}\n\n.editable-canvas-shape-context-menu li a {\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n}\n\n.editable-canvas-shape-context-menu li {\n    width: 100%;\n    list-style-type:none;\n}\n\n.editable-canvas-shape-context-menu li a {\n    text-align: left;\n    display: inline-block;\n    margin: 0;\n    width: 100%;\n    padding: 10px 20px;\n    cursor:pointer;\n}\n\n.editable-canvas-shape-context-menu li a:hover {\n    text-decoration: none;\n    background: #1f78d8;\n    color: white;\n}", ".zoom-tooltip {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    background-color: #ccc;\n    padding: 5px;\n    -webkit-transform: translateX(-50%) translateY(-50%);\n    -moz-transform: translateX(-50%) translateY(-50%);\n    -ms-transform: translateX(-50%) translateY(-50%);\n    transform: translateX(-50%) translateY(-50%);\n    z-index: 20000000;\n    border-radius: 5px;\n    opacity: 0.85;\n\n    pointer-events: none;\n    transition: opacity 500ms linear;\n}\n\n.zoom-tooltip.transparent-tooltip {\n    opacity:0;\n}", "ul.editable-canvas-toolbar li.editable-canvas-brushsize {\n    width: auto;\n    padding-left: 10px !important;\n    padding-right: 10px;\n}\n\n\nli.editable-canvas-brushsize .editable-canvas-brushsize-indicator{\n    width: 50px;\n    display: inline-block;\n    margin-right: 5px;\n}\n\n.popup-wrapper .popup-content .editable-canvas-toolbar li.editable-canvas-brushsize {\n    width: 100%;\n    min-width: 100%;\n    margin: 0;\n    padding: 0 10px;\n    box-sizing: border-box;\n    text-align: right;\n}\n\n.popup-wrapper .popup-content .editable-canvas-toolbar li.editable-canvas-brushsize .editable-canvas-brushsize-input{\n    float: right;\n}\n\n.popup-wrapper .popup-content li.editable-canvas-brushsize .editable-canvas-brushsize-indicator {\n    float: none;\n    text-align: left;\n}\n\n\nli.editable-canvas-brushsize .editable-canvas-brushsize-input {\n    display: inline-block;\n    vertical-align: top;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n    position: relative;\n    right: 0;\n}\n", ".touch li.drawer-plugin-brushsize {\n    padding-top: 10px !important;\n    padding-left: 10px !important;\n}", ".colorpicker-control .color-dropdown {\n    position: absolute;\n    padding: 5px;\n}\n\nli.colorpicker-control {\n    padding-left: 10px !important;\n    width: auto !important;\n}\n\n/*li.colorpicker-control span.toolbar-label {\n    display: inline-block;\n    width: auto;\n    margin-right: 10px;\n    float: left;\n    vertical-align: middle;\n    padding-top: 9px;\n}\n*/\n\n\n.colorpicker-control span.color-indicator {\n    display: inline-block;\n    width: 32px;\n    position: relative;\n    top: 0px;\n    height: 32px;\n    background-color: white;\n}\n\n.colorpicker-control .color-swatch {\n  box-sizing: border-box;\n  float : left;\n  width: 32px;\n  height: 32px;\n  padding : 0;\n  margin : 0;\n  border : 1px solid white;\n}\n\n.colorpicker-control .color-swatch.transparent {\n  width: 100%;\n  background-color: white;\n  text-align:  center;\n  margin : 0;\n}\n\n.control-hidden {\n  visibility: hidden !important;\n}\n\n.color-dropdown {\n    max-width: 365px;\n    max-height: 365px;\n    z-index: 1;\n\n    background: #fff;\n    border: 1px solid #ccc;\n\n    display: -ms-flexbox;\n    display: -webkit-flex;\n    display: flex;\n\n    -webkit-justify-content: flex-start;\n    -ms-flex-pack: start;\n    justify-content: flex-start;\n\n    -webkit-align-content: center;\n    -ms-flex-line-pack: center;\n    align-content: center;\n\n    -webkit-align-items: flex-start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n}\n\n.color-dropdown.palette-with-scroll {\n    display: block;\n}\n\n.popup-content-wrapper .popup-content li.colorpicker-control {\n    min-width: 50%;\n}\n\n.popup-content-wrapper .popup-content li.colorpicker-control.editable-canvas-text-option {\n    min-width: 177px;\n    height: 40px;\n    line-height: 40px !important;\n}\n\n.popup-content-wrapper .popup-content li.colorpicker-control span.color-indicator {\n    right: 8px;\n    float: right;\n}\n\n.popup-content-wrapper .popup-content li.colorpicker-control.editable-canvas-text-option span.color-indicator {\n    right: 1px;\n    float: right;\n    top: 4px;\n}\n\n.popup-content-wrapper .popup-content .color-dropdown {\n    overflow: auto;\n    background: #fff;\n    border: 1px solid #ccc;\n}\n\n.popup-content-wrapper .popup-content .color-dropdown:not([data-position]){\n    width: 365px;\n\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n}\n\n.toolbar-placeholder-bottom .color-dropdown,\n.popup-content-wrapper .popup-content[data-position=top] .color-dropdown {\n    left: 0px;\n    bottom: 100%;\n    max-width: 365px;\n\n    -webkit-flex-direction: row;\n    -ms-flex-direction: row;\n    flex-direction: row;\n\n    -webkit-flex-wrap: wrap-reverse;\n    -ms-flex-wrap: wrap-reverse;\n    flex-wrap: wrap-reverse;\n}\n\n.toolbar-placeholder-top .color-dropdown,\n.popup-content-wrapper .popup-content[data-position=bottom] .color-dropdown {\n    left: 0px;\n    top: 100%;\n    max-width: 375px;\n\n    -webkit-flex-direction: row;\n    -ms-flex-direction: row;\n    flex-direction: row;\n\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n}\n\n.toolbar-placeholder-left .color-dropdown,\n.popup-content-wrapper .popup-content[data-position=right] .color-dropdown {\n    left: 100%;\n    top: 0;\n    max-height: 375px;\n\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n}\n\n.toolbar-placeholder-right .color-dropdown,\n.popup-content-wrapper .popup-content[data-position=left] .color-dropdown {\n    right: 100%;\n    top: 0;\n    max-height: 375px;\n\n    padding-bottom: 14px;\n\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n\n    -webkit-flex-wrap: wrap-reverse;\n    -ms-flex-wrap: wrap-reverse;\n    flex-wrap: wrap-reverse;\n}", "li.editable-canvas-color-redactor {\n    padding-left: 10px !important;\n    width: auto !important;\n}\n\n/*li.editable-canvas-color-redactor span.toolbar-label {\n    display: inline-block;\n    width: auto;\n    margin-right: 10px;\n    float: left;\n    vertical-align: middle;\n    padding-top: 9px;\n}\n*/\n\nli.editable-canvas-color-redactor span.color-dropdown {\n    position: absolute;\n    left:0px;\n    top:30px;\n    width: 290px;\n}\n\nli.editable-canvas-color-redactor span.color-indicator {\n    display: inline-block;\n    width: 32px;\n    position: relative;\n    top: 0px;\n    height: 32px;\n    background-color: white;\n}\n\n.toolbar-topRight li.editable-canvas-color-redactor span.color-dropdown {\n    right: 0;\n    left: auto;\n}\n\n\n\nul.editable-canvas-toolbar li.editable-canvas-opacity-label {\n    width: auto;\n    padding-right: 3px;\n    padding-left: 3px;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-opacity {\n    width: auto;\n    margin-right: 10px;\n    margin-left: 10px;\n}\n\n\nli.editable-canvas-opacity .editable-canvas-opacity-indicator{\n    width: 50px;\n    display: inline-block;\n    margin-right:  5px;\n}\n\n\n\nli.editable-canvas-opacity .editable-canvas-opacity-input {\n    display: inline-block;\n    vertical-align: top;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n    position: relative;\n    right: 0;\n}\n\n.popup-content .editable-canvas-toolbar li.editable-canvas-opacity {\n    width: 100%;\n    min-width: 100%;\n    margin: 0;\n    padding: 0 10px;\n    box-sizing: border-box;\n    text-align: right;\n}\n\n.popup-content li.editable-canvas-opacity .editable-canvas-opacity-input{\n    float: right;\n}\n\n.popup-content .editable-canvas-toolbar li.editable-canvas-opacity .editable-canvas-opacity-indicator {\n    float: none;\n    text-align: left;\n}", "\n.editable-canvas-colorpicker {\n    width: 32px;\n    background: white;\n    height: 32px;\n    border: none;\n    -webkit-appearance: none;\n    position: relative;\n}\n\n.editable-canvas-colorpicker::-webkit-color-swatch-wrapper {\n    padding: 0;\n}\n\n.editable-canvas-colorpicker::-webkit-color-swatch {\n    border: none;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-plugin-color .toolbar-label {\n    position: relative;\n    top: -9px;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-plugin-color {\n    width: auto;\n    padding-left: 10px;\n    text-align: left;\n}", ".editable-canvas-line-width .toolbar-item-description {\n    display: inline-block;\n}\n\n.editable-canvas-line-width input.editable-canvas-line-width-input {\n    display: inline-block;\n    vertical-align: top;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n    position: relative;\n    right: 0;\n}\n\n.editable-canvas-line-width .editable-canvas-line-width-label {\n    padding-left: 10px;\n}\n\nspan.toolbar-label.editable-canvas-line-width-indicator {\n    width: 50px;\n    display: inline-block;\n    margin-right: 5px;\n}", "\n\n.editable-canvas-opacity-option-indicator {\n    width: 50px;\n    display: inline-block;\n    margin-right: 5px;\n}\n\nli.editable-canvas-opacity-option .editable-canvas-opacity-option-input {\n    display: inline-block;\n    vertical-align: top;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n    position: relative;\n    right: 0;\n}\n\n.editable-canvas-opacity-option-label {\n    padding-left: 10px ;\n}\n\n.popup-content .editable-canvas-toolbar li.editable-canvas-opacity-option {\n    width: 100%;\n    min-width: 100%;\n    margin: 0;\n    padding: 0 10px;\n    box-sizing: border-box;\n    text-align: right;\n}\n\n.popup-content li.editable-canvas-opacity-option .editable-canvas-opacity-option-input{\n    float: right;\n}\n\n.popup-content .editable-canvas-toolbar li.editable-canvas-opacity-option .editable-canvas-opacity-option-indicator {\n    float: none;\n    text-align: left;\n}", ".editable-canvas-stroke-width .toolbar-item-description {\n    display: inline-block;\n}\n\n.editable-canvas-stroke-width input.editable-canvas-stroke-width-input {\n    display: inline-block;\n    vertical-align: top;\n    width: 100px;\n    margin-left: 7px;\n    margin-right: 7px;\n    position: relative;\n    right: 0;\n}\n\n.editable-canvas-stroke-width .editable-canvas-stroke-width-label {\n    padding-left: 10px;\n}\n\nspan.toolbar-label.editable-canvas-stroke-width-indicator {\n    width: 50px;\n    display: inline-block;\n    margin-right: 5px;\n}", ".editable-canvas-text-option {\n    padding-left: 10px;\n    padding-right: 10px;\n}\n\n.editable-canvas-text-option select{\n    margin: 7px auto;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-fontfamily {\n    cursor: pointer;\n    width: auto;\n    height: 100%;\n    position: relative;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown {\n    position: absolute;\n    left: 0px;\n    top: 30px;\n    width: 100%;\n    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.15);\n    z-index: 99999;\n    background: white;\n    width: 300px;\n    height: auto !important;\n    padding-left: 5px !important;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown li {\n    display: block;\n    background: white;\n    text-align: left;\n    float: left;\n    width: auto;\n    height: auto;\n}\n\nul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown li a {\n    padding: 5px;\n}\n\nul.editable-canvas-toolbar.toolbar-topRight li.editable-canvas-fontfamily .fonts-dropdown {\n    left: auto;\n    right: 0;\n}", ".editable-canvas-border-type {\n    width: auto !important;\n    position: relative;\n    padding-left: 8px;\n}\n\n\n.popup-content-wrapper .popup-content .editable-canvas-border-type .toolbar-label {\n    margin-right: 6px;\n}\n\n.editable-canvas-border-type .border-type-indicator {\n    margin-top: 8px;\n    display: inline-block;\n    width: 60px !important;\n    height: 20px;\n    line-height: 20px;\n\n    padding: 0 8px;\n    box-sizing: content-box;\n    text-align: center;\n    border: 1px solid #ccc;\n\n    background-position: center;\n    background-repeat: no-repeat;\n}\n\n.border-type-dropdown {\n    width: 66px;\n    right: 0;\n    left: auto;\n    transform: none;\n    background: #fff;\n    padding: 5px;\n    border: 1px solid #ccc;\n    box-sizing: content-box;\n    display: inline-block;\n    position: absolute;\n    text-align: center;\n}\n\n.border-type-dropdown ul li {\n    display: block;\n    width: 100%;\n    background-position-y: 50%;\n    background-repeat: no-repeat;\n    cursor: pointer;\n    height: 20px;\n    line-height: 20px;\n}\n\n.border-type-dropdown ul li:hover {\n    background-color: #1f78d8;\n}\n\n.toolbar-placeholder-bottom .border-type-dropdown,\n.popup-content-wrapper .popup-content .border-type-dropdown{\n    bottom: 100%;\n}\n\n.toolbar-placeholder-top .border-type-dropdown{\n    top: 100%;\n}\n\n.toolbar-placeholder-left .border-type-dropdown{\n    left: 100%;\n    top: 0;\n}\n\n.toolbar-placeholder-right .border-type-dropdown{\n    right: 100%;\n    top: 0;\n}\n\n.border-type-dropdown.toolbar-dropdown-block.border-type-dropdown-cloned {\n    width: 66px;\n    text-align: center;\n}\n\n.border-type-dropdown-cloned ul {\n    padding: 0;\n}", ".toolbar-button .fa.fa-long-arrow-right ,\n.toolbar-button .fa.fa-arrows-h {\n    -webkit-transform: rotate(-45deg);\n    -ms-transform: rotate(-45deg);\n    -moz-transform: rotate(-45deg);\n    transform: rotate(-45deg);\n}\n", ".toolbar-button .fa-line {\n    width: 14px;\n    -ms-transform: rotate(24deg);\n    -webkit-transform: rotate(24deg);\n    -moz-transform: rotate(24deg);\n    transform: rotate(24deg);\n}\n.toolbar-button  .fa-line:before {\n    position: relative;\n    line-height: 10px;\n    top: 2px;\n    content: \"/\";\n    font-size: 19px !important;\n    font-weight: bold;\n}\n\n.drawer-tool-mouse-tooltip .fa-line {\n    position: relative;\n    left: 5px;\n    top: 2px;\n}", "button.stop-polygon {\n    position: absolute;\n    right: 0;\n    top: 0;\n    z-index: 1000;\n\n    background: white;\n    border: 1px solid rgb(226, 226, 226);\n    padding: 6px 15px;\n    box-shadow: 0px 0px 5px rgba(0,0,0,0.1);\n}", ".btn-triangle .fa-play,\n.drawer-tool-mouse-tooltip .fa-play{\n    -moz-transform: rotate(-90deg);\n    -webkit-transform: rotate(-90deg);\n    -o-transform: rotate(-90deg);\n    transform: rotate(-90deg);\n}\n\n.drawer-tool-mouse-tooltip {\n    opacity: 0;\n    position:absolute;\n    margin-left: 10px;\n    margin-top: 10px;\n    padding:5px;\n    background: #f5f5f5;\n    border: 1px solid #efefef;\n    border-radius: 4px;\n    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.03);\n    z-index: 99999;\n    padding-left: 10px;\n    padding-right: 10px;\n    pointer-events: none;\n    white-space: nowrap;\n    transition: opacity 0.2s;\n}", "body {\n    overflow: hidden !important;\n}\n\n.hidden {\n    display: none !important;\n    visibility: hidden !important\n}\n\n#redactor-drawer-box .sp-replacer {\n    padding: 0;\n    margin: 0;\n    width: 32px;\n    height: 32px;\n}\n\n#redactor-drawer-box .sp-preview {\n    margin: 0;\n    padding: 0;\n    width: 100%;\n    height: 100%;\n}\n\n#redactor-drawer-box .sp-preview-inner {\n    height: 100%;\n    width: 100%;\n}\n\n#redactor-drawer-box .sp-dd {\n    display: none;\n}\n\n#redactor-drawer-box {\n    vertical-align: middle;\n    outline: none;\n    top: 35px;\n    background: white;\n    transition: none;\n    background: transparent;\n    box-sizing: content-box;\n}\n\n#redactor-drawer-box.animated {\n    transition: height 0.5s ease-out,\n    width 0.5s ease-out 0s,\n    left 0.5s ease-out 0s,\n    top 0.5s ease-out 0s;\n}\n\nbody.drawer-moving #redactor-drawer-box.drawer-instance-container,\nbody.drawer-moving #redactor-drawer-box.drawer-instance-container .canvas-container,\nbody.drawer-resizing #redactor-drawer-box.drawer-instance-container,\nbody.drawer-resizing #redactor-drawer-box.drawer-instance-container .canvas-container{\n    transition: none;\n}\n\nbody.drawer-zoom-moving,\nbody.drawer-zoom-moving * {\n    cursor: -webkit-grabbing !important;;\n    cursor: grabbing !important;\n}\n\nbody.drawer-moving,\nbody.drawer-moving * {\n    cursor: move !important;\n}\n\nbody.drawer-resizing,\nbody.drawer-resizing * {\n    cursor: nwse-resize !important;\n}\n\n\n#redactor-drawer-box .canvas-container {\n    margin: 0;\n    box-shadow: 0px 0px 50px rgba(0, 0, 0, 0.03);\n    z-index: 200;\n    overflow: hidden;\n    transition: none;\n}\n\n#redactor-drawer-box.animated .canvas-container{\n    transition: height 0.5s ease-out,\n    width 0.5s ease-out 0s,\n    left 0.5s ease-out 0s,\n    top 0.5s ease-out 0s;\n}\n\n#redactor-drawer-box .canvas-container canvas {\n    background: transparent;\n}\n\n.noselect {\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n}\n\n/**\n * DrawerJs properties modal.\n */\n#redactor-drawer-properties .drawer-properties-width,\n#redactor-drawer-properties .drawer-properties-height {\n    width: 45%;\n    text-align: center;\n}\n\n#redactor-drawer-properties .size-separator {\n    width: 10%;\n    display: inline-block;\n    text-align: center;\n}\n\n#redactor-drawer-box {\n    font-family: 'Arial', sans-serif;\n}\n\n.redactor-editor .btn-group ul {\n    position: absolute;\n    left: -2px;\n    top: 30px;\n    padding: 0;\n    background: white;\n    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n\n.editable-canvas-image {\n    opacity: 1;\n}\n\n.editable-canvas-image.edit-mode {\n    opacity: 0;\n}\n\n#redactor-modal-overlay, #redactor-modal-box, #redactor-modal {\n    z-index: 1100 !important;\n}"]}