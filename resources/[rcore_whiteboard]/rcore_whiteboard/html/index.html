<!doctype html>
<html lang="en">
    <head>
        <meta name="referrer" content="no-referrer"/>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <title>NUI React Boilerplate</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700&display=swap"
            rel="stylesheet">
        <script src="./config.js" type="text/javascript"></script>
        <link href="./positions.css" rel="stylesheet">
        <script defer="defer" src="./main.1b3a9850.js"></script>
        <link href="./main.06902409.css" rel="stylesheet">
        <script src="./jquery-3.4.1.min.js"></script>
        <script src="./jquery-migrate-1.2.1.min.js"></script>

        <link rel="stylesheet" href="./assets/bootstrap.min.css"/>
        <link href="./assets/font-awesome.min.css" rel="stylesheet">
        <link rel="stylesheet" href="./drawerJs.css"/>
        <link rel="stylesheet" href="./breakpoints.css"/>
        <script src="./drawerJs.standalone.js"></script>
        <style>
            .canvas-container {
                background: white;
            }
            .drawer-instance-container {
                background: transparent!important;
            }

        </style>
    </head>

    <body>
        <script>
            function sendNuiMessage(event, data) {
                $.post(`https://${GetParentResourceName()}/${event}`, JSON.stringify({data}))
            }
            function onCloseButton() {
                sendNuiMessage('onCloseButton')
            }
            function onSaveButton() {
                sendNuiMessage('onSaveButton')
            }
            function onClear() {
                window.drawer.api.loadCanvasFromData({})
            }
    
            window.alert = console.log
        </script>
        <div id="editor-wrapper" class="size-container" style="display:none;">
            <div id="canvas-editor" style="position: absolute; left: 500px; top: 500px;"></div>
            <div class="buttons">
                <button onclick='onCloseButton()' class="button cancel-button">CANCEL</button>
                <button onclick='onClear()' class="button clear-button">CLEAR</button>
                <button onclick='onSaveButton()' class="button save-button">SAVE</button>
            </div>
        </div>
        
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root"></div>

        
    <script>
        $(document).ready(function () {
            var drawerPlugins = [
                // Drawing tools
                'Pencil',
                'Eraser',
                'Text',
                'Line',
                'ArrowOneSide',
                'ArrowTwoSide',
                'Triangle',
                'Rectangle',
                'Circle',
                'Image',
                'BackgroundImage',
                'Polygon',
                'ImageCrop',

                // Drawing options
                //'ColorHtml5',
                'Color',
                'ShapeBorder',
                'BrushSize',
                'OpacityOption',

                'LineWidth',
                'StrokeWidth',

                // 'Resize',
                'ShapeContextMenu',
                // 'CloseButton',
                'OvercanvasPopup',
                'OpenPopupButton',
                // 'MinimizeButton',
                'ToggleVisibilityButton',
                // 'MovableFloatingMode',
                // 'FullscreenModeButton',

                'TextLineHeight',
                'TextAlign',

                'TextFontFamily',
                'TextFontSize',
                'TextFontWeight',
                'TextFontStyle',
                'TextDecoration',
                'TextColor',
                'TextBackgroundColor'
            ];

            // drawer is created as global property solely for debug purposes.
            // doing  in production is considered as bad practice
            window.drawer = null
            function createDrawer(size) {
                window.drawer = new DrawerJs.Drawer(null, {
                    exitOnOutsideClick: false,
                    plugins: drawerPlugins,
                    corePlugins: [
                        'Zoom' // use null here if you want to disable Zoom completely
                    ],
                    pluginsConfig: {
                        Image: {
                            scaleDownLargeImage: true,
                            maxImageSizeKb: 10240, //1MB
                            cropIsActive: true
                        },
                        BackgroundImage: {
                            scaleDownLargeImage: true,
                            maxImageSizeKb: 10240, //1MB
                            //fixedBackgroundUrl: '/examples/redactor/images/vanGogh.jpg',
                            imagePosition: 'center',  // one of  'center', 'stretch', 'top-left', 'top-right', 'bottom-left', 'bottom-right'
                            acceptedMIMETypes: ['image/jpeg', 'image/png', 'image/gif'] ,
                            dynamicRepositionImage: true,
                            dynamicRepositionImageThrottle: 100,
                            cropIsActive: false
                        },
                        Text: {
                            editIconMode : false,
                            editIconSize : 'large',
                            defaultValues : {
                            fontSize: 72,
                            lineHeight: 2,
                            textFontWeight: 'bold'
                            },
                            predefined: {
                            fontSize: [8, 12, 14, 16, 32, 40, 72],
                            lineHeight: [1, 2, 3, 4, 6]
                            }
                        },
                        Zoom: {
                            enabled: true, 
                            showZoomTooltip: true, 
                            useWheelEvents: true,
                            zoomStep: 1.05, 
                            defaultZoom: 1, 
                            maxZoom: 32,
                            minZoom: 1, 
                            smoothnessOfWheel: 0,
                            //Moving:
                            enableMove: true,
                            enableWhenNoActiveTool: true,
                            enableButton: true
                        }
                    },
                    toolbars: {
                        drawingTools: {
                            position: 'top',         
                            positionType: 'outside',
                            customAnchorSelector: '#custom-toolbar-here',  
                            hidden: false,     
                            toggleVisibilityButton: false,
                            fullscreenMode: {
                                position: 'top', 
                                hidden: false,
                                toggleVisibilityButton: false
                            }
                        },
                        toolOptions: {
                            position: 'bottom', 
                            positionType: 'outside',
                            hidden: false,
                            toggleVisibilityButton: false,
                            fullscreenMode: {
                                position: 'bottom', 
                                compactType: 'popup',
                                hidden: false,
                                toggleVisibilityButton: false
                            }
                        },
                        settings : {
                            position : 'right', 
                            positionType: 'outside',					
                            hidden: false,	
                            toggleVisibilityButton: false,
                            fullscreenMode: {
                                position : 'right', 
                                hidden: false,
                                toggleVisibilityButton: false
                            }
                        }
                    },
                    defaultImageUrl: '/examples/redactor/images/drawer.jpg',
                    defaultActivePlugin : { name : 'Pencil', mode : 'lastUsed', name: 'TextBackgroundColor', name: 'TextSize', name: 'TextFontSize', name: 'TextFontFamily'},
                    debug: false,
                    activeColor: '#f39b1b',
                    transparentBackground: false,
                    align: 'floating',  //one of 'left', 'right', 'center', 'inline', 'floating'
                    lineAngleTooltip: { enabled: true, color: 'blue',  fontSize: 15}
                }, size.width, size.height);

                $('#canvas-editor').append(window.drawer.getHtml());
                window.drawer.onInsert();
            }
            
            function onStartEditing(size) {
                createDrawer(size)
                window.drawer.api.startEditing()
            }

            function onStopEditing() {
                window.drawer.api.stopEditing()
                window.drawer.destroy()
            }
            function onLoad(data) {
                window.drawer.api.loadCanvasFromData(data)
            }
            function onSave() {
                return window.drawer.api.getCanvasAsJSON()
            }

            function openEditor(size) {
                document.getElementById('editor-wrapper').style.display = 'block'
                onStartEditing(size)
            }

            function closeEditor() {
                onStopEditing()
                document.getElementById('editor-wrapper').style.display = 'none'
            }

            function saveEditor() {
                sendNuiMessage('onEditorSave', onSave())
            }
            
            window.addEventListener('message', function(event) {
                const {action, data} = event.data
                
                if (action === 'OPEN_EDITOR') {
                    openEditor(data.size)

                    if (data.initialData) {
                        onLoad(data.initialData)
                    }
                }

                if (action === 'CLOSE_EDITOR') {
                    if (data.shouldSave) {
                        saveEditor()
                    }
                    closeEditor()
                }

                if (action === 'LOAD_DATA') {
                    onLoad(action.data)
                }

                if (action === 'SAVE_DATA') {
                    saveEditor()
                }
            });
        });
    </script>
    </body>
</html>