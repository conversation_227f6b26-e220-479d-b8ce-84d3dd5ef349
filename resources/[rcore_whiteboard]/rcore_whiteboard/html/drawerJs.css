/*! drawerjs - 1.11.0
 */


 .button {
    width: 100px;
    height: 35px;
    margin: 0 5px;
    color: white;
    border-radius: 3px;
    border: none;
    outline: none;
    z-index: 99999;
 }
 .cancel-button {
    background-color: rgb(208, 2, 27);
 }
.save-button {
    background-color: rgb(74, 142, 5);
}
.clear-button {
    background-color: rgb(229, 106, 0);
}
.buttons {
    position: fixed;
    z-index: 99999;
    bottom: 25px;
    right: 25px;
}

.size-container {
    height: 100vh;
}
.drawer-instance-container {
    border: none!important;
}

.editable-canvas-toolbar,
.editable-canvas-toolbar ul {
    margin: 0;
    padding: 0;
    position: relative; 
    z-index: 1060 !important;
    box-sizing: border-box;
    background: inherit;
    user-select: none;
}


.editable-canvas-toolbar {
    background: white;
    box-shadow: 0px -10px 40px rgba(0, 0, 0, 0.04);
    border: 1px solid rgb(240, 240, 240);
}

.editable-canvas-toolbar ul,
.editable-canvas-toolbar .toolbar-dropdown-block{
    z-index: 202 !important;
}

.editable-canvas-toolbar .toolbar-content-wrapper {
    display: inline-block;
    position: relative;
    left: 0;
    top:  0;
    transition: left 0.2s ease-out, top 0.2s ease-out;
}

.editable-canvas-toolbar.toolbar-horizontal {
    width: 100%;
}

.editable-canvas-toolbar.toolbar-vertical {
    height: 100%;
}

.popup-content-wrapper .popup-content .editable-canvas-toolbar{
    overflow: visible;
}


.editable-canvas-toolbar.toolbar-multiline.toolbar-horizontal .toolbar-content-wrapper {
    width: 100%;
    height: auto;
    white-space: normal;
}

.editable-canvas-toolbar.toolbar-multiline.toolbar-vertical .toolbar-content-wrapper {
    width: 100%;
    height: auto;
    white-space: normal;
}


.editable-canvas-toolbar.toolbar-scrollable.toolbar-horizontal .toolbar-content-wrapper {
    width: auto;
    height: 100%;
    white-space: nowrap;
}

.editable-canvas-toolbar.toolbar-scrollable.toolbar-vertical .toolbar-content-wrapper {
    width: 100%;
    height: auto;
    white-space: normal;
}

.editable-canvas-toolbar > ul > li,
.editable-canvas-toolbar > li,
li.toolbar-item {
    display: inline-block;
    vertical-align: top;
    list-style: none;
    text-align: start;
    background: white;
    white-space: nowrap;
}

.toolbar-button {
    list-style-type: none;
    width: 32px;
    min-width: 32px;
    /*height: 32px;*/
    display: inline-block;
    vertical-align: top;
    text-align: center;
    background: white;
}

.toolbar-button a {
    color: #333;
    height: 100%;
    width: 100%;
    display: inline-block;
    line-height: 32px;
    text-align: center;
}

.toolbar-button a:focus {
    outline: none;
}

.toolbar-button a i {
    pointer-events: none;
}

.drawer-instance-container:not(.touch) .toolbar-button:not(.dragging):not(.disabled) a:hover {
    color: white;
    background: #1f78d8;
}

.toolbar-button.disabled:not(.dragging) a {
    color: white;
    background-color: #dcdcdc;
}

.toolbar-button a.active {
    outline: none;
    background-color: #1f78d8;
    color: #fff;
}

.submenu-wrapper {
    position: absolute;
    width: 35px;
    z-index: 1100;
    box-shadow: 0 0 10px grey;
    background-color: white;
    pointer-events: auto;
}

.submenu-wrapper .toolbar-button {
    width: 35px;
}

.toolbar-content-wrapper .submenu-wrapper {
    display: none;
}

.group-items-container {
    padding: 0;
    white-space: normal;
}


.editable-canvas-toolbar .btn-delete-canvas a {
    color: #B32525;
}


.editable-canvas-toolbar.toolbar-scrollable {
    overflow: hidden;
}

.editable-canvas-toolbar.toolbar-horizontal {
    width: 100%;
    display: block;
}

.editable-canvas-toolbar.toolbar-vertical .toolbar-content-wrapper,
.editable-canvas-toolbar.toolbar-horizontal .toolbar-content-wrapper{
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-align-content: flex-start;
    -ms-flex-line-pack: start;
    align-content: flex-start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.editable-canvas-toolbar.toolbar-multiline .toolbar-content-wrapper,
.popup-content-wrapper .popup-content .editable-canvas-toolbar .toolbar-content-wrapper{
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.editable-canvas-toolbar.toolbar-scrollable .toolbar-content-wrapper{
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}


.editable-canvas-toolbar.toolbar-horizontal .toolbar-content-wrapper {
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;

}

.editable-canvas-toolbar.toolbar-vertical .toolbar-content-wrapper {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
}

.editable-canvas-toolbar.toolbar-horizontal.empty-toolbar {
    height: 0;
}

.editable-canvas-toolbar.toolbar-vertical {
    width: 35px;
    display: inline-block;
    vertical-align: top;
}

.editable-canvas-toolbar.toolbar-vertical.empty-toolbar {
    width: 0;
}

.editable-canvas-toolbar.toolbar-horizontal>li {
    height: 100%;
}

.toolbars-wrapper {
    position: absolute;
    pointer-events: none;
    z-index: 202;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.toolbar-placeholder {
    position: absolute;
    padding-left: 0;
    z-index: 1000;
    margin: 0;
    box-sizing: border-box;
    pointer-events: auto;
}

.toolbar-placeholder:not(.toolbar-placeholder-inside) {
    z-index: 1001;
}

.toolbar-placeholder-top {
    height: auto;
    right: 0;
    left: 0;
    top: -2px;
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    transform: translateY(-100%);
}


.toolbar-placeholder-bottom {
    height: auto;
    right: 0;
    left: 0;
    bottom: -12px;
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    -ms-transform: translateY(100%);
    transform: translateY(100%);
}

.toolbar-placeholder-left {
    width: auto;
    left: -2px;
    bottom: 0;
    top: 0;
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    transform: translateX(-100%);
}

.toolbar-placeholder-right {
    width: auto;
    right: -2px;
    bottom: 0;
    top: 0;
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    transform: translateX(100%);
}

.toolbar-placeholder-top.toolbar-placeholder-inside {
    top: 0;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    transform: none;
    margin-top: 0;
}

.toolbar-placeholder-bottom.toolbar-placeholder-inside {
    bottom: 0;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    transform: none;
    margin-bottom: 0;
    z-index: 1002;
}

.toolbar-placeholder-left.toolbar-placeholder-inside {
    left: 0;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    transform: none;
    margin-left: 0;
    z-index: 1001;
}

.toolbar-placeholder-right.toolbar-placeholder-inside {
    right: 0;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    transform: none;
    margin-right: 0;
    z-index: 1001;
}

.toolbar-placeholder.toolbar-placeholder-inside {
    overflow: hidden;
    pointer-events: none;
    height: 100%;
    width: 100%;
}

.toolbar-placeholder.toolbar-placeholder-inside .editable-canvas-toolbar {
    overflow: visible;
    position: absolute;
    pointer-events: auto;
}

.toolbar-placeholder-top.toolbar-placeholder-inside  .editable-canvas-toolbar{
    top: 0;
    left:0;
    right:0;
}

.toolbar-placeholder-bottom.toolbar-placeholder-inside  .editable-canvas-toolbar{
    bottom: 0;
    left:0;
    right:0;
}

.toolbar-placeholder-left.toolbar-placeholder-inside  .editable-canvas-toolbar{
    bottom: 0;
    left:0;
    top: 0;
}

.toolbar-placeholder-right.toolbar-placeholder-inside  .editable-canvas-toolbar{
    bottom: 0;
    right:0;
    top: 0;
}

.toolbar-placeholder-overcanvas {
    z-index: 1100;
    top: 0;
    left: 0;
}

.toolbar-placeholder-overcanvas .editable-canvas-toolbar{
    box-shadow: none;
    border: none;
    overflow: initial;
}

.toolbar-placeholder-overcanvas .editable-canvas-toolbar .toolbar-content-wrapper{
    position: static;
}

.toolbar-placeholder-overcanvas .editable-canvas-toolbar .toolbar-content-wrapper li {
    position: absolute;
    border: 1px solid #E5E5E5;
    width: 35px;
}

.toolbar-placeholder .toolbar-placeholder-overlay {
    display: none;
    position: absolute;
    list-style-type: none;
    background-color: #fff;
    opacity: 0.8;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1200;
}

.toolbar-placeholder.placeholder-overlayed  .toolbar-placeholder-overlay {
    display: block;
}

.toolbar-placeholder.placeholder-disabled,
.toolbar-placeholder.placeholder-disabled *:not(.popup-overlay):not(.toolbar-placeholder-overlay){
    cursor: default !important;
    pointer-events: none !important;
}

.toolbar-label {
    display: inline-block;
    vertical-align: top;

    width: auto;
    height: 100%;
    margin-right: 10px;
    line-height: 35px;
    /* padding-top: 9px; */
}

.toolbar-button.scroll-to-begin-btn,
.toolbar-button.scroll-to-end-btn {
    display: none;
    position: absolute;
    z-index: 1000;
}


/* show scroll buttons if .show-scroll class is on */
.show-scroll .toolbar-button.scroll-to-begin-btn,
.show-scroll .toolbar-button.scroll-to-end-btn {
    display: block;
}

/* free space on left and right edge for scroll buttons, if .show-scroll class is on*/
.toolbar-horizontal.toolbar-scrollable.show-scroll {
    padding:  0 35px 0 35px;
}

/* for overCanvas mode, if .show-scroll class is on */
.toolbar-placeholder-right.toolbar-placeholder-inside .toolbar-horizontal.toolbar-scrollable.show-scroll {
    padding:  0 50px 0 35px;
}

/* free space on top and bottom edge for scroll buttons, if .show-scroll class is on */
.toolbar-vertical.toolbar-scrollable.show-scroll {
    padding:  35px 0 35px 0;
}

/* for overCanvas mode, if .show-scroll class is on */
.toolbar-placeholder-bottom.toolbar-placeholder-inside .toolbar-vertical.toolbar-scrollable.show-scroll {
    padding:  35px 0 50px 0;
}

/* horizontal toolbar scroll buttons positioning */
.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-begin-btn {
    left : 0;
    top: 0;
}
.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn {
    right : 0;
    top: 0;
}

/* for overCanvas mode, if .show-scroll class is on */
.toolbar-placeholder-bottom.toolbar-placeholder-inside .toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn {
    right : 15px;
    top: 0;
}

/* vertical toolbar scroll buttons positioning */
.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-begin-btn {
    left : 0;
    top: 0;
}
.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn {
    left : 0;
    bottom: 0;
}

/* for overCanvas mode, if .show-scroll class is on */
.toolbar-placeholder-right.toolbar-placeholder-inside .toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn {
    left : 0;
    bottom: 15px;
}

/* scroll buttons icons for horizontal toolbar */
.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-begin-btn i:before {
    content:"\f053";
}
.toolbar-horizontal.toolbar-scrollable .toolbar-button.scroll-to-end-btn i:before {
    content:"\f054";
}

/* scroll buttons icons for vertical toolbar */
.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-begin-btn i:before {
    content:"\f077";
}
.toolbar-vertical.toolbar-scrollable .toolbar-button.scroll-to-end-btn i:before {
    content:"\f078";
}

.toolbar-item-wrapper {
    display: inline-block;
    list-style: none;
    text-align: left;
    vertical-align: top;
    position: relative;
}

.toolbar-item-label {
    display: inline-block;
    vertical-align: top;
    width: auto;
    height: 100%;
    margin-right: 10px;
    line-height: 35px;
    /* padding-top: 9px; */
}

.toolbar-item-icon {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-position: center;
    line-height: 32px;
    text-align: center;
    background-color: #fff;
    cursor: pointer;

    transition: background-color 400ms linear;
}

.toolbar-item-icon:before {
    transition: color 400ms linear;
}

.toolbar-item-wrapper .toolbar-dropdown-block {
    display: inline-block;
    position: relative;
}



/*********************************/
.toolbar-item-wrapper.toolbar-button-item {
    width: 32px;
    height: 32px;
    border: 1px solid #E5E5E5;
    padding: 0;
    margin: 0 5px;
}

.toolbar-item-wrapper.toolbar-button-item:before{
    width: 32px;
    height: 32px;
    border: 1px solid #E5E5E5;
    padding: 0;
    margin: 0 5px;
    color: #333;
}


.toolbar-item-wrapper.toolbar-button-item.prevent-highlight .toolbar-item-icon {
    background-color: #fff !important;
}
.toolbar-item-wrapper.toolbar-button-item.prevent-highlight .toolbar-item-icon:before {
    color: #333 !important;
}

.toolbar-item-wrapper.toolbar-button-item:not(.option-value-inherited):not(.option-value-invalid):not(.option-value-multiple) .toolbar-item-icon {
    background-color: #1f78d8;
}

.toolbar-item-wrapper.toolbar-button-item:not(.option-value-inherited):not(.option-value-invalid):not(.option-value-multiple) .toolbar-item-icon:before {
    color: #fff;
}

.toolbar-item-wrapper.toolbar-button-item.option-value-inherited:not(.option-value-multiple) .toolbar-item-icon {
    background-color: #fff;
}

.toolbar-item-wrapper.toolbar-button-item.option-value-multiple .toolbar-item-icon {
    background-color: #dcdcdc;
}


.toolbar-item-wrapper.toolbar-button-item .toolbar-dropdown-block {
    position: absolute;
    left: -1px;
    top: 100%;
}

.toolbar-placeholder-bottom .toolbar-item-wrapper.toolbar-button-item .toolbar-dropdown-block {
    position: absolute;
    left: -1px;
    bottom: 100%;
    top: auto;
}

.toolbar-item-wrapper.toolbar-button-item.collapsed .toolbar-dropdown-block {
    display: none;
}

.toolbar-item-wrapper.toolbar-button-item .toolbar-item-label {
    display: none;
}

.toolbar-item-wrapper.toolbar-button-item .toolbar-item-icon {
    display: block;
}

.btn-toggle-canvas.active a,
.btn-toggle-canvas.dragging a {
    outline: none;
    background-color: #1f78d8;
    color: #fff;
}

.editable-canvas-toolbar.tool-minimized-toolbar {
    display: none;
}

.toolbar-item-description {
    display: inline-block;
    vertical-align: top;
}

.toolbar-item-description input{
    vertical-align: top;
}

.editable-canvas-toolbar input[type=range] {
    /*removes default webkit styles*/
    -webkit-appearance: none;

    /*fix for FF unable to apply focus style bug */
    border: 1px solid white;

    /*required for proper track sizing in FF*/
    width: 300px;

    padding: 0;
    height: 20px;
    top: 7px;
    overflow: hidden;
}

.editable-canvas-toolbar input[type=range]::-webkit-slider-runnable-track {
    width: 300px;
    height: 5px;
    background: #ddd;
    border: none;
    border-radius: 3px;
}
.editable-canvas-toolbar  input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #1f78d8;
    margin-top: -5px;
}
.editable-canvas-toolbar  input[type=range]:focus {
    outline: none;
}
.editable-canvas-toolbar  input[type=range]:focus::-webkit-slider-runnable-track {
    background: #ccc;
}

.editable-canvas-toolbar input[type=range]::-moz-range-track {
    width: 300px;
    height: 5px;
    background: #ddd;
    border: none;
    border-radius: 3px;
}

.editable-canvas-toolbar  input[type=range]::-moz-range-thumb {
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #1f78d8;
}

/*hide the outline behind the border*/
.editable-canvas-toolbar input[type=range]:-moz-focusring{
    outline: 1px solid white;
    outline-offset: -1px;
}

.editable-canvas-toolbar  input[type=range]::-ms-track {
    width: 300px;
    height: 5px;

    /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */
    background: transparent;

    /*leave room for the larger thumb to overflow with a transparent border */
    border-color: transparent;
    border-width: 6px 0;

    /*remove default tick marks*/
    color: transparent;
}

.editable-canvas-toolbar input[type=range]::-ms-fill-lower {
    background: #777;
    border-radius: 10px;
}

.editable-canvas-toolbar input[type=range]::-ms-fill-upper {
    background: #ddd;
    border-radius: 10px;
}

.editable-canvas-toolbar input[type=range]::-ms-thumb {
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #1f78d8;
}

.editable-canvas-toolbar input[type=range]:focus::-ms-fill-lower {
    background: #888;
}

.editable-canvas-toolbar input[type=range]:focus::-ms-fill-upper {
    background: #ccc;
}
.toolbar-combobox-wrapper {
    border: 1px solid #E5E5E5;
    width: auto;
    min-width: 120px;
    outline: none;
    color: #444;
    margin: 2px auto;
    display: inline-block;
    box-sizing: border-box;

    position: relative;
}

.toolbar-placeholder-bottom .toolbar-combobox-wrapper {
    position: absolute;
    bottom: 100%;
    top: auto;
}

.toolbar-combobox-wrapper > .selected {
    float: left;
    height: 28px;
    line-height: 20px;
    display: table;
    width: 100%;
    cursor: pointer;

    transition: background-color 400ms linear;
}

.toolbar-combobox-wrapper > .selected span {
    display: table-cell;
    vertical-align: middle;
    padding: 0 13px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-size: 14px;
}

/****/

.toolbar-combobox-wrapper.prevent-highlight {

}

.toolbar-combobox-wrapper.option-value-inherited > .selected {
    background-color: #dcdcdc;
}

.toolbar-combobox-wrapper.option-value-inherited.button-mode > .selected {

}

.toolbar-combobox-wrapper.option-value-invalid > .selected {

}

.toolbar-combobox-wrapper.option-value-invalid.button-mode > .selected {

}

.toolbar-combobox-wrapper.option-value-multiple > .selected {
    background-color: #efb5b5;
}

.toolbar-combobox-wrapper.option-value-multiple.button-mode > .selected {

}

/****/

.toolbar-combobox-wrapper.hightlight-red > .selected .ui-button {
    background: white;
}

.toolbar-combobox-wrapper .dropdown-box {
    border-top: none;
    position: relative;
    box-sizing: border-box;
    z-index: 2;
    background-color: #fff;
    padding: 5px 0;
    top: 29px;

    /*border: 1px solid #E5E5E5;*/
    /*left: -1px;*/
    /*right: -1px;*/
}

.toolbar-combobox-wrapper:not(.button-mode) .toolbar-item-description {
    display: inline-block;
}

.toolbar-combobox-wrapper .inputbox {
    clear: both;
    margin: 5px 5px 0;
    display: none;
}

.toolbar-combobox-wrapper.edit-mode .inputbox {
    display: block;
}

.toolbar-combobox-wrapper .inputbox input[type=text] {
    border: 1px solid #E5E5E5;
    width: 100%;
    padding: 5px 5px;
    outline: none;
}

.toolbar-combobox-wrapper .option-list {
    clear: both;
    list-style: none;
    padding: 0;
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
    -webkit-margin-start: 0;
    -webkit-margin-end: 0;
    -webkit-padding-start: 0;
    margin-top: 5px;

    width: 100%;
    max-height: 195px;
    position: relative;
    overflow-y: visible;
    overflow-x: auto;
    display: inline-block;
}

.toolbar-combobox-wrapper .option-list .option-item {
    margin: 2px 5px;
    line-height: 30px;
    cursor: pointer;
    padding: 0 8px;
    font-size: 12px;
    color: #333;
    display: block;
}

.toolbar-combobox-wrapper .option-list .option-item .option-item-text{
    display: inline-block;
    white-space: nowrap;
}


.toolbar-combobox-wrapper .option-list .option-item:first-letter {
    text-transform: uppercase;
}

.toolbar-combobox-wrapper .option-list .option-item:not(.selected):hover {
    background-color: #EEEEEE;
}

.toolbar-combobox-wrapper .option-list .option-item.selected {
    background-color: #d9d9d9;
}

.toolbar-combobox-wrapper .ui-button {
    border-left: 1px solid #E5E5E5;
    float: right;
    display: table-cell;
    cursor: pointer;
    height: 100%;
    color: #999999;
    width: 40px;
    text-align: center;
    background-color: #fff;
}

.toolbar-combobox-wrapper .ui-button i {
    margin-top: 7px;
}

.toolbar-combobox-wrapper:focus {
    border-color: #999999;
}

.toolbar-combobox-wrapper:focus .dropdown-box {
    border-color: #999999;
}

.toolbar-combobox-wrapper:focus .ui-button {
    border-left: none;
}

.toolbar-combobox-wrapper.focus {
    border-color: #999999;
}

.toolbar-combobox-wrapper.focus .dropdown-box {
    border-color: #999999;
}

.toolbar-combobox-wrapper.focus .ui-button {
    border-left: none;
}

.toolbar-combobox-wrapper.button-mode {

}

.toolbar-item-wrapper:not(.toolbar-button-item)  .toolbar-combobox-wrapper .dropdown-box.collapsed,
.toolbar-item-wrapper:not(.toolbar-button-item)  .toolbar-combobox-wrapper.collapsed .dropdown-box{
    display: none;
}

/***********/

.toolbar-button-item .toolbar-combobox-wrapper {
    margin: 0 0 -1px;
}

.toolbar-button-item .toolbar-combobox-wrapper > .selected,
.combobox-cloned.toolbar-combobox-wrapper > .selected{
    display: none;
}

.toolbar-button-item .toolbar-combobox-wrapper > .dropdown-box,
.combobox-cloned.toolbar-combobox-wrapper > .dropdown-box{
    top: 0;
    display: block;
}

.toolbar-button-item .collapsed .toolbar-combobox-wrapper:not(.combobox-cloned) {
    display: none;
}
.tooltip-transparent {
    opacity: 0 !important;
    display: block !important;
    visibility: visible !important;
}


.editable-canvas-tooltip {
    position: absolute;
    background: black;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    padding: 5px 8px;
    transition: opacity 0.2s;
    opacity: 0;
    z-index: 100001;
    display: block;
    pointer-events: none
}

.editable-canvas-tooltip.active {
    opacity: 1;
}

.editable-canvas-tooltip:after {
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
}

.editable-canvas-tooltip[positionX=left][positionY=top] {
    border-bottom-right-radius: 0;
}

.editable-canvas-tooltip[positionX=left][positionY=top]:after {
    left:100%;
    bottom:0;

    border-width: 10px 0 0 10px;
    border-color: transparent transparent transparent black;
}

.editable-canvas-tooltip[positionX=left][positionY=bottom] {
    border-top-right-radius: 0;
}

.editable-canvas-tooltip[positionX=left][positionY=bottom]:after {
    left:100%;
    top:0;

    border-width: 10px 10px 0 0;
    border-color: black transparent transparent transparent;
}

.editable-canvas-tooltip[positionX=right][positionY=bottom] {
    border-top-left-radius: 0;
}

.editable-canvas-tooltip[positionX=right][positionY=bottom]:after {
    right:100%;
    top:0;


    border-width: 0 10px 10px 0;
    border-color: transparent black transparent transparent;
}

.editable-canvas-tooltip[positionX=right][positionY=top] {
    border-bottom-left-radius: 0;
}

.editable-canvas-tooltip[positionX=right][positionY=top]:after {
    right: 100%;
    bottom: 0;

    border-width: 0 0 10px 10px;
    border-color: transparent transparent black transparent;
}

.editable-canvas-tooltip[positionX=right][positionY=center]:after {
    top: 50%;
    right: 100%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);

    border-width: 5px 10px 5px 0;
    border-color: transparent black transparent transparent;
}

.editable-canvas-tooltip[positionX=left][positionY=center]:after {
    top: 50%;
    left: 100%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);

    border-width: 5px 0 5px 10px;
    border-color: transparent transparent transparent black;
}

.editable-canvas-tooltip[positionX=center][positionY=top]:after {
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);

    border-width: 10px 5px 0 5px;
    border-color: black transparent transparent transparent;
}

.editable-canvas-tooltip[positionX=center][positionY=bottom]:after {
    bottom: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);

    border-width: 0 5px 10px 5px;
    border-color: transparent transparent black transparent;
}
label.background-transparency {
    display: inline !important;
    width: auto;
    font-weight: normal;
    padding-left: 10px !important;
}
#redactor-drawer-box.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: auto !important;
    height: auto !important;
    background: #fff !important;
    transition: none !important;
    z-index: 100000;
}

#redactor-drawer-box.fullscreen .toolbar-button.btn-delete-canvas,
#redactor-drawer-box.fullscreen .toolbar-button.btn-move,
#redactor-drawer-box.fullscreen .toolbar-button.btn-minimize-canvas,
#redactor-drawer-box.fullscreen .toolbar-button.btn-restore-canvas,
#redactor-drawer-box.fullscreen .redactor-drawer-resizer{
    display: none !important;
}

#redactor-drawer-box.fullscreen .fullscreenOverOther {
    z-index: 2000 !important;
}

#redactor-drawer-box.fullscreen .canvas-container {
    width: 100% !important;
    height: 100% !important;
}

#redactor-drawer-box.animation.fullscreen,
#redactor-drawer-box.fullscreen-in-progress {
    transition: none !important;
}

#redactor-drawer-box.fullscreen-in-progress .toolbars-wrapper {
    opacity: 0 !important;
}
.crop-container {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 20000;
    background: #fff;
}

.crop-container .cr-image {
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    transform-origin: 0 0;
    max-height: none;
    max-width: none;
}

.crop-container .cr-image {
    z-index: -2;
    position: absolute;
    top: 0;
    left: 0;
    transform-origin: 0 0;
    max-height: none;
    max-width: none;
    opacity: 0;
}

.crop-container .cr-boundary {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    z-index: 1;
    width: 100%;
    height: 100%;
}

.crop-container .cr-viewport {
    position: absolute;
    border: 2px solid #fff;
    margin: auto;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    box-shadow: 0 0 2000px 2000px rgba(0, 0, 0, 0.5);
    z-index: 100;
    pointer-events: none;
}

.crop-container .cr-original-image {
    display: none;
}

.crop-container .cr-vp-circle {
    border-radius: 50%;
}

.crop-container .cr-overlay {
    z-index: 1;
    position: absolute;
    cursor: move;
}

.crop-container .cr-slider-wrap {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    margin: 15px auto;
    text-align: center;
}

.croppie-result {
    position: relative;
    overflow: hidden;
}

.croppie-result img {
    position: absolute;
}

.crop-container .cr-image,
.crop-container .cr-overlay,
.crop-container .cr-viewport {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.cr-viewport-resizer {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    width: inherit;
    height: inherit;
}

.cr-viewport-resizer.resizing {
    border: 2px dotted black;
    background: rgba(255, 255, 255, 0.2);
    margin: 0;
    box-sizing: content-box;
}

.cr-viewport-resizer-handler {
    position: absolute;
    left: 100%;
    top: 100%;
    width: 15px;
    height: 15px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    background: #5c5c5c;
    cursor: nwse-resize;
    pointer-events: auto;
}

.cr-controls-wrapper {
    position: absolute;
    z-index: 200;
    right: 5px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.cr-controls-wrapper .btn {
    display: block;
    margin: 5px auto;
}

/*************************************/
/***** STYLING RANGE INPUT ***********/
/*************************************/
/*http://brennaobrien.com/blog/2014/05/style-input-type-range-in-every-browser.html */
/*************************************/

.cr-slider {
    -webkit-appearance: none;
    /*removes default webkit styles*/
    /*border: 1px solid white; *//*fix for FF unable to apply focus style bug */
    width: 300px;
    /*required for proper track sizing in FF*/
    max-width: 100%;
    padding-top: 8px;
    padding-bottom: 8px;
    background-color: transparent;
    cursor: pointer;
}

.cr-slider::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: rgba(0, 0, 0, 0.5);
    border: 0;
    border-radius: 3px;
}

.cr-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #ddd;
    margin-top: -6px;
}

.cr-slider:focus {
    outline: none;
}

/*
.cr-slider:focus::-webkit-slider-runnable-track {
background: #ccc;
}
*/

.cr-slider::-moz-range-track {
    width: 100%;
    height: 3px;
    background: rgba(0, 0, 0, 0.5);
    border: 0;
    border-radius: 3px;
}

.cr-slider::-moz-range-thumb {
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #ddd;
    margin-top: -6px;
}

/*hide the outline behind the border*/
.cr-slider:-moz-focusring {
    outline: 1px solid white;
    outline-offset: -1px;
}

.cr-slider::-ms-track {
    width: 100%;
    height: 5px;
    background: transparent;
    /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */
    border-color: transparent; /*leave room for the larger thumb to overflow with a transparent border */
    border-width: 6px 0;
    color: transparent; /*remove default tick marks*/
}

.cr-slider::-ms-fill-lower {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
}

.cr-slider::-ms-fill-upper {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
}

.cr-slider::-ms-thumb {
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #ddd;
    margin-top: 1px;
}

.cr-slider:focus::-ms-fill-lower {
    background: rgba(0, 0, 0, 0.5);
}

.cr-slider:focus::-ms-fill-upper {
    background: rgba(0, 0, 0, 0.5);
}

/*******************************************/

/***********************************/
/* Rotation Tools */
/***********************************/
.cr-rotate-controls {
    position: absolute;
    bottom: 5px;
    left: 5px;
    z-index: 1;
}

.cr-rotate-controls button {
    border: 0;
    background: none;
}

.cr-rotate-controls i:before {
    display: inline-block;
    font-style: normal;
    font-weight: 900;
    font-size: 22px;
}

.cr-rotate-l i:before {
    content: '↺';
}

.cr-rotate-r i:before {
    content: '↻';
}

.image-crop-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 201;
    overflow: hidden;
}

.cr-image {
    max-width: 100%;
    max-height: 100%;
    top: 50%;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.cr-image-fullsize {
    opacity: 0;
}

.imager-crop-container {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1300;
}

.imager-crop-canvas {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.crop-corner {
    position: absolute;
    width: 14px;
    height: 14px;
    border: 1px solid rgb(60, 58, 58);
    background: rgb(255, 255, 255);
    z-index: 1301;
}

.crop-top-left {
    left: 0;
    top: 0;
    cursor: nwse-resize;
}

.crop-top-right {
    right: 0;
    top: 0;
    cursor: nesw-resize;
}

.crop-bottom-right {
    right: 0;
    bottom: 0;
    cursor: nwse-resize;
}

.crop-bottom-left {
    left: 0;
    bottom: 0;
    cursor: nesw-resize;
}

.crop-border {
    /*background: url(../../assets/border.gif);*/
    position:absolute;
}

.crop-border.crop-border-top {
    width: 100%;
    height: 1px;
    top: 4px;
}

.crop-border.crop-border-right {
    width: 1px;
    height: 100%;
    right: 2px;
}

.crop-border.crop-border-bottom {
    width: 100%;
    height: 1px;
    bottom: 2px;
}

.crop-border.crop-border-left {
    width: 1px;
    height: 100%;
    left: 4px;
}
.minimized {
    border: none !important;
    height: auto !important;
    width: auto !important;
}

.minimized .toolbar-placeholder.toolbar-placeholder-top.toolbar-placeholder-inside,
.minimized .toolbars-wrapper {
    position: static;
}

/* Elements to hide */
.minimized .canvas-container,
.minimized .editable-canvas-toolbar:not(.tool-minimized-toolbar),
.minimized .toolbar-button.btn-fullscreen-canvas,
.minimized span.redactor-drawer-resizer{
    display: none;
}

/* Hide toolbar while normal mode */
.editable-canvas-toolbar.tool-minimized-toolbar {
    display: none;
}

.minimized .editable-canvas-toolbar.tool-minimized-toolbar {
    display: inline-block;
}

.minimized .editable-canvas-toolbar.tool-minimized-toolbar .toolbar-content-wrapper {
    white-space: nowrap;
    display: inline-block;
}

/* Caption of canvas */
.canvas-caption {
    list-style-type: none;
    line-height: 32px;
    margin: 0 5px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    background: white;
}
.popup-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1106;
}

.popup-wrapper .popup-content-wrapper{
    position: absolute;
    background-color: #fff;
    z-index: 1;
    max-width: 300px;
    border: 1px solid #ccc;
}

.popup-wrapper .popup-content-wrapper.popup-transparent {
    opacity: 0;
}

/***
 * Arrow styles
 */
.popup-wrapper .popup-content-wrapper .popup-arrow {
    width: 10px;
    height: 10px;
    z-index: 111;
    position: absolute;
    margin-left: -5px;
    margin-top: -5px;
}

.popup-wrapper .popup-content-wrapper .popup-arrow:after,
.popup-wrapper .popup-content-wrapper .popup-arrow:before{
    content: "";
    display: block;
    border: none;
    position: absolute;
    border-width: 0;
    border-style: solid;

    margin-left: -5px;
    margin-top: -5px;
}

.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow {
    bottom: auto !important;
    top: 100% !important;
}

.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:after,
.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:before {
    margin-top: 5px;
    border-width: 10px 10px 0 10px;
}

.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:after{
    border-color: #fff transparent transparent   transparent;
    margin-top: 3px;
}

.popup-wrapper .popup-content-wrapper[data-position=top] .popup-arrow:before {
    border-color: #ccc  transparent transparent transparent;
}

.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow {
    right: auto !important;
    left: 100% !important;
}

.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:after,
.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:before {
    margin-left: 5px;
    border-width: 10px 0 10px 10px;
}

.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:after{
    border-color: transparent transparent transparent #fff;
    margin-left: 3px;
}

.popup-wrapper .popup-content-wrapper[data-position=left] .popup-arrow:before {
    border-color: transparent transparent transparent #ccc;
}

.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow {
    right: 100% !important;
    left: auto !important;

}

.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:after,
.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:before {
    margin-left: 0;
    border-width: 10px 10px 10px 0;
}

.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:after{
    margin-left: 2px;
    border-color: transparent #fff transparent transparent;
}

.popup-wrapper .popup-content-wrapper[data-position=right] .popup-arrow:before {
    border-color: transparent #ccc transparent transparent;
}

.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow {
    margin-top: 0;
    bottom: 100% !important;
    top: auto !important;

}

.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:after,
.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:before {
    border-width: 0 10px 10px 10px;
}

.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:after{
    border-color:  transparent transparent #fff transparent;
    margin-top: 2px;
}

.popup-wrapper .popup-content-wrapper[data-position=bottom] .popup-arrow:before {
    border-color:  transparent transparent #ccc transparent;
    margin-top: 0px;
}

/*******************
*
*
*/

.popup-wrapper .popup-content{
    padding: 10px;

    box-sizing: border-box;

    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;

    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;

    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;

    -webkit-align-content: center;
    -ms-flex-line-pack: center;
    align-content: center;

    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.popup-wrapper .popup-overlay{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    background-color: #D5D5D5;
    opacity: 0.6;
}

.popup-wrapper .popup-close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 32px;
    height: 32px;
    z-index: 1007;
    cursor: pointer;
    text-align: center;
    line-height: 32px;
}

.popup-wrapper .popup-content .toolbar-placeholder{
    position: static;
}

.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range {
    width: 100%;
    min-width: 100%;
    padding: 0;
    position: relative;
    height: 35px;
}

.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range input[type=range] {
    float: right;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
}

.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-item-description {
    position: absolute;
    top: 0;
    left: 0;
    right: 110px;
    padding: 0 0 0 8px;
}



.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-label {
    float: left;
    text-align: left;
    padding: 0;
}

.popup-wrapper .popup-content .toolbar-placeholder .toolbar-item-range .toolbar-label.toolbar-label-indicator {
    float: right;
    padding: 0;
}
.redactor-drawer-resizer {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0%;
    top: 0%;
}

.redactor-drawer-resizer .resizer-box {
    position: absolute;
    right: 0px;
    bottom: 0px;
    height: 15px;
    width: 15px;
    z-index: 1003;
    background: rgb(92, 92, 92);
    cursor: nwse-resize;
}

.redactor-drawer-resizer .resizer-box.touch {
    height: 25px;
    width: 25px;
}

.toolbar-placeholder-bottom.toolbar-placeholder-inside .editable-canvas-toolbar:after,
.toolbar-placeholder-right.toolbar-placeholder-inside .editable-canvas-toolbar:after {
    position: absolute;
    background: #fff;
    display: block;
    content: "";
    z-index: 1105;
    /*opacity:0;*/
}

.toolbar-placeholder-bottom.toolbar-placeholder-inside .editable-canvas-toolbar:after {
    right: -1px;
    top: 0px;
    bottom: 0px;
    width: 17px;
}

.toolbar-placeholder-right.toolbar-placeholder-inside .editable-canvas-toolbar:after {
    right: 0px;
    left: 0px;
    bottom: -1px;
    height: 17px;
}
.editable-canvas-shape-context-menu {
    position: absolute;
    background: white;
    box-shadow: 0 10px 50px rgba(0,0,0,0.15);
    z-index: 99999;
    width: 200px;
    height:auto !important;
    text-align: left;
    padding:0;
}

.editable-canvas-shape-context-menu li a {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.editable-canvas-shape-context-menu li {
    width: 100%;
    list-style-type:none;
}

.editable-canvas-shape-context-menu li a {
    text-align: left;
    display: inline-block;
    margin: 0;
    width: 100%;
    padding: 10px 20px;
    cursor:pointer;
}

.editable-canvas-shape-context-menu li a:hover {
    text-decoration: none;
    background: #1f78d8;
    color: white;
}
.zoom-tooltip {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #ccc;
    padding: 5px;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    z-index: 20000000;
    border-radius: 5px;
    opacity: 0.85;

    pointer-events: none;
    transition: opacity 500ms linear;
}

.zoom-tooltip.transparent-tooltip {
    opacity:0;
}
ul.editable-canvas-toolbar li.editable-canvas-brushsize {
    width: auto;
    padding-left: 10px !important;
    padding-right: 10px;
}


li.editable-canvas-brushsize .editable-canvas-brushsize-indicator{
    width: 50px;
    display: inline-block;
    margin-right: 5px;
}

.popup-wrapper .popup-content .editable-canvas-toolbar li.editable-canvas-brushsize {
    width: 100%;
    min-width: 100%;
    margin: 0;
    padding: 0 10px;
    box-sizing: border-box;
    text-align: right;
}

.popup-wrapper .popup-content .editable-canvas-toolbar li.editable-canvas-brushsize .editable-canvas-brushsize-input{
    float: right;
}

.popup-wrapper .popup-content li.editable-canvas-brushsize .editable-canvas-brushsize-indicator {
    float: none;
    text-align: left;
}


li.editable-canvas-brushsize .editable-canvas-brushsize-input {
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
    position: relative;
    right: 0;
}

.touch li.drawer-plugin-brushsize {
    padding-top: 10px !important;
    padding-left: 10px !important;
}
.colorpicker-control .color-dropdown {
    position: absolute;
    padding: 5px;
}

li.colorpicker-control {
    padding-left: 10px !important;
    width: auto !important;
}

/*li.colorpicker-control span.toolbar-label {
    display: inline-block;
    width: auto;
    margin-right: 10px;
    float: left;
    vertical-align: middle;
    padding-top: 9px;
}
*/


.colorpicker-control span.color-indicator {
    display: inline-block;
    width: 32px;
    position: relative;
    top: 0px;
    height: 32px;
    background-color: white;
}

.colorpicker-control .color-swatch {
  box-sizing: border-box;
  float : left;
  width: 32px;
  height: 32px;
  padding : 0;
  margin : 0;
  border : 1px solid white;
}

.colorpicker-control .color-swatch.transparent {
  width: 100%;
  background-color: white;
  text-align:  center;
  margin : 0;
}

.control-hidden {
  visibility: hidden !important;
}

.color-dropdown {
    max-width: 365px;
    max-height: 365px;
    z-index: 1;

    background: #fff;
    border: 1px solid #ccc;

    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;

    -webkit-align-content: center;
    -ms-flex-line-pack: center;
    align-content: center;

    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;

    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.color-dropdown.palette-with-scroll {
    display: block;
}

.popup-content-wrapper .popup-content li.colorpicker-control {
    min-width: 50%;
}

.popup-content-wrapper .popup-content li.colorpicker-control.editable-canvas-text-option {
    min-width: 177px;
    height: 40px;
    line-height: 40px !important;
}

.popup-content-wrapper .popup-content li.colorpicker-control span.color-indicator {
    right: 8px;
    float: right;
}

.popup-content-wrapper .popup-content li.colorpicker-control.editable-canvas-text-option span.color-indicator {
    right: 1px;
    float: right;
    top: 4px;
}

.popup-content-wrapper .popup-content .color-dropdown {
    overflow: auto;
    background: #fff;
    border: 1px solid #ccc;
}

.popup-content-wrapper .popup-content .color-dropdown:not([data-position]){
    width: 365px;

    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.toolbar-placeholder-bottom .color-dropdown,
.popup-content-wrapper .popup-content[data-position=top] .color-dropdown {
    left: 0px;
    bottom: 100%;
    max-width: 365px;

    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;

    -webkit-flex-wrap: wrap-reverse;
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}

.toolbar-placeholder-top .color-dropdown,
.popup-content-wrapper .popup-content[data-position=bottom] .color-dropdown {
    left: 0px;
    top: 100%;
    max-width: 375px;

    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;

    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.toolbar-placeholder-left .color-dropdown,
.popup-content-wrapper .popup-content[data-position=right] .color-dropdown {
    left: 100%;
    top: 0;
    max-height: 375px;

    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;

    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.toolbar-placeholder-right .color-dropdown,
.popup-content-wrapper .popup-content[data-position=left] .color-dropdown {
    right: 100%;
    top: 0;
    max-height: 375px;

    padding-bottom: 14px;

    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;

    -webkit-flex-wrap: wrap-reverse;
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}
li.editable-canvas-color-redactor {
    padding-left: 10px !important;
    width: auto !important;
}

/*li.editable-canvas-color-redactor span.toolbar-label {
    display: inline-block;
    width: auto;
    margin-right: 10px;
    float: left;
    vertical-align: middle;
    padding-top: 9px;
}
*/

li.editable-canvas-color-redactor span.color-dropdown {
    position: absolute;
    left:0px;
    top:30px;
    width: 290px;
}

li.editable-canvas-color-redactor span.color-indicator {
    display: inline-block;
    width: 32px;
    position: relative;
    top: 0px;
    height: 32px;
    background-color: white;
}

.toolbar-topRight li.editable-canvas-color-redactor span.color-dropdown {
    right: 0;
    left: auto;
}



ul.editable-canvas-toolbar li.editable-canvas-opacity-label {
    width: auto;
    padding-right: 3px;
    padding-left: 3px;
}

ul.editable-canvas-toolbar li.editable-canvas-opacity {
    width: auto;
    margin-right: 10px;
    margin-left: 10px;
}


li.editable-canvas-opacity .editable-canvas-opacity-indicator{
    width: 50px;
    display: inline-block;
    margin-right:  5px;
}



li.editable-canvas-opacity .editable-canvas-opacity-input {
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
    position: relative;
    right: 0;
}

.popup-content .editable-canvas-toolbar li.editable-canvas-opacity {
    width: 100%;
    min-width: 100%;
    margin: 0;
    padding: 0 10px;
    box-sizing: border-box;
    text-align: right;
}

.popup-content li.editable-canvas-opacity .editable-canvas-opacity-input{
    float: right;
}

.popup-content .editable-canvas-toolbar li.editable-canvas-opacity .editable-canvas-opacity-indicator {
    float: none;
    text-align: left;
}

.editable-canvas-colorpicker {
    width: 32px;
    background: white;
    height: 32px;
    border: none;
    -webkit-appearance: none;
    position: relative;
}

.editable-canvas-colorpicker::-webkit-color-swatch-wrapper {
    padding: 0;
}

.editable-canvas-colorpicker::-webkit-color-swatch {
    border: none;
}

ul.editable-canvas-toolbar li.editable-canvas-plugin-color .toolbar-label {
    position: relative;
    top: -9px;
}

ul.editable-canvas-toolbar li.editable-canvas-plugin-color {
    width: auto;
    padding-left: 10px;
    text-align: left;
}
.editable-canvas-line-width .toolbar-item-description {
    display: inline-block;
}

.editable-canvas-line-width input.editable-canvas-line-width-input {
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
    position: relative;
    right: 0;
}

.editable-canvas-line-width .editable-canvas-line-width-label {
    padding-left: 10px;
}

span.toolbar-label.editable-canvas-line-width-indicator {
    width: 50px;
    display: inline-block;
    margin-right: 5px;
}


.editable-canvas-opacity-option-indicator {
    width: 50px;
    display: inline-block;
    margin-right: 5px;
}

li.editable-canvas-opacity-option .editable-canvas-opacity-option-input {
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
    position: relative;
    right: 0;
}

.editable-canvas-opacity-option-label {
    padding-left: 10px ;
}

.popup-content .editable-canvas-toolbar li.editable-canvas-opacity-option {
    width: 100%;
    min-width: 100%;
    margin: 0;
    padding: 0 10px;
    box-sizing: border-box;
    text-align: right;
}

.popup-content li.editable-canvas-opacity-option .editable-canvas-opacity-option-input{
    float: right;
}

.popup-content .editable-canvas-toolbar li.editable-canvas-opacity-option .editable-canvas-opacity-option-indicator {
    float: none;
    text-align: left;
}
.editable-canvas-stroke-width .toolbar-item-description {
    display: inline-block;
}

.editable-canvas-stroke-width input.editable-canvas-stroke-width-input {
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin-left: 7px;
    margin-right: 7px;
    position: relative;
    right: 0;
}

.editable-canvas-stroke-width .editable-canvas-stroke-width-label {
    padding-left: 10px;
}

span.toolbar-label.editable-canvas-stroke-width-indicator {
    width: 50px;
    display: inline-block;
    margin-right: 5px;
}
.editable-canvas-text-option {
    padding-left: 10px;
    padding-right: 10px;
}

.editable-canvas-text-option select{
    margin: 7px auto;
}

ul.editable-canvas-toolbar li.editable-canvas-fontfamily {
    cursor: pointer;
    width: auto;
    height: 100%;
    position: relative;
}

ul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown {
    position: absolute;
    left: 0px;
    top: 30px;
    width: 100%;
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.15);
    z-index: 99999;
    background: white;
    width: 300px;
    height: auto !important;
    padding-left: 5px !important;
}

ul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown li {
    display: block;
    background: white;
    text-align: left;
    float: left;
    width: auto;
    height: auto;
}

ul.editable-canvas-toolbar li.editable-canvas-fontfamily .fonts-dropdown li a {
    padding: 5px;
}

ul.editable-canvas-toolbar.toolbar-topRight li.editable-canvas-fontfamily .fonts-dropdown {
    left: auto;
    right: 0;
}
.editable-canvas-border-type {
    width: auto !important;
    position: relative;
    padding-left: 8px;
}


.popup-content-wrapper .popup-content .editable-canvas-border-type .toolbar-label {
    margin-right: 6px;
}

.editable-canvas-border-type .border-type-indicator {
    margin-top: 8px;
    display: inline-block;
    width: 60px !important;
    height: 20px;
    line-height: 20px;

    padding: 0 8px;
    box-sizing: content-box;
    text-align: center;
    border: 1px solid #ccc;

    background-position: center;
    background-repeat: no-repeat;
}

.border-type-dropdown {
    width: 66px;
    right: 0;
    left: auto;
    transform: none;
    background: #fff;
    padding: 5px;
    border: 1px solid #ccc;
    box-sizing: content-box;
    display: inline-block;
    position: absolute;
    text-align: center;
}

.border-type-dropdown ul li {
    display: block;
    width: 100%;
    background-position-y: 50%;
    background-repeat: no-repeat;
    cursor: pointer;
    height: 20px;
    line-height: 20px;
}

.border-type-dropdown ul li:hover {
    background-color: #1f78d8;
}

.toolbar-placeholder-bottom .border-type-dropdown,
.popup-content-wrapper .popup-content .border-type-dropdown{
    bottom: 100%;
}

.toolbar-placeholder-top .border-type-dropdown{
    top: 100%;
}

.toolbar-placeholder-left .border-type-dropdown{
    left: 100%;
    top: 0;
}

.toolbar-placeholder-right .border-type-dropdown{
    right: 100%;
    top: 0;
}

.border-type-dropdown.toolbar-dropdown-block.border-type-dropdown-cloned {
    width: 66px;
    text-align: center;
}

.border-type-dropdown-cloned ul {
    padding: 0;
}
.toolbar-button .fa.fa-long-arrow-right ,
.toolbar-button .fa.fa-arrows-h {
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.toolbar-button .fa-line {
    width: 14px;
    -ms-transform: rotate(24deg);
    -webkit-transform: rotate(24deg);
    -moz-transform: rotate(24deg);
    transform: rotate(24deg);
}
.toolbar-button  .fa-line:before {
    position: relative;
    line-height: 10px;
    top: 2px;
    content: "/";
    font-size: 19px !important;
    font-weight: bold;
}

.drawer-tool-mouse-tooltip .fa-line {
    position: relative;
    left: 5px;
    top: 2px;
}
button.stop-polygon {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1000;

    background: white;
    border: 1px solid rgb(226, 226, 226);
    padding: 6px 15px;
    box-shadow: 0px 0px 5px rgba(0,0,0,0.1);
}

.btn-triangle .fa-play,
.drawer-tool-mouse-tooltip .fa-play{
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);
}

.drawer-tool-mouse-tooltip {
    opacity: 0;
    position:absolute;
    margin-left: 10px;
    margin-top: 10px;
    padding:5px;
    background: #f5f5f5;
    border: 1px solid #efefef;
    border-radius: 4px;
    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.03);
    z-index: 99999;
    padding-left: 10px;
    padding-right: 10px;
    pointer-events: none;
    white-space: nowrap;
    transition: opacity 0.2s;
}
body {
    background: transparent;
    overflow: hidden !important;
}

.hidden {
    display: none !important;
    visibility: hidden !important
}

#redactor-drawer-box .sp-replacer {
    padding: 0;
    margin: 0;
    width: 32px;
    height: 32px;
}

#redactor-drawer-box .sp-preview {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}

#redactor-drawer-box .sp-preview-inner {
    height: 100%;
    width: 100%;
}

#redactor-drawer-box .sp-dd {
    display: none;
}

#redactor-drawer-box {
    vertical-align: middle;
    outline: none;
    top: 35px;
    background: white;
    transition: none;
    background: transparent;
    box-sizing: content-box;
}

#redactor-drawer-box.animated {
    transition: height 0.5s ease-out,
    width 0.5s ease-out 0s,
    left 0.5s ease-out 0s,
    top 0.5s ease-out 0s;
}

body.drawer-moving #redactor-drawer-box.drawer-instance-container,
body.drawer-moving #redactor-drawer-box.drawer-instance-container .canvas-container,
body.drawer-resizing #redactor-drawer-box.drawer-instance-container,
body.drawer-resizing #redactor-drawer-box.drawer-instance-container .canvas-container{
    transition: none;
}

body.drawer-zoom-moving,
body.drawer-zoom-moving * {
    cursor: -webkit-grabbing !important;;
    cursor: grabbing !important;
}

body.drawer-moving,
body.drawer-moving * {
    cursor: move !important;
}

body.drawer-resizing,
body.drawer-resizing * {
    cursor: nwse-resize !important;
}


#redactor-drawer-box .canvas-container {
    margin: 0;
    box-shadow: 0px 0px 50px rgba(0, 0, 0, 0.03);
    z-index: 200;
    overflow: hidden;
    transition: none;
}

#redactor-drawer-box.animated .canvas-container{
    transition: height 0.5s ease-out,
    width 0.5s ease-out 0s,
    left 0.5s ease-out 0s,
    top 0.5s ease-out 0s;
}

#redactor-drawer-box .canvas-container canvas {
    background: transparent;
}

.noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/**
 * DrawerJs properties modal.
 */
#redactor-drawer-properties .drawer-properties-width,
#redactor-drawer-properties .drawer-properties-height {
    width: 45%;
    text-align: center;
}

#redactor-drawer-properties .size-separator {
    width: 10%;
    display: inline-block;
    text-align: center;
}

#redactor-drawer-box {
    font-family: 'Arial', sans-serif;
}

.redactor-editor .btn-group ul {
    position: absolute;
    left: -2px;
    top: 30px;
    padding: 0;
    background: white;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
}


.editable-canvas-image {
    opacity: 1;
}

.editable-canvas-image.edit-mode {
    opacity: 0;
}

#redactor-modal-overlay, #redactor-modal-box, #redactor-modal {
    z-index: 1100 !important;
}
/*# sourceMappingURL=drawerJs.css.map */