Locales['en'] = {
    ['non_permission'] = 'You dont have permission to edit this whiteboard',
    ['edit_content'] = 'Edit content',
    ['read_only'] = 'Read only board',
    ['menu_help'] = '~INPUT_CONTEXT~ to open editor',
    ['invalid_player_id'] = 'Invalid player id!',
    ['not_permission_editor'] = 'You don\'t have sufficient authorization',
    ['not_valid_model'] = 'Use /rw_editor [model], you can find all models in config. Example /rw_editor rcore_prop_whiteboard_large',
    ['error_spawn_object'] = "Sorry, we cant spawn selected model",
    ['error_get_closer'] = "You need to get closer to the board to edit it.",
    ["editor_enter_key"] = "Whiteboard Editor enter",
    ["editor_set_position"] = "Set proper position with arrows",
    ["editor_set_rotation"] = "Set proper rotation with arrows",
    ["editor_help_rotation"] = "Change rotation with arrows~n~~INPUT_SPRINT~ Change rotation/position mode~n~~INPUT_CELLPHONE_DOWN~ Down~n~~INPUT_CELLPHONE_UP~ Up~n~~INPUT_CELLPHONE_LEFT~ Left~n~~INPUT_CELLPHONE_RIGHT~ Right~n~~INPUT_SCRIPTED_FLY_ZUP~ Forward~n~~INPUT_SCRIPTED_FLY_ZDOWN~ Backward",
    ["editor_help_scaleform_rotation"] = "Change rotation with arrows~n~~INPUT_CELLPHONE_DOWN~ Down~n~~INPUT_CELLPHONE_UP~ Up~n~~INPUT_CELLPHONE_LEFT~ Left~n~~INPUT_CELLPHONE_RIGHT~ Right~n~~INPUT_SCRIPTED_FLY_ZUP~ Forward~n~~INPUT_SCRIPTED_FLY_ZDOWN~ Backward",
    ["editor_help_position"] = "Change position with arrows~n~~INPUT_SPRINT~ Change rotation/position mode~n~~INPUT_CELLPHONE_DOWN~ Down~n~~INPUT_CELLPHONE_UP~ Up~n~~INPUT_CELLPHONE_LEFT~ Left~n~~INPUT_CELLPHONE_RIGHT~ Right~n~~INPUT_SCRIPTED_FLY_ZUP~ Forward~n~~INPUT_SCRIPTED_FLY_ZDOWN~ Backward",
    ["editor_copy"] = "Your whiteboard data is printed into console",
}
