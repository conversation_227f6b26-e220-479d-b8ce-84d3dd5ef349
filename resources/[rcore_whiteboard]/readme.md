# RCore Whiteboard
## Introduction

Thanks for buying our rcore_whiteboard product. Resource folder contains
two folders rcore_whiteboard and rcore_whiteboard_scaleform. rcore_whiteboard_scaleform
must be started before rcore_whiteboard or it will be not working.

## Installation

1) Download the resource from the keymaster page.
2) Copy [rcore_whiteboard] folder and put it in resources folder of your server
3) Go to your server.cfg and add `ensure [rcore_whiteboard]`
4) Add to bottom of server.cfg permission part (script need this permission to use properly ACE permission)

```lua
add_ace resource.rcore_whiteboard command.add_ace allow
add_ace resource.rcore_whiteboard command.add_principal allow
add_ace resource.rcore_whiteboard command.remove_ace allow
add_ace resource.rcore_whiteboard command.remove_principal allow
```

5) Go to rcore_whiteboard/config.lua find Config.TargetSystem and set it to fit system that fits your server

````lua
--Targets.QB_TARGET
--Targets.OX_TARGET
--Targets.STANDALONE
Config.TargetSystem = Targets.STANDALONE
````

6) Go to rcore_whiteboard/config.lua and find Config.Framework and set it to fit framework that fits your server

- Standalone version does not have job and grade restriction available (you can edit it for your use in rcore_whiteboard/server/lib/permission.lua)
````lua
--Frameworks.QBCORE
--Frameworks.ESX
--Frameworks.STANDALONE
Config.Framework = Frameworks.STANDALONE
````

## Usage & In-game editor

File: rcore_whiteboard/config.lua

### Predefined models

These models are predefined in config so you can use them without any more configuration

- ch_prop_whiteboard_03
- rcore_prop_whiteboard_small
- rcore_prop_whiteboard_medium
- rcore_prop_whiteboard_large

If you want to add new model please contact us in ticket we will help you with it or use 
rcore_television editor and add new model into Config.ModelSettings

### How to add new board?

#### Position editor

In rcore_whiteboard/config.lua you can find Config.OpenEditorCommand = 'rw_editor' thats
the command that will open editor. You can change it to whatever you want if you need.
This command needs ACE permission to be open in default its set to group.admin you can check
rcore_whiteboard/permissions.lua to change it

**Usage: /rw_editor [model]**

You must use this command with model that is configured in Config.ModelSettings so for example to 
find proper place for large board you can use /rw_editor rcore_prop_whiteboard_large

It will create object in front of your character and you can go where you want to place it and press ENTER

![Find proper place for object](https://media.discordapp.net/attachments/1102957147878326362/1135158497231769610/image.png?width=693&height=466)

After you press ENTER it will freeze board to it place and you can manipulate position and rotation
with arrows and page up and down

![Manipulation](https://media.discordapp.net/attachments/1102957147878326362/1135159085222854767/image.png?width=910&height=466)

You can change position & rotation mode with LEFT SHIFT after you find proper place press ENTER and then open console with F8
you can find there position and rotation data that you can paste to your config to new board.

![Copy](https://media.discordapp.net/attachments/1102957147878326362/1135159747797069915/image.png?width=973&height=428)

#### Static URL (load content from URL)

If you will add url parameter to board data it will load content from url and it will be static.
This board will not have editor functions.

````lua
{
    id = 'RCORE_STATIC',
    pos = vector3(448.6489, -982.556, 25.69997),
    rotation = vector3(0.0, 0.0, 0.0),
    model = 'rcore_prop_whiteboard_small',
    url = 'https://rcore.cz/', --Use static url this will turn off editor functions
}
````

#### Job & grade restriction

You can add as many jobs as you can also you can remove or leave empty grades parameter
if you want to allow all grades to edit this whiteboard.

````lua
{
    id = 'POLICE_MAIN',
    pos = vector3(450.804382, -984.659363, 25.690796),
    rotation = vector3(0.0, 0.0, 0.0),
    model = 'ch_prop_whiteboard_03',
    jobs = { --you can specify jobs that can edit this whiteboard
        'police',
    },
    grades = { --you can specify grades that can edit this whiteboard
        'boss',
    }
}
````

#### Multiple boards with same content

If you want to use multiple boards with same content you can use same id for them.
This will share content and editor functions between them. If you edit one of them
content will be updated on all of them.

````lua
{
    id = 'POLICE_LARGE',
    pos = vector3(425.88, -1027.67, 29.06),
    rotation = vector3(0.0, 0.0, 0.0),
    model = 'rcore_prop_whiteboard_large',
},
{
    id = 'POLICE_LARGE',
    pos = vec3(442.829102, -988.352356, 31.041954),
    rotation = vec3(0.000000, 0.000000, -89.998894),
    model = 'rcore_prop_whiteboard_large',
},
````

#### Read only boards

If you want to use multiple boards with same content and you only want to allow edit one of them
you can use readOnly option. This option will turn off editor functions.

````
{
    id = 'POLICE_SMALL',
    pos = vector3(430.24, -1027.08, 28.94),
    rotation = vector3(0.0, 0.0, 0.0),
    model = 'rcore_prop_whiteboard_small',
    readOnly = true, --this whiteboard can't be edited
}
````


### API

Server side update event

````
AddEventHandler(triggerName('updated'), function(boardId, playerId)
    --boardId = board id
    --playerId = player that updated board
end)
````

## FAQ

#### I cant see board content when I save it

Check if you have proper rotation you can simply rotate it if you add - to rotation example
I have board with this rotation `vector3(0.0, 0.0, 50.0)` so change it to `vector3(0.0, 0.0, -50.0)` and restart script