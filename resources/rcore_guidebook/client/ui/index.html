<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <title>rcore_guidebook UI</title>
    <link href="./output.css" rel="stylesheet">
    <script type="module" crossorigin src="./assets/index.baa68e99.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vendor.b9589c43.js">
    <link rel="modulepreload" crossorigin href="./assets/@react-dom.5e90ab5f.js">
    <link rel="modulepreload" crossorigin href="./assets/@radix-ui.6164df64.js">
    <link rel="stylesheet" href="./assets/index.1f40ad7e.css">
  </head>
  <body>
    <div id="root"></div><script type="module" src="./config.js"></script>
  </body>
</html>
