import{b as es,d as ts}from"./@react-dom.5e90ab5f.js";import{r as b,k as e,t as ns,m as ss,L as Ot,e as F,n as i,M as rs,p as os,Z as as,q as is,X as Me,u as ls,v as cs,S as ds,C as us,w as $t,B as ms,x as zt,T as ps,y as ae,F as Dt,z as Y,A as Ge,K as Ke,P as pe,D as hs,E as We,G as fs,H as gs,I as ot,J as bs,N as at,O as it,Q as Lt,R as lt,U as ct,V as ys,W as xs,Y as vs,a0 as _s,a1 as ks,a2 as he,a3 as Cs,a4 as _,a5 as ws,a6 as fe,a7 as ge,a8 as H,a9 as Ns,aa as Es,ab as Se,ac as yt,ad as Ss,ae as Ts}from"./vendor.b9589c43.js";import{$ as Ft,a as As,b as Is,c as Rt,d as Ps,e as jt,f as Os,g as $s,h as zs,i as Vt,j as Mt,k as Gt,l as Ds,m as Kt,n as Bt,o as Ht,p as Ut,q as qt,r as Jt,s as Ls,t as Fs,u as Wt,v as Rs,w as js,x as Yt,y as Vs,z as Qt,A as Zt,B as Ms,C as Gs,D as Xt,E as Ks,F as Bs,G as en,H as Hs,I as Us,J as qs,K as tn,L as nn,M as sn,N as rn,O as on,P as an,Q as Js}from"./@radix-ui.6164df64.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const a of o)if(a.type==="childList")for(const c of a.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&r(c)}).observe(document,{childList:!0,subtree:!0});function n(o){const a={};return o.integrity&&(a.integrity=o.integrity),o.referrerpolicy&&(a.referrerPolicy=o.referrerpolicy),o.crossorigin==="use-credentials"?a.credentials="include":o.crossorigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(o){if(o.ep)return;o.ep=!0;const a=n(o);fetch(o.href,a)}})();const dt=()=>!window.invokeNative,Ws=()=>{},oe=(t,s)=>{const n=b.exports.useRef(Ws);b.exports.useEffect(()=>{n.current=s},[s]),b.exports.useEffect(()=>{const r=o=>{const{action:a,data:c}=o.data;n.current&&a===t&&n.current(c)};return window.addEventListener("message",r),()=>window.removeEventListener("message",r)},[t])};async function L(t,s,n){const r={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(s)};if(dt()&&n)return n;const o=window.GetParentResourceName?window.GetParentResourceName():"rcore_guidebook";return await(await fetch(`https://${o}/${t}`,r)).json()}var q=(t=>(t.ADMIN="admin",t.GUIDEBOOK="guidebook",t.CUSTOM_CONTENT="custom_content",t))(q||{}),N=(t=>(t.ADMIN_HOMEPAGE="admin_homepage",t.NOT_FOUND="not_found",t.PAGE_CONTENT="page_content",t.CATEGORY_LIST="category_list",t.PAGE_LIST="page_list",t.PAGE_EDIT="page_edit",t.PAGE_ADD="page_add",t.CATEGORY_EDIT="category_edit",t.CATEGORY_ADD="category_add",t.POINT_LIST="point_list",t.POINT_EDIT="point_edit",t.POINT_ADD="point_add",t.SEARCH_RESULTS="search_results",t))(N||{}),re=(t=>(t.PAGE="PAGE",t.CATEGORY="CATEGORY",t.TELEPORT="TELEPORT",t.POINT="POINT",t))(re||{}),de=(t=>(t.OPEN="OPEN",t.OPEN_ADMIN="OPEN_ADMIN",t.OPEN_SPECIFIC_PAGE="OPEN_SPECIFIC_PAGE",t.CLOSE="CLOSE",t.INIT_DATA="INIT_DATA",t.REFRESH_PAGE="REFRESH_PAGE",t.UPDATED_PAGE="UPDATED_PAGE",t.UPDATED_CATEGORY="UPDATED_CATEGORY",t.POINTS="POINTS",t))(de||{});const ln=b.exports.createContext({}),Ys=({children:t})=>{const[s,n]=b.exports.useState(!0),[r,o]=b.exports.useState(!0),[a,c]=b.exports.useState(q.GUIDEBOOK),[m,d]=b.exports.useState(N.NOT_FOUND),[h,f]=b.exports.useState({}),[g,x]=b.exports.useState([]),[p,S]=b.exports.useState({}),[y,A]=b.exports.useState(),[k,z]=b.exports.useState(),[v,j]=b.exports.useState(),[T,K]=b.exports.useState({}),[E,u]=b.exports.useState(),[l,C]=b.exports.useState(),[M,G]=b.exports.useState(),[J,Q]=b.exports.useState(),[X,Ne]=b.exports.useState(),[Ee,Le]=b.exports.useState(),[Fe,An]=b.exports.useState(),[In,Pn]=b.exports.useState(),[On,$n]=b.exports.useState(),[zn,Dn]=b.exports.useState(),[Ln,Fn]=b.exports.useState(1),[Rn,jn]=b.exports.useState(!1),[Vn,Mn]=b.exports.useState(!1),[Gn,Kn]=b.exports.useState(void 0),[Bn,Hn]=b.exports.useState({}),[Un,qn]=b.exports.useState(!1),[Jn,Wn]=b.exports.useState(!1),[Yn,Qn]=b.exports.useState(!1),[Zn,Xn]=b.exports.useState(0);return e(ln.Provider,{value:{isLoading:s,isAdmin:r,categories:h,points:g,deletedItem:y,currentContentPage:m,currentScreen:a,translations:p,canInsertIFrame:Vn,setCanInsertIFrame:Mn,editedPageKey:v,isDataFinishedLoading:Jn,setIsDataFinishedLoading:Wn,editedCategoryKey:k,pageCache:T,currentPage:E,quill:l,playerJob:Gn,serverJobs:Bn,teleportToPoint:In,setTeleportToPoint:Pn,userConfig:M,pageScale:Ln,setPageScale:Fn,isCopyDisabled:Rn,setIsCopyDisabled:jn,latestUpdatedPageKey:On,setLatestUpdatedPageKey:$n,firstPageKey:X,customContent:Fe,searchResults:zn,setSearchResults:Dn,setCustomContent:An,setFirstPageKey:Ne,clickedEmptyCategoryKey:J,editedPointKey:Ee,setEditedPointKey:Le,setClickedEmptyCategoryKey:Q,setUserConfig:G,setQuill:C,setCurrentPage:u,setPageCache:K,setEditedPageKey:j,setEditedCategoryKey:z,setTranslations:S,setCurrentContentPage:d,setCategories:f,setIsLoading:n,setPoints:x,setIsAdmin:o,setScreen:c,setDeletedItem:A,setServerJobs:Hn,setPlayerJob:Kn,setIsJobPermissionEnabled:qn,isJobPermissionEnabled:Un,setIsJobPermissionDisabledForAdmin:Qn,isJobPermissionDisabledForAdmin:Yn,searchCooldown:Zn,setSearchCooldown:Xn},children:t})},R=()=>{const t=b.exports.useContext(ln);if(!t)throw new Error("useAppContext must be used within a ContextProvider");return t},Qs=b.exports.createContext(null),Zs=({children:t})=>{const[s,n]=b.exports.useState(!1),{pageScale:r}=R(),o=b.exports.useRef(r);return oe("setVisible",n),b.exports.useEffect(()=>{o.current=r},[r]),b.exports.useEffect(()=>{if(!s)return;const a=c=>{["Escape"].includes(c.code)&&(dt()?n(!s):L("close",{pageScale:o.current}))};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[s]),e(Qs.Provider,{value:{visible:s,setVisible:n},children:e("div",{style:{visibility:s?"visible":"hidden",height:"100%"},children:t})})};async function Xs(){const s=await(await fetch("config.js")).text(),n=new Blob([s],{type:"text/javascript"}),r=URL.createObjectURL(n),o=document.createElement("script");return o.src=r,new Promise(a=>{o.onload=()=>{URL.revokeObjectURL(r),a(window.appConfig)},document.head.appendChild(o)})}function w(...t){return ns(ss(t))}const B=({isLoading:t,className:s,icon:n,onClick:r,size:o=10,hoverColor:a="accent",disabled:c,type:m="button"})=>e("button",{type:m,onClick:r,className:w("flex aspect-square shrink-0 items-center justify-center gap-2 rounded-full bg-primary transition-[color,opacity] active:bg-primary-dark lg:text-left",`h-${o}`,`hover:text-${a}`,{"pointer-events-none opacity-60":t||c},s),disabled:c||t,children:t?e(Ot,{className:"animate-spin"}):n}),ut=F.forwardRef(({className:t,children:s,...n},r)=>i(Ft,{ref:r,className:w("relative overflow-hidden",t),...n,children:[e(As,{className:"ScrollAreaViewport h-full max-h-full w-full rounded-[inherit]",children:s}),e(cn,{}),e(Is,{})]}));ut.displayName=Ft.displayName;const cn=F.forwardRef(({className:t,orientation:s="vertical",...n},r)=>e(Rt,{ref:r,orientation:s,className:w("flex touch-none select-none transition-colors",s==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",s==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...n,children:e(Ps,{className:w("relative flex-1 rounded-full bg-accent-dark")})}));cn.displayName=Rt.displayName;const dn=1.5,un=.8,xt=.1,er=()=>{const{pageScale:t,setPageScale:s}=R();return{increasePageScale:()=>{t<dn&&s(t+xt)},decreasePageScale:()=>{t>un&&s(t-xt)}}},tr=({menu:t,contentElement:s,onCloseButtonClick:n})=>{const{currentScreen:r,customContent:o,currentContentPage:a,pageScale:c}=R(),[m,d]=b.exports.useState(!1),h=b.exports.useRef(null),{increasePageScale:f,decreasePageScale:g}=er(),x=r===q.ADMIN;return b.exports.useEffect(()=>{h.current&&(h.current.scrollTop=0)},[s]),b.exports.useEffect(()=>{o!==void 0&&d(!0)},[o]),b.exports.useEffect(()=>{var y;const p=((y=h.current)==null?void 0:y.clientHeight)||0,S=document.createElement("style");return S.type="text/css",S.innerHTML=`.iframe-fullsize { height: calc(${p}px - 4.6rem) !important; width: 100% !important; }`,document.getElementsByTagName("head")[0].appendChild(S),()=>{document.getElementsByTagName("head")[0].removeChild(S)}},[]),i("div",{className:w("relative mx-auto my-auto flex h-[85%] w-screen min-w-0 max-w-[1340px] select-none rounded-3xl bg-secondary p-6 text-left text-text 3xl:max-w-[1920px]",{"gap-8":!m}),children:[e("style",{children:`
          .ContentScrollArea .ScrollAreaViewport > div {
            display: block !important;
          }
        `}),e("div",{className:w("max-w-[18rem] flex-shrink-0 flex-grow-0 overflow-hidden p-0.5",{"transition-all":o===void 0},{"pointer-events-none basis-0 origin-left scale-0 opacity-0":m},{[x?"basis-[25%]":"basis-[30%]"]:!m}),children:t}),i("div",{className:w("relative min-w-0 flex-grow rounded-2xl bg-primary transition-all",{"rounded-bl-none":o===void 0}),children:[o===void 0&&e("div",{className:"absolute -left-5 bottom-0 z-50",children:e(B,{icon:m?e(rs,{size:"1.1rem",className:"rotate-45"}):e(os,{size:"1.1rem",className:"rotate-45"}),onClick:()=>d(!m)})}),e(ut,{ref:h,className:"ContentScrollArea h-full overflow-visible px-7 py-9",children:e("div",{className:"h-full p-0.5",children:s})})]}),i("div",{className:"absolute right-1 top-1 flex flex-shrink-0 items-center rounded-full",children:[a===N.PAGE_CONTENT&&i("div",{className:"-mr-8 flex items-center gap-2 rounded-full bg-secondary p-2 pr-8",children:[e(B,{disabled:c>=dn,size:8,icon:e(as,{size:"1.1rem"}),onClick:f}),e(B,{disabled:c<=un,size:8,icon:e(is,{size:"1.1rem"}),onClick:g})]}),e("div",{className:"rounded-full bg-secondary p-2",children:e(B,{icon:e(Me,{size:"1.2rem"}),onClick:n})})]})]})},nr=8,Ae=300,vt=Ae/2,sr=(t=1)=>`${t*nr}px`,rr=ls`
  * {
    box-sizing: border-box;
  }

  body {
    padding: 0;
    margin: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }

  html, body, #root {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  /* keyframes */
  .fade-enter {
    opacity: 0;
  }

  .fade-enter-active {
    opacity: 1;
    transition: opacity ${Ae}ms;
  }

  .fade-exit {
    opacity: 1;
  }

  .fade-exit-active {
    opacity: 0;
    transition: opacity ${Ae}ms;
  }

  .pop-in-enter {
    transform: scale(0);
    opacity: 0;
  }

  .pop-in-enter-active {
    transform: scale(1);
    opacity: 1;
    transition-duration: ${vt}ms;
    transition-property: transform, opacity;
  }

  .pop-in-exit {
    transform: scale(1);
    opacity: 1;
  }

  .pop-in-exit-active {
    transform: scale(0);
    opacity: 0;
    transition-duration: ${vt}ms;
    transition-property: transform, opacity;
  }

  .scale-down-enter, .scale-down-exit {
    transform: scale(1) skew(0);
    opacity: 1;
  }

  .scale-down-enter-active, .scale-down-exit-active {
    transform: scale(0) skew(-59deg, -28deg);
    opacity: 0;
    transform-origin: top right;
    transition-duration: ${Ae}ms;
    transition-timing-function: cubic-bezier(.57,.21,.69,3.25);
    transition-property: transform, opacity;
  }
  
  .slide-enter, .slide-exit-active {
    transform: scaleY(0);
    visibility: hidden;
    opacity: 0;
  }

  .slide-exit, .slide-enter-active {
    transform: scaleY(1);
    visibility: visible;
    opacity: 1;
  }

  .slide-enter-active,
  .slide-exit-active {
    transition-duration: ${Ae}ms;
    transition-property: transform, visibility, opacity;
    transform-origin: center top;
  }
`,or=cs.div`
  text-align: center;
  height: 100%;
  width: 100%;
  display: flex;
  padding: ${sr(2)};
`,ar=(t="dark")=>{const s=window.appConfig.themes[t];if(s){const n=document.documentElement.style;for(const[r,o]of Object.entries(s))n.setProperty(`--color-${r}`,o)}},V=()=>{const{translations:t}=R(),s=(r,o)=>{if(!r.length||!o)return o||null;const[a,...c]=r;return s(c,o[a])};return(r,o)=>{const a=r.split(".");let c=s(a,t);if(c&&o)for(const[m,d]of Object.entries(o)){const h=new RegExp(`{${m}}`,"g");c=c.replace(h,d.toString())}return c||r}},W=F.forwardRef(({className:t,type:s,icon:n,...r},o)=>i("div",{className:"relative",children:[e("input",{type:s,className:w("placeholder:text-inactive flex h-10 w-full rounded-md border border-input-border bg-input-bg px-3 py-2 text-sm text-input-text ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300",{"pr-10":n},t),ref:o,...r}),n&&e("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:n})]}));W.displayName="Input";const ir=3,lr=()=>{const t=V(),{setCurrentContentPage:s,setSearchResults:n,setIsLoading:r,searchCooldown:o}=R(),[a,c]=b.exports.useState(""),[m,d]=b.exports.useState(0),h=f=>{if(f.preventDefault(),a.length<ir){L("notify",{title:t("exceptions.error"),message:t("notification.search_too_short"),isError:!0});return}const g=new Date().getTime();if(g-m<o*1e3){L("notify",{title:t("exceptions.error"),message:t("notification.search_too_fast"),isError:!0});return}d(g),r(!0),L("search",{query:a}).then(x=>{n({query:a,results:x}),s(N.SEARCH_RESULTS),c(""),r(!1)})};return e("div",{className:"relative",children:e("form",{onSubmit:f=>h(f),children:e(W,{className:"bg-primary",type:"text",placeholder:t("inputs.search"),value:a,onChange:f=>c(f.target.value),icon:e("button",{className:"-m-2 p-2",type:"submit",children:e(ds,{size:"1.2rem"})})})})})},cr=()=>{const{currentScreen:t,setScreen:s,setCurrentContentPage:n,isAdmin:r}=R(),o=t===q.ADMIN;return i("div",{className:"flex gap-2 pr-4",children:[r&&e(B,{icon:o?e(us,{size:"1.2rem"}):e($t,{size:"1.2rem"}),onClick:()=>{!r||(t===q.ADMIN?(s(q.GUIDEBOOK),n(N.PAGE_CONTENT)):(s(q.ADMIN),n(N.ADMIN_HOMEPAGE)))}}),!o&&e(lr,{})]})},mn=({title:t,titleIcon:s,children:n,disableScroll:r})=>i("div",{className:"flex h-[100%] flex-col",children:[i("div",{className:"flex items-center gap-2",children:[s!=null?s:e(ms,{size:"1.75rem"}),e("h1",{className:"py-2 text-xl font-bold",children:t!=null?t:"Guidebook"})]}),e(ut,{className:"mr-1 mt-3 rounded-md pr-2",children:n}),e("div",{className:"mt-auto pt-4",children:e(cr,{})})]}),dr=({children:t,title:s,collapsed:n,isRestricted:r})=>{var h;const[o,a]=b.exports.useState(!!n),c=b.exports.useRef(null),[m,d]=b.exports.useState(o?0:(h=c.current)==null?void 0:h.scrollHeight);return b.exports.useLayoutEffect(()=>{var f;d(((f=c.current)==null?void 0:f.scrollHeight)||0)},[t]),i("div",{className:w("overflow-hidden rounded-lg bg-primary px-4 pt-3 transition-[padding] [&:not(:first-child)]:mt-3",o?"pb-1":"pb-4"),children:[i("div",{className:"-m-3 flex cursor-pointer items-center justify-between gap-3 bg-primary p-3",onClick:()=>a(!o),children:[e("h2",{className:w("relative flex-grow overflow-x-hidden break-words font-medium",{"italic opacity-90":r}),children:s}),e("div",{className:w("transition-transform",{"scale-y-[-1]":o}),children:e(zt,{size:"1.4rem"})})]}),e(ps,{in:!o,timeout:300,children:f=>e("div",{ref:c,className:"mt-2 space-y-1.5",style:{overflow:"hidden",height:f==="entering"||f==="entered"?`${m}px`:"0px",transition:"height 0.3s ease-in-out"},children:t})})]})},pn=t=>{const s=document.createElement("textarea"),n=document.getSelection();if(!!n)return s.textContent=t,document.body.appendChild(s),n.removeAllRanges(),s.select(),document.execCommand("copy"),n.removeAllRanges(),document.body.removeChild(s),!0},_t=({categoryKey:t,pageKey:s,isActive:n,onClick:r,title:o,isPlaceholder:a,isRestricted:c})=>{const m=V(),[d,h]=b.exports.useState(!1),{isAdmin:f,setEditedCategoryKey:g,setScreen:x,setEditedPageKey:p,setCurrentContentPage:S,setClickedEmptyCategoryKey:y}=R(),A=a?"cursor-default":"cursor-pointer",k=(v,j)=>{v.stopPropagation(),S(j)},z=()=>f?a?e(B,{className:"bg-transparent",size:8,icon:e(Dt,{size:"1.2rem"}),onClick:v=>{v.stopPropagation(),g(void 0),p(void 0),y(t),x(q.ADMIN),k(v,N.PAGE_ADD)}}):i(Y,{children:[e(B,{className:"bg-transparent",size:8,icon:e(Ge,{size:"1.2rem"}),onClick:v=>{v.stopPropagation(),g(t),p(s),x(q.ADMIN),k(v,N.PAGE_EDIT)}}),s&&e(B,{className:"bg-transparent",size:8,icon:e(Ke,{size:"1.2rem"}),onClick:v=>{v.stopPropagation(),pn(s)?L("notify",{title:m("exceptions.success"),message:m("notification.key_copied_success")}):L("notify",{title:m("exceptions.error"),message:m("notification.key_copied_error"),isError:!0})}})]}):null;return i("div",{onClick:r,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),className:w("relative flex min-h-[32px] items-center overflow-hidden text-ellipsis rounded-md bg-primary-dark px-2 py-1 transition-colors hover:bg-secondary 3xl:min-h-[40px]",A,{"rounded-l-none border-l-[3px] border-l-accent font-medium":n}),children:[a?e("i",{className:"font-thin text-text-inactive",children:m("category.category_empty")}):e("span",{className:w({"italic opacity-90":c}),children:o}),f&&e(ae,{in:d,timeout:100,classNames:"fade",unmountOnExit:!0,children:i("div",{className:"absolute left-0 top-0 h-full w-full",children:[e("div",{className:"pointer-events-none absolute right-0 z-10 h-full w-full bg-gradient-to-l from-controls-shadow to-transparent"}),e("div",{className:"absolute right-0 top-1/2 z-20 flex -translate-y-1/2 items-center space-x-1 rounded-full pr-2",children:z()})]})})]})},Be=(t,s,n,r)=>{const o=s[t];if(o!==void 0){n(o);return}L("requestPage",t).then(a=>{if(a==null){r==null||r();return}n(a)})},hn=()=>{const{isJobPermissionEnabled:t,isAdmin:s,isJobPermissionDisabledForAdmin:n}=R();return{hasPermission:(o,a)=>{var d;if(!t||s&&n||!o)return!0;const{permissions:c}=o;if(!c||!c.enabled||!c.jobs||!((d=c.jobs)!=null&&d.length)||!a&&!o)return!0;if(!a)return!1;const m=c.jobs.find(h=>h.jobName===a.name);return m?m.grade<=a.grade:!1}}},Ye=({})=>e("div",{className:"pointer-events-none relative flex min-h-[32px] animate-pulse items-center overflow-hidden text-ellipsis rounded-md bg-primary-dark px-2 py-1 transition-colors hover:bg-secondary 3xl:min-h-[40px]",children:e("div",{className:"absolute left-0 top-0 h-full w-full",children:e("div",{className:"pointer-events-none absolute right-0 z-10 h-full w-full bg-gradient-to-l from-controls-shadow to-transparent"})})}),Qe=()=>i("div",{className:"pointer-events-none overflow-hidden rounded-lg bg-primary px-4 pb-4 pt-3 transition-[padding] [&:not(:first-child)]:mt-3",children:[e("div",{className:"flex h-6 w-1/2 animate-pulse cursor-pointer items-center justify-between gap-3 rounded-md bg-primary-dark p-3"}),i("div",{className:"mt-2 space-y-1.5",children:[e(Ye,{}),e(Ye,{}),e(Ye,{})]})]}),kt=()=>{const{isAdmin:t,setScreen:s,setCurrentContentPage:n}=R(),r=V();return i("div",{className:w("flex w-full items-center justify-between gap-4 break-all rounded-lg bg-primary px-4 py-1 transition-[padding] [&:not(:first-child)]:mt-3",{"py-3":!t}),children:[t?e("span",{className:"flex-shrink",children:r("category.new_category_cta")}):e("span",{className:"flex-shrink",children:r("general.guidebook_empty")}),e("div",{className:"flex-shrink-0",children:t&&e(B,{size:8,icon:e(pe,{size:"1.4rem"}),onClick:()=>{s(q.ADMIN),n(N.CATEGORY_ADD)}})})]})},ur=()=>{const t=V(),{setIsLoading:s,pageCache:n,setPageCache:r,categories:o,setCurrentPage:a,currentPage:c,setCurrentContentPage:m,isDataFinishedLoading:d,playerJob:h}=R(),{hasPermission:f}=hn(),g=(y,A)=>{r(k=>({...k,[y]:A}))},x=y=>{s(!0),Be(y,n,function(A){a(A),g(y,A),s(!1),m(N.PAGE_CONTENT)},void 0)},p=y=>{const{is_enabled:A,attributes:k}=y;return!(!A||!f(k,h))},S=()=>{var A,k,z;if(!d&&!((A=Object.entries(o))!=null&&A.length))return i(Y,{children:[e(Qe,{}),e(Qe,{}),e(Qe,{})]});if(!((k=Object.entries(o))!=null&&k.length))return e(kt,{});const y=Object.entries(o).map(([v,j])=>{var J;const{pages:T,default_expand:K,is_enabled:E,attributes:u,label:l}=j;if(!E)return null;const C=T==null?void 0:T.some(Q=>(c==null?void 0:c.key)===Q.key);if(!f(u,h))return null;const G=T==null?void 0:T.filter(p);return e(dr,{title:l,collapsed:!C&&!K,isRestricted:!!((J=u==null?void 0:u.permissions)!=null&&J.enabled),children:G!=null&&G.length?G.map(({id:Q,key:X,label:Ne,attributes:Ee})=>{var Le,Fe;return e(_t,{pageKey:X,categoryKey:v,title:Ne,onClick:()=>x(X),isActive:(c==null?void 0:c.key)===X,isRestricted:!!((Le=u==null?void 0:u.permissions)!=null&&Le.enabled)||!!((Fe=Ee==null?void 0:Ee.permissions)!=null&&Fe.enabled)},Q)}):e(_t,{isPlaceholder:!0,categoryKey:v})},v)});return(z=y==null?void 0:y.filter(v=>v!==null))!=null&&z.length?y:e(kt,{})};return e(mn,{title:t("general.guide_title"),children:S()})},Ze=({title:t,icon:s,controls:n,onClick:r})=>{const[o,a]=b.exports.useState(!1);return i("div",{className:"relative rounded-full transition-colors hover:bg-secondary-light [&:not(:first-child)]:mt-3 ",onMouseEnter:()=>a(!0),onMouseLeave:()=>a(!1),onClick:r,children:[e("button",{className:"relative flex w-full items-center justify-between overflow-visible p-2",children:i("div",{className:"flex w-full items-center space-x-2",children:[e("span",{className:"flex-shrink-0",children:s}),e("h3",{className:"text-md overflow-hidden overflow-ellipsis whitespace-nowrap",children:t})]})}),n&&e(ae,{in:o,timeout:200,classNames:"fade",unmountOnExit:!0,onExited:()=>a(!1),children:e("div",{className:"absolute right-0 top-1/2 flex -translate-y-1/2 items-center space-x-1 rounded-full bg-primary p-1",children:n})})]})},He=()=>{const{setCurrentContentPage:t,setIsLoading:s,setEditedCategoryKey:n,setEditedPageKey:r}=R(),o=(d,h,f)=>{d.stopPropagation(),f&&s(!0),setTimeout(()=>{f&&s(!1),t(h)},0)};return{changeContentPage:o,addPage:d=>{n(void 0),r(void 0),o(d,N.PAGE_ADD,!0)},addCategory:d=>{n(void 0),o(d,N.CATEGORY_ADD,!0)},addPoint:d=>{o(d,N.POINT_ADD,!0)}}},mr=({})=>{const t=V(),s=()=>i("span",{children:[t("general.guide_title"),e("sup",{className:"text-xs",children:t("general.admin_label")})]}),{addCategory:n,addPage:r,addPoint:o,changeContentPage:a}=He(),c=()=>i("div",{className:"h-full flex-grow",children:[e(Ze,{title:t("category.categories"),icon:e(hs,{size:"1.5rem"}),controls:i(Y,{children:[e(B,{size:8,icon:e(We,{size:"1.2rem"}),onClick:m=>a(m,N.CATEGORY_LIST)}),e(B,{size:8,icon:e(fs,{size:"1.2rem"}),onClick:n})]}),onClick:m=>a(m,N.CATEGORY_LIST)}),e(Ze,{title:t("page.pages"),icon:e(gs,{size:"1.5rem"}),controls:i(Y,{children:[e(B,{size:8,icon:e(We,{size:"1.2rem"}),onClick:m=>a(m,N.PAGE_LIST)}),e(B,{size:8,icon:e(Dt,{size:"1.2rem"}),onClick:r})]}),onClick:m=>a(m,N.PAGE_LIST)}),e(Ze,{title:t("point.points"),icon:e(ot,{size:"1.5rem"}),controls:i(Y,{children:[e(B,{size:8,icon:e(We,{size:"1.2rem"}),onClick:m=>a(m,N.POINT_LIST)}),e(B,{size:8,icon:e(pe,{size:"1.2rem"}),onClick:o})]}),onClick:m=>a(m,N.POINT_LIST)})]});return e(mn,{titleIcon:e($t,{size:"1.75rem"}),title:s(),children:c()})},pr=({content:t})=>{const{pageScale:s,isCopyDisabled:n}=R(),r=b.exports.useRef(null),o=b.exports.useRef(null),[a,c]=b.exports.useState(0),m=V(),d=x=>{const p=x.target,S=p==null?void 0:p.getAttribute("data-x"),y=p==null?void 0:p.getAttribute("data-y"),A=p==null?void 0:p.getAttribute("data-label");L("navigate",{x:parseFloat(S||"0"),y:parseFloat(y||"0")}).then(()=>{L("notify",{title:m("exceptions.success"),message:`${m("notification.gps_set")} - ${A}`})})},h=x=>{x.preventDefault();let p=x.target;if(p.tagName!=="A"&&(p=p.closest("a")),!p||p.tagName!=="A")return;const S=p.classList;!S.contains("ql-action")&&!S.contains("ql-preview")&&!S.contains("btn")&&!S.contains("a-btn")&&(pn(p.href),L("notify",{title:m("exceptions.success"),message:m("notification.link_copied")}))},f=x=>{x.target instanceof HTMLButtonElement&&d(x),x.target instanceof HTMLAnchorElement&&h(x)};b.exports.useEffect(()=>{var x;return(x=r.current)==null||x.addEventListener("click",f),()=>{var p;(p=r.current)==null||p.removeEventListener("click",f)}},[]),b.exports.useEffect(()=>{const x=o.current;x&&c(x.clientWidth)},[o.current]);const g=x=>(x.preventDefault(),!1);return b.exports.useEffect(()=>(n?document.addEventListener("copy",g):document.removeEventListener("copy",g),()=>{document.removeEventListener("copy",g)}),[n]),e("div",{ref:o,className:"ql-snow",children:e("div",{ref:r,className:w("page__content ql-editor origin-top-left",n?"!select-none":"!select-text"),style:{transform:`scale(${s})`,width:a&&s>1?`${a/s}px`:"auto"},dangerouslySetInnerHTML:{__html:t||""}})})},hr=({className:t,size:s})=>i("svg",{className:t,width:s,height:s,viewBox:"0 0 512 512",fill:"currentColor",children:[e("path",{d:"M469.922 270.678c-57.692-16.97-107.221-56.706-136.453-109.291 6.269-12.662 7.18-27.623 1.958-41.255l-11.67-30.465c-6.262-16.345-22.229-27.327-39.732-27.327h-6.469a40.913 40.913 0 0 0 5.875-21.169c0-22.701-18.469-41.17-41.17-41.17s-41.169 18.469-41.169 41.17a40.92 40.92 0 0 0 5.875 21.169h-6.469c-17.503 0-33.47 10.982-39.732 27.327l-11.671 30.465c-7.253 18.937-2.676 40.438 11.663 54.777l27.259 27.259-17.513 161.112a31.543 31.543 0 0 0 2.406 15.942h-15.232c-5.523 0-10 4.477-10 10v46.389H101.29c-5.523 0-10 4.477-10 10V492H44.9c-5.523 0-10 4.477-10 10s4.477 10 10 10h152.361c5.523 0 10-4.477 10-10s-4.477-10-10-10H111.29v-36.389h46.389c5.523 0 10-4.477 10-10v-46.389h149.167v46.389c0 5.523 4.477 10 10 10h46.389V492h-85.973c-5.523 0-10 4.477-10 10s4.477 10 10 10h152.361c5.523 0 10-4.477 10-10s-4.477-10-10-10h-46.388v-46.389c0-5.523-4.477-10-10-10h-46.389v-46.389c0-5.523-4.477-10-10-10h-15.233a31.543 31.543 0 0 0 2.406-15.942l-2.758-25.37a42.214 42.214 0 0 1 24.658 3.785 61.803 61.803 0 0 0 27.108 6.26c23.076-.002 45.289-12.887 56.041-34.996 8.358-17.187 27.309-26.598 46.092-22.878l.005-.023a9.991 9.991 0 0 0 1.941.216c4.322 0 8.308-2.824 9.589-7.181 1.557-5.298-1.474-10.857-6.773-12.415zM221.092 41.17c0-11.673 9.497-21.17 21.169-21.17 11.673 0 21.17 9.497 21.17 21.17s-9.497 21.169-21.17 21.169-21.169-9.496-21.169-21.169zm-53.32 86.116 11.671-30.465c3.318-8.662 11.779-14.481 21.055-14.481h83.527c9.276 0 17.737 5.82 21.055 14.481l11.67 30.465c4.434 11.574 1.636 24.716-7.128 33.48l-13.65 13.651V128c0-5.523-4.477-10-10-10s-10 4.477-10 10v59.8h-67.421V128c0-5.523-4.477-10-10-10s-10 4.477-10 10v46.417l-13.65-13.65c-8.765-8.765-11.563-21.907-7.129-33.481zm39.751 80.514H277l17.136 157.641c.668 6.144-3.577 11.797-9.663 12.87-6.346 1.125-12.462-3.128-13.627-9.469l-18.749-102.029a10 10 0 0 0-19.67 0l-18.749 102.029c-1.165 6.341-7.277 10.586-13.627 9.469-6.086-1.073-10.331-6.726-9.663-12.87zm23.781 171.422a31.62 31.62 0 0 0 2.044-6.766l8.913-48.505 8.913 48.505a31.62 31.62 0 0 0 2.044 6.766zm169.778-75.01c-10.179 20.929-35.486 29.674-56.417 19.497a62.21 62.21 0 0 0-35.567-5.691l-12.593-115.85 23.725-23.725c25.587 42.511 63.599 76.676 108.239 97.768-11.618 5.99-21.367 15.623-27.387 28.001zm-6.089-233.203c5.426 0 9.84 4.414 9.84 9.84 0 5.523 4.477 10 10 10s10-4.477 10-10c0-5.426 4.414-9.84 9.84-9.84 5.523 0 10-4.477 10-10s-4.477-10-10-10c-5.426 0-9.84-4.414-9.84-9.839 0-5.523-4.477-10-10-10s-10 4.477-10 10c0 5.425-4.414 9.839-9.84 9.839-5.523 0-10 4.477-10 10s4.477 10 10 10zM50.261 192.881c5.426 0 9.84 4.414 9.84 9.839 0 5.523 4.477 10 10 10s10-4.477 10-10c0-5.425 4.414-9.839 9.84-9.839 5.523 0 10-4.477 10-10s-4.477-10-10-10c-5.426 0-9.84-4.414-9.84-9.84 0-5.523-4.477-10-10-10s-10 4.477-10 10c0 5.426-4.414 9.84-9.84 9.84-5.523 0-10 4.477-10 10s4.477 10 10 10z"}),e("circle",{cx:"242.261",cy:"502.004",r:"10"})]}),fr=({})=>{const t=V();return i("div",{className:"flex h-full w-full flex-col items-center justify-center gap-20 break-words text-center",children:[e(hr,{size:"100%",className:"max-h-[25rem] text-text"}),i("div",{className:"w-96",children:[e("h2",{className:"text-3xl font-semibold",children:t("page_template.admin_welcome_title")}),e("p",{className:"mt-4",children:t("page_template.admin_welcome_text")})]})]})},gr=({className:t,size:s})=>e("svg",{className:t,width:s,height:s,viewBox:"0 0 256 256",fill:"currentColor",children:e("path",{d:"M128 24a104 104 0 1 0 104 104A104.11 104.11 0 0 0 128 24Zm0 192a88 88 0 1 1 88-88 88.1 88.1 0 0 1-88 88Zm61.66-93.66a8 8 0 0 1-11.32 11.32L168 123.31l-10.34 10.35a8 8 0 0 1-11.32-11.32L156.69 112l-10.35-10.34a8 8 0 0 1 11.32-11.32L168 100.69l10.34-10.35a8 8 0 0 1 11.32 11.32L179.31 112Zm-80-20.68L99.31 112l10.35 10.34a8 8 0 0 1-11.32 11.32L88 123.31l-10.34 10.35a8 8 0 0 1-11.32-11.32L76.69 112l-10.35-10.34a8 8 0 0 1 11.32-11.32L88 100.69l10.34-10.35a8 8 0 0 1 11.32 11.32ZM140 180a12 12 0 1 1-12-12 12 12 0 0 1 12 12Z"})}),Xe=({})=>{const t=V();return i("div",{className:"flex h-full w-full flex-col items-center justify-center gap-20 text-center",children:[e(gr,{size:"100%",className:"max-h-[25rem]"}),i("div",{className:"w-96",children:[e("h2",{className:"text-3xl font-semibold",children:t("page_template.not_found_page")}),i("p",{className:"mt-4",children:[t("page_template.not_exist_or_no_perms"),e("br",{}),e("br",{}),t("page_template.not_found_page_2")]})]})]})},br=({icon:t,value:s,bold:n})=>i("div",{className:"flex items-center gap-2 rounded-md bg-primary px-2 py-1",children:[e("span",{className:"text-data-row-icon",children:t}),e("span",{className:w("text-sm",{"font-semibold":n}),children:s})]}),yr=Os,fn=$s,gn=zs,mt=F.forwardRef(({className:t,sideOffset:s=4,align:n="center",...r},o)=>e(jt,{align:n,avoidCollisions:!0,hideWhenDetached:!0,collisionPadding:16,ref:o,sideOffset:s,className:w("z-50 max-w-full overflow-hidden rounded-md border border-tooltip-border bg-tooltip-bg px-3 py-1.5 text-sm text-tooltip-text shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-slate-800 dark:bg-slate-950 dark:text-slate-50",t,"max-w-[var(--radix-tooltip-content-available-width)]"),...r}));mt.displayName=jt.displayName;const pt=({className:t,data:s,actions:n,title:r})=>i("div",{className:w(t,"grid grid-cols-12 items-center justify-between gap-6 rounded-md bg-primary-light px-4 py-3 transition-[filter] hover:brightness-105"),children:[i("div",{className:w("relative flex flex-wrap items-center gap-2",{"col-span-9":n.length===3,"col-span-10":n.length===2}),children:[r&&i(fn,{children:[e(gn,{className:"inline cursor-help",asChild:!0,children:e("h2",{className:"max-w-[200px] truncate text-base font-semibold",children:r})}),e(mt,{align:"start",children:r})]}),s.map(o=>e(br,{icon:o.icon,value:o.value,bold:o.bold},o.key))]}),e("div",{className:w("flex justify-end gap-2",{"col-span-3":n.length===3,"col-span-2":n.length===2}),children:n.map(o=>e(B,{icon:o.icon,hoverColor:o.hoverColor,onClick:o.onClick},o.key))})]}),le=({onClick:t,title:s,action:n})=>i("div",{className:"sticky top-0 z-40 flex items-center justify-between gap-4 bg-primary pb-2",children:[i("div",{className:"items-center, flex gap-4",children:[t&&e("div",{className:"-m-2 cursor-pointer p-2",onClick:t,children:e(B,{size:6,icon:e(bs,{})})}),e("h2",{className:"text-xl font-semibold",children:s})]}),n]}),ze=at("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors transition-opacity focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",destructive:"bg-red-500/70 text-slate-50 hover:bg-red-500/60 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-button-secondary-bg text-button-secondary-text hover:bg-button-secondary-hover",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ce=F.forwardRef(({className:t,variant:s,size:n,asChild:r=!1,...o},a)=>e(r?Vt:"button",{className:w(ze({variant:s,size:n,className:t})),ref:a,...o}));ce.displayName="Button";const xr=({})=>{const t=V(),{categories:s,setDeletedItem:n,setCurrentContentPage:r,setEditedCategoryKey:o}=R(),{addCategory:a}=He(),c=d=>{var h,f;return[{icon:e(Ke,{size:"1.2rem"}),value:d.key,key:`${d.key}-key`},{icon:e(Lt,{size:"1.2rem"}),value:d.order_number,key:`${d.key}-sort`},{icon:e(lt,{size:"1.2rem"}),value:d.is_enabled?t("general.enabled_tag"):t("general.disabled_tag"),key:`${d.key}-enabled`},((f=(h=d.attributes)==null?void 0:h.permissions)==null?void 0:f.enabled)&&{icon:e(ct,{size:"1.2rem"}),value:t("general.restricted_tag"),key:`${d.key}-permissions`}].filter(Boolean)},m=d=>{o(d),r(N.CATEGORY_EDIT)};return i("div",{children:[e(le,{title:t("category.categories"),action:i(ce,{variant:"secondary",onClick:a,size:"sm",className:"gap-2",children:[e(pe,{size:"1rem"}),t("category.new_category_cta")]})}),e("div",{className:"mt-6 flex flex-col gap-3",children:Object.values(s).map(d=>e(pt,{title:d.label,actions:[{icon:e(Ge,{size:"1.2rem"}),onClick:()=>m(d.key),key:`${d.key}-edit`},{icon:e(it,{size:"1.2rem"}),onClick:()=>{n({key:d.key,label:d.label,type:re.CATEGORY})},hoverColor:"error",key:`${d.key}-delete`}],data:c(d)},d.id))})]})},vr=({})=>{const t=V(),{categories:s,setDeletedItem:n,setEditedPageKey:r,setCurrentContentPage:o,setEditedCategoryKey:a}=R(),{addPage:c}=He(),m=(g,x)=>{var p,S;return[{icon:e(Ke,{size:"1.2rem"}),value:g.key,key:`${g.key}-key`},{icon:e(ys,{size:"1.2rem"}),value:x,key:`${g.key}-category`},{icon:e(Lt,{size:"1.2rem"}),value:g.order_number,key:`${g.key}-sort`},{icon:e(lt,{size:"1.2rem"}),value:g.is_enabled?"enabled":"disabled",key:`${g.key}-visible`},((S=(p=g.attributes)==null?void 0:p.permissions)==null?void 0:S.enabled)&&{icon:e(ct,{size:"1.2rem"}),value:t("general.restricted_tag"),key:`${g.key}-permissions`}].filter(Boolean)},d=(g,x)=>{r(x),a(g),o(N.PAGE_EDIT)},h=()=>Object.values(s).map(x=>{const{pages:p,key:S}=x;return i("div",{className:"[&:not(:first-child)]:mt-4",children:[e("h3",{className:"text-lg font-semibold",children:x.label}),e("hr",{className:"mb-4 mt-2 h-px border-0 bg-secondary-light"}),e("div",{children:p!=null&&p.length?p.map(y=>e(pt,{title:y.label,className:"[&:not(:first-child)]:mt-2",actions:[{icon:e(Ge,{size:"1.2rem"}),onClick:()=>d(S,y.key),key:`${y.key}-edit`},{icon:e(it,{size:"1.2rem"}),onClick:()=>{n({key:y.key,label:y.label,type:re.PAGE})},hoverColor:"error",key:`${y.key}-delete`}],data:m(y,x.label)},y.id)):e("i",{className:"font-thin text-text-inactive",children:t("category.category_empty")})})]},x.key)}),f=()=>h().flat();return i("div",{children:[e(le,{title:t("page.pages"),action:i(ce,{variant:"secondary",onClick:c,size:"sm",className:"gap-2",children:[e(pe,{size:"1rem"}),t("page.new_page_cta")]})}),e("div",{className:"mt-6 flex flex-col gap-3",children:f()})]})},_r=({})=>{const t=V(),{points:s,setDeletedItem:n,setEditedPointKey:r,setCurrentContentPage:o,setTeleportToPoint:a}=R(),{addPoint:c}=He(),m=h=>{var f,g;return[{icon:e(Ke,{size:"1.2rem"}),value:h.key,key:`${h.key}-key`},{icon:e(xs,{size:"1.2rem"}),value:h.help_key==="custom_content"?"Custom":h.help_key,key:`${h.key}-content`},{icon:e(lt,{size:"1.2rem"}),value:h.is_enabled?"enabled":"disabled",key:`${h.key}-enabled`},((g=(f=h.attributes)==null?void 0:f.permissions)==null?void 0:g.enabled)&&{icon:e(ct,{size:"1.2rem"}),value:t("general.restricted_tag"),key:`${h.key}-permissions`}].filter(Boolean)},d=h=>{r(h),o(N.POINT_EDIT)};return i("div",{children:[e(le,{title:t("point.points"),action:i(ce,{variant:"secondary",onClick:c,size:"sm",className:"gap-2",children:[e(pe,{size:"1rem"}),t("point.new_point_cta")]})}),e("p",{className:"mt-2 text-sm",children:t("point.points_help")}),e("div",{className:"mt-6 flex flex-col gap-3",children:Object.values(s).map(h=>e(pt,{title:h.label,actions:[{icon:e(Ge,{size:"1.2rem"}),onClick:()=>{d(h.key)},key:`${h.key}-edit`},{icon:e(ot,{size:"1.2rem"}),onClick:()=>a(h),key:`${h.key}-teleport`},{icon:e(it,{size:"1.2rem"}),onClick:()=>{n({key:h.key,label:h.label,type:re.POINT})},hoverColor:"error",key:`${h.key}-delete`}],data:m(h)},h.id))})]})},kr=({})=>e("div",{className:"pointer-events-none flex h-full w-full flex-col items-center justify-center gap-20 overflow-hidden text-center",children:e(Ot,{size:"100%",className:"h-1/5 max-h-28 animate-spin",strokeWidth:1.5})}),Cr=at("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),bn=F.forwardRef(({className:t,...s},n)=>e(Mt,{ref:n,className:w(Cr(),t),...s}));bn.displayName=Mt.displayName;const be=vs,yn=F.createContext({}),P=({...t})=>e(yn.Provider,{value:{name:t.name},children:e(ks,{...t})}),Ue=()=>{const t=F.useContext(yn),s=F.useContext(xn),{getFieldState:n,formState:r}=_s(),o=n(t.name,r);if(!t)throw new Error("useFormField should be used within <FormField>");const{id:a}=s;return{id:a,name:t.name,formItemId:`${a}-form-item`,formDescriptionId:`${a}-form-item-description`,formMessageId:`${a}-form-item-message`,...o}},xn=F.createContext({}),O=F.forwardRef(({className:t,...s},n)=>{const r=F.useId();return e(xn.Provider,{value:{id:r},children:e("div",{ref:n,className:w("space-y-2",t),...s})})});O.displayName="FormItem";const I=F.forwardRef(({className:t,...s},n)=>{const{error:r,formItemId:o}=Ue();return e(bn,{ref:n,className:w(r&&"text-red-500 dark:text-red-900",t),htmlFor:o,...s})});I.displayName="FormLabel";const $=F.forwardRef(({...t},s)=>{const{error:n,formItemId:r,formDescriptionId:o,formMessageId:a}=Ue();return e(Vt,{ref:s,id:r,"aria-describedby":n?`${o} ${a}`:`${o}`,"aria-invalid":!!n,...t})});$.displayName="FormControl";const wr=F.forwardRef(({className:t,...s},n)=>{const{formDescriptionId:r}=Ue();return e("p",{ref:n,id:r,className:w("text-sm text-slate-500 dark:text-slate-400",t),...s})});wr.displayName="FormDescription";const D=F.forwardRef(({className:t,children:s,...n},r)=>{const{error:o,formMessageId:a}=Ue(),c=o?String(o==null?void 0:o.message):s;return c?e("p",{ref:r,id:a,className:w("text-sm font-medium text-red-500 dark:text-red-900",t),...n,children:c}):null});D.displayName="FormMessage";const U=({icon:t,children:s})=>e("div",{className:"contents max-w-full",children:i(fn,{children:[e(gn,{className:"inline cursor-help",asChild:!0,children:t}),e(mt,{children:s})]})}),Z=F.forwardRef(({className:t,...s},n)=>e(Gt,{ref:n,className:w("peer h-5 w-5 shrink-0 rounded-sm border border-tooltip-border bg-input-bg ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-input-text data-[state=checked]:text-input-bg",t),...s,children:e(Ds,{className:w("flex items-center justify-center text-current"),children:e(he,{className:"h-5 w-5"})})}));Z.displayName=Gt.displayName;const ee=t=>Cs(t).toLowerCase().replaceAll(/~[a-zA-Z]+~/gm,"").replace("-","").replace(/ /g,"_"),ht=_.object({is_enabled:_.boolean(),permissions_enabled:_.boolean(),permissions_jobs:_.array(_.object({jobName:_.string(),jobLabel:_.string(),grade:_.number(),gradeLabel:_.string()})).optional()}),Nr=({jobs:t,onAddClick:s,onRemoveClick:n})=>{const r=V();return i("div",{className:"flex justify-between gap-2 rounded-md bg-primary-light p-2",children:[e("div",{className:"flex flex-wrap gap-2",children:t==null?void 0:t.map(o=>i("div",{className:"flex items-center gap-1 rounded-md bg-primary px-2 py-0.5 text-sm",children:[e("span",{children:o.jobLabel}),o.grade!==0&&i("span",{className:"text-xs",children:["(",r("permissions.jobs.grade_and_higher",{grade:o.gradeLabel}),")"]}),e("button",{type:"button",onClick:()=>n(o.jobName),children:e(Me,{size:"1rem"})})]},o.jobName))}),e(B,{className:"self-center",icon:e(pe,{size:"1.2rem"}),onClick:s})]})},De=Fs,Er=Ls,vn=b.exports.forwardRef(({className:t,children:s,...n},r)=>e(Kt,{className:w("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...n,ref:r}));vn.displayName=Kt.displayName;const ye=b.exports.forwardRef(({className:t,...s},n)=>i(Er,{children:[e(vn,{}),e(Bt,{ref:n,className:w("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-primary-light bg-primary p-6 text-text shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full",t),...s})]}));ye.displayName=Bt.displayName;const xe=({className:t,...s})=>e("div",{className:w("flex flex-col space-y-2 text-center sm:text-left",t),...s});xe.displayName="AlertDialogHeader";const ve=({className:t,...s})=>e("div",{className:w("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s});ve.displayName="AlertDialogFooter";const _e=b.exports.forwardRef(({className:t,...s},n)=>e(Ht,{ref:n,className:w("text-lg font-semibold",t),...s}));_e.displayName=Ht.displayName;const ke=b.exports.forwardRef(({className:t,...s},n)=>e(Ut,{ref:n,className:w("text-sm text-text-inactive",t),...s}));ke.displayName=Ut.displayName;const Ce=b.exports.forwardRef(({className:t,...s},n)=>e(qt,{ref:n,className:w(ze({variant:"destructive"}),"bg-destructive-button-bg text-destructive-button-text hover:bg-destructive-button-hover hover:destructive-button-text font-semibold",t),...s}));Ce.displayName=qt.displayName;const we=b.exports.forwardRef(({className:t,...s},n)=>e(Jt,{ref:n,className:w(ze({variant:"outline"}),"mt-2 sm:mt-0",t),...s}));we.displayName=Jt.displayName;const Pe=Ks,Oe=Bs,ue=F.forwardRef(({className:t,children:s,...n},r)=>i(Wt,{ref:r,className:w("placeholder:text-inactive flex h-10 w-full items-center justify-between rounded-md border border-input-border bg-input-bg px-3 py-2 text-sm ring-offset-white focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus:ring-slate-300",t),...n,children:[s,e(Rs,{asChild:!0,children:e(ws,{className:"h-4 w-4 opacity-50"})})]}));ue.displayName=Wt.displayName;const me=F.forwardRef(({className:t,children:s,position:n="popper",...r},o)=>e(js,{children:e(Yt,{ref:o,className:w("relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-input-border bg-input-bg text-input-text shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-slate-800 dark:bg-slate-950 dark:text-slate-50",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...r,children:e(Vs,{className:w("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] max-h-[var(--radix-select-content-available-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s})})}));me.displayName=Yt.displayName;const Sr=F.forwardRef(({className:t,...s},n)=>e(Qt,{ref:n,className:w("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...s}));Sr.displayName=Qt.displayName;const ie=F.forwardRef(({className:t,children:s,...n},r)=>i(Zt,{ref:r,className:w("relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input-bg-focus focus:text-input-text data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-slate-800 dark:focus:text-slate-50",t),...n,children:[e("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e(Ms,{children:e(he,{className:"h-4 w-4"})})}),e(Gs,{children:s})]}));ie.displayName=Zt.displayName;const Tr=F.forwardRef(({className:t,...s},n)=>e(Xt,{ref:n,className:w("-mx-1 my-1 h-px bg-slate-100 dark:bg-slate-800",t),...s}));Tr.displayName=Xt.displayName;const et="all_grades",Ar=({onJobSelected:t,open:s,onOpenChange:n})=>{const{serverJobs:r}=R(),o=V(),a=_.object({name:_.string().min(1,o("exceptions.field_required")),grade:_.string().min(1,o("exceptions.field_required"))}),c=fe({resolver:ge(a),defaultValues:{name:"",grade:et},mode:"onChange"}),m=Object.entries(r||{}).map(([f,g])=>({key:f,...g})),d=f=>{var p;const g=m.find(S=>S.key===f.name),x=isNaN(parseInt(f.grade))?0:parseInt(f.grade);t({jobName:f.name,jobLabel:(g==null?void 0:g.name)||"",grade:x,gradeLabel:((p=g==null?void 0:g.grades[x])==null?void 0:p.name)||""})},h=f=>{const g=r[f].grades;return i(me,{avoidCollisions:!0,collisionPadding:16,children:[e(ie,{value:et,children:o("permissions.jobs.all_grades")},et),Object.values(g).map(x=>i(ie,{value:x.key,children:["(",x.key,") ",x.name]},x.key))]})};return b.exports.useEffect(()=>{s||c.reset()},[s]),e(De,{open:s,onOpenChange:n,children:i(ye,{children:[i(xe,{children:[e(_e,{children:o("permissions.jobs.dialog_title")}),e(ke,{children:o("permissions.jobs.dialog_description")})]}),e(be,{...c,children:e("form",{className:"space-y-4",onSubmit:c.handleSubmit(d),children:e(P,{control:c.control,name:"name",render:({field:f})=>i("div",{className:"flex gap-6",children:[i(O,{className:"w-1/2",children:[e(I,{children:o("permissions.jobs.select_job_title")}),i(Pe,{onValueChange:g=>{var x,p;f.onChange(g),c.setValue("grade",((p=Object.values(((x=m.find(S=>S.key===g))==null?void 0:x.grades)||{})[0])==null?void 0:p.key)||"")},value:f.value,children:[e($,{children:e(ue,{children:e(Oe,{})})}),e(me,{avoidCollisions:!0,collisionPadding:16,children:m.map(({key:g,name:x})=>e(ie,{value:g,children:x},g))})]}),e(D,{})]}),c.getValues("name")&&e(P,{control:c.control,name:"grade",render:({field:g})=>i(O,{className:"w-1/2",children:[e(I,{children:o("permissions.jobs.select_grade_title")}),i(Pe,{onValueChange:g.onChange,value:g.value,children:[e($,{children:e(ue,{children:e(Oe,{})})}),h(c.getValues("name"))]}),e(D,{})]})})]})})})}),i(ve,{className:"mt-4 flex gap-2",children:[e(we,{children:o("inputs.cancel")}),e(Ce,{onClick:c.handleSubmit(d),children:o("inputs.add")})]})]})})},ft=({form:t})=>{const{isJobPermissionEnabled:s}=R(),n=V(),r=b.exports.useRef(null),[o,a]=b.exports.useState(!1),[c,m]=b.exports.useState(!1);return!t||!s?null:i("div",{className:"relative",children:[i("div",{className:"space-y-4",children:[e("h3",{className:"text-lg",children:n("permissions.title")}),e(P,{control:t.control,name:"permissions_enabled",render:({field:d})=>i(Y,{children:[i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{name:"permissions_enabled",checked:d.value,onCheckedChange:h=>{d.onChange(h)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[n("permissions.enabled"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:n("tooltip.permissions_enabled")})]}),e(D,{})]}),e(P,{control:t.control,name:"permissions_jobs",render:({field:h})=>e(ae,{in:d.value,mountOnEnter:!0,timeout:100,classNames:"fade",unmountOnExit:!0,onEnter:()=>{var f;c?(f=r.current)==null||f.scrollIntoView({behavior:"smooth",block:"nearest"}):m(!0)},children:e("div",{ref:r,className:"mt-2 w-2/3 rounded-md bg-primary-dark p-4",children:i("div",{className:"flex flex-col gap-2",children:[i("div",{className:"inline-flex cursor-pointer items-center gap-1",children:[e("h4",{children:n("permissions.jobs.title")}),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:n("tooltip.permissions_job")})]}),e(Nr,{jobs:h.value,onAddClick:()=>a(!0),onRemoveClick:f=>{var x;const g=(x=h.value)==null?void 0:x.filter(p=>p.jobName!==f);h.onChange(g)}})]})})})})]})})]}),e(Ar,{open:o,onOpenChange:a,onJobSelected:d=>{let h=t.getValues("permissions_jobs")||[];if(h.some(p=>p.jobName===d.jobName&&p.grade===d.grade)){L("notify",{title:n("exceptions.warning"),message:n("notification.job_already_defined"),isError:!0});return}h.find(p=>p.jobName===d.jobName)&&(h=h.filter(p=>p.jobName!==d.jobName));const x=[...h,d];t.setValue("permissions_jobs",x,{shouldDirty:!0,shouldValidate:!0}),a(!1)}})]})},gt=({control:t,name:s,disabled:n})=>{const r=V(),o=Ns({control:t,name:"label"});if(!(s in t._formValues))throw new Error(`${s} is not in control field`);return e(P,{control:t,name:"key",render:({field:a})=>i(O,{children:[i(I,{className:"inline-flex items-center gap-1",children:[r("inputs.key"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:r("tooltip.key")})]}),e($,{children:e(W,{disabled:n,className:"w-auto",placeholder:r("placeholders.page_key"),icon:!n&&e("div",{className:"-m-3 cursor-pointer p-3",onClick:()=>{a.onChange(ee(o))},children:e(Es,{size:"1.2rem",className:"transition-colors hover:text-accent"})}),...a})}),e(D,{})]})})},$e=({control:t,name:s,onChange:n,label:r,placeholder:o,labelClassName:a,type:c="text"})=>{if(!(s in t._formValues))throw new Error(`${s} is not in control field`);return e(P,{control:t,name:s,render:({field:m})=>i(O,{children:[e(I,{className:a,children:r}),e($,{children:e(W,{className:"w-auto",placeholder:o,...m,onChange:d=>{m.onChange(d),n==null||n(d.target.value)},type:c})}),e(D,{})]})})},Ir=(t,s)=>Object.values(s).some(n=>{var r;return(r=n.pages)==null?void 0:r.some(o=>o.key===t)}),Pr=(t,s)=>Object.values(s).some(n=>n.key===t),Or=(t,s)=>s.some(n=>n.key===t),Ct=({})=>{const t=V(),{categories:s,setCurrentContentPage:n,setIsLoading:r,editedCategoryKey:o,setEditedCategoryKey:a}=R(),c=!!o,m=ht.extend({label:_.string().min(3,{message:t("exceptions.must_be_length_3")}),key:_.string().min(3,{message:t("exceptions.must_be_length_3")}).refine(y=>!y.includes(" "),t("exceptions.no_spaces")).refine(y=>c?y===s[o].key:!0,t("exceptions.cant_be_edited")).optional(),order_number:_.coerce.number().gte(0,t("exceptions.equal_higher_than",{number:0})),default_expand:_.boolean()}),h=(y=>{var A;if(y&&o){const k=s[o],z=y?((A=k==null?void 0:k.attributes)==null?void 0:A.permissions)||{}:void 0;return{label:k.label,key:k.key,order_number:k.order_number,is_enabled:k.is_enabled,default_expand:k.default_expand,permissions_enabled:y&&(z==null?void 0:z.enabled)||!1,permissions_jobs:y?z==null?void 0:z.jobs:[]}}else return{label:"",key:"",order_number:Object.values(s).length+1,is_enabled:!0,default_expand:!1,permissions_enabled:!1,permissions_jobs:[]}})(c),f=fe({resolver:ge(m),defaultValues:h,mode:"onChange"}),g=y=>{r(!0);const A={...y,attributes:{permissions:{enabled:y.permissions_enabled,jobs:y.permissions_jobs}}};L("saveEndpoint",{data:A,type:re.CATEGORY}).then(k=>{r(!1),k?L("notify",{title:t("exceptions.success"),message:t("notification.data_save_success")}):L("notify",{title:t("exceptions.error"),message:t("notification.data_save_error"),isError:!0}),n(N.CATEGORY_LIST),a(void 0)})},x=y=>{if(!c&&(typeof y.key=="string"||!y.key)){const A=ee(y.label);y.key=A}if(!c&&Pr(y.key,s)){L("notify",{title:t("exceptions.warning"),message:t("notification.key_exists"),isError:!0});return}g(y)},p=()=>{const y=f.getValues("label"),A=ee(y);f.setValue("key",A)},S=c?!f.formState.isValid||!f.formState.isDirty:!f.formState.isValid;return b.exports.useEffect(()=>()=>{a(void 0)},[]),i("div",{children:[e(le,{onClick:()=>{n(N.CATEGORY_LIST)},title:t(c?"category.category_editing":"category.category_creating")}),e("div",{className:"mt-6",children:e(be,{...f,children:i("form",{onSubmit:f.handleSubmit(x),className:"space-y-8",children:[i("div",{className:"flex gap-6",children:[e($e,{control:f.control,name:"label",onChange:()=>{c||p()},label:t("inputs.label"),placeholder:t("placeholders.category_label")}),e(gt,{control:f.control,name:"key",disabled:c})]}),e($e,{control:f.control,name:"order_number",labelClassName:"inline-flex items-center gap-1",label:i(Y,{children:[t("inputs.order_number"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.order_number")})]}),placeholder:t("placeholders.order_number"),type:"number"}),e(P,{control:f.control,name:"is_enabled",render:({field:y})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:y.value,onCheckedChange:A=>{y.onChange(A)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.enabled"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.enabled")})]}),e(D,{})]})}),e(P,{control:f.control,name:"default_expand",render:({field:y})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:y.value,onCheckedChange:A=>{y.onChange(A)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.expanded"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.expanded")})]}),e(D,{})]})}),e(ft,{form:f}),i(ce,{disabled:S,variant:"secondary",className:"absolute bottom-4 right-6 z-30 flex items-center gap-2",type:"submit",children:[e(he,{size:"1.2rem"})," ",t("inputs.submit")]})]})})})]})};var $r=globalThis&&globalThis.__extends||function(){var t=function(s,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var a in o)o.hasOwnProperty(a)&&(r[a]=o[a])},t(s,n)};return function(s,n){t(s,n);function r(){this.constructor=s}s.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ve=globalThis&&globalThis.__assign||function(){return Ve=Object.assign||function(t){for(var s,n=1,r=arguments.length;n<r;n++){s=arguments[n];for(var o in s)Object.prototype.hasOwnProperty.call(s,o)&&(t[o]=s[o])}return t},Ve.apply(this,arguments)},zr=globalThis&&globalThis.__spreadArrays||function(){for(var t=0,s=0,n=arguments.length;s<n;s++)t+=arguments[s].length;for(var r=Array(t),o=0,s=0;s<n;s++)for(var a=arguments[s],c=0,m=a.length;c<m;c++,o++)r[o]=a[c];return r};globalThis&&globalThis.__importDefault;var ne=function(t){$r(s,t);function s(n){var r=t.call(this,n)||this;r.dirtyProps=["modules","formats","bounds","theme","children"],r.cleanProps=["id","className","style","placeholder","tabIndex","onChange","onChangeSelection","onFocus","onBlur","onKeyPress","onKeyDown","onKeyUp"],r.state={generation:0},r.selection=null,r.onEditorChange=function(a,c,m,d){var h,f,g,x;a==="text-change"?(f=(h=r).onEditorChangeText)===null||f===void 0||f.call(h,r.editor.root.innerHTML,c,d,r.unprivilegedEditor):a==="selection-change"&&((x=(g=r).onEditorChangeSelection)===null||x===void 0||x.call(g,c,d,r.unprivilegedEditor))};var o=r.isControlled()?n.value:n.defaultValue;return r.value=o!=null?o:"",r}return s.prototype.validateProps=function(n){var r;if(F.Children.count(n.children)>1)throw new Error("The Quill editing area can only be composed of a single React element.");if(F.Children.count(n.children)){var o=F.Children.only(n.children);if(((r=o)===null||r===void 0?void 0:r.type)==="textarea")throw new Error("Quill does not support editing on a <textarea>. Use a <div> instead.")}if(this.lastDeltaChangeSet&&n.value===this.lastDeltaChangeSet)throw new Error("You are passing the `delta` object from the `onChange` event back as `value`. You most probably want `editor.getContents()` instead. See: https://github.com/zenoamaro/react-quill#using-deltas")},s.prototype.shouldComponentUpdate=function(n,r){var o=this,a;if(this.validateProps(n),!this.editor||this.state.generation!==r.generation)return!0;if("value"in n){var c=this.getEditorContents(),m=(a=n.value,a!=null?a:"");this.isEqualValue(m,c)||this.setEditorContents(this.editor,m)}return n.readOnly!==this.props.readOnly&&this.setEditorReadOnly(this.editor,n.readOnly),zr(this.cleanProps,this.dirtyProps).some(function(d){return!Se(n[d],o.props[d])})},s.prototype.shouldComponentRegenerate=function(n){var r=this;return this.dirtyProps.some(function(o){return!Se(n[o],r.props[o])})},s.prototype.componentDidMount=function(){this.instantiateEditor(),this.setEditorContents(this.editor,this.getEditorContents())},s.prototype.componentWillUnmount=function(){this.destroyEditor()},s.prototype.componentDidUpdate=function(n,r){var o=this;if(this.editor&&this.shouldComponentRegenerate(n)){var a=this.editor.getContents(),c=this.editor.getSelection();this.regenerationSnapshot={delta:a,selection:c},this.setState({generation:this.state.generation+1}),this.destroyEditor()}if(this.state.generation!==r.generation){var m=this.regenerationSnapshot,a=m.delta,d=m.selection;delete this.regenerationSnapshot,this.instantiateEditor();var h=this.editor;h.setContents(a),wt(function(){return o.setEditorSelection(h,d)})}},s.prototype.instantiateEditor=function(){this.editor?this.hookEditor(this.editor):this.editor=this.createEditor(this.getEditingArea(),this.getEditorConfig())},s.prototype.destroyEditor=function(){!this.editor||this.unhookEditor(this.editor)},s.prototype.isControlled=function(){return"value"in this.props},s.prototype.getEditorConfig=function(){return{bounds:this.props.bounds,formats:this.props.formats,modules:this.props.modules,placeholder:this.props.placeholder,readOnly:this.props.readOnly,scrollingContainer:this.props.scrollingContainer,tabIndex:this.props.tabIndex,theme:this.props.theme}},s.prototype.getEditor=function(){if(!this.editor)throw new Error("Accessing non-instantiated editor");return this.editor},s.prototype.createEditor=function(n,r){var o=new yt(n,r);return r.tabIndex!=null&&this.setEditorTabIndex(o,r.tabIndex),this.hookEditor(o),o},s.prototype.hookEditor=function(n){this.unprivilegedEditor=this.makeUnprivilegedEditor(n),n.on("editor-change",this.onEditorChange)},s.prototype.unhookEditor=function(n){n.off("editor-change",this.onEditorChange)},s.prototype.getEditorContents=function(){return this.value},s.prototype.getEditorSelection=function(){return this.selection},s.prototype.isDelta=function(n){return n&&n.ops},s.prototype.isEqualValue=function(n,r){return this.isDelta(n)&&this.isDelta(r)?Se(n.ops,r.ops):Se(n,r)},s.prototype.setEditorContents=function(n,r){var o=this;this.value=r;var a=this.getEditorSelection();typeof r=="string"?n.setContents(n.clipboard.convert(r)):n.setContents(r),wt(function(){return o.setEditorSelection(n,a)})},s.prototype.setEditorSelection=function(n,r){if(this.selection=r,r){var o=n.getLength();r.index=Math.max(0,Math.min(r.index,o-1)),r.length=Math.max(0,Math.min(r.length,o-1-r.index)),n.setSelection(r)}},s.prototype.setEditorTabIndex=function(n,r){var o,a;!((a=(o=n)===null||o===void 0?void 0:o.scroll)===null||a===void 0)&&a.domNode&&(n.scroll.domNode.tabIndex=r)},s.prototype.setEditorReadOnly=function(n,r){r?n.disable():n.enable()},s.prototype.makeUnprivilegedEditor=function(n){var r=n;return{getHTML:function(){return r.root.innerHTML},getLength:r.getLength.bind(r),getText:r.getText.bind(r),getContents:r.getContents.bind(r),getSelection:r.getSelection.bind(r),getBounds:r.getBounds.bind(r)}},s.prototype.getEditingArea=function(){if(!this.editingArea)throw new Error("Instantiating on missing editing area");var n=es.findDOMNode(this.editingArea);if(!n)throw new Error("Cannot find element for editing area");if(n.nodeType===3)throw new Error("Editing area cannot be a text node");return n},s.prototype.renderEditingArea=function(){var n=this,r=this.props,o=r.children,a=r.preserveWhitespace,c=this.state.generation,m={key:c,ref:function(d){n.editingArea=d}};return F.Children.count(o)?F.cloneElement(F.Children.only(o),m):a?F.createElement("pre",Ve({},m)):F.createElement("div",Ve({},m))},s.prototype.render=function(){var n;return F.createElement("div",{id:this.props.id,style:this.props.style,key:this.state.generation,className:"quill "+(n=this.props.className,n!=null?n:""),onKeyPress:this.props.onKeyPress,onKeyDown:this.props.onKeyDown,onKeyUp:this.props.onKeyUp},this.renderEditingArea())},s.prototype.onEditorChangeText=function(n,r,o,a){var c,m;if(!!this.editor){var d=this.isDelta(this.value)?a.getContents():a.getHTML();d!==this.getEditorContents()&&(this.lastDeltaChangeSet=r,this.value=d,(m=(c=this.props).onChange)===null||m===void 0||m.call(c,n,r,o,a))}},s.prototype.onEditorChangeSelection=function(n,r,o){var a,c,m,d,h,f;if(!!this.editor){var g=this.getEditorSelection(),x=!g&&n,p=g&&!n;Se(n,g)||(this.selection=n,(c=(a=this.props).onChangeSelection)===null||c===void 0||c.call(a,n,r,o),x?(d=(m=this.props).onFocus)===null||d===void 0||d.call(m,n,r,o):p&&((f=(h=this.props).onBlur)===null||f===void 0||f.call(h,g,r,o)))}},s.prototype.focus=function(){!this.editor||this.editor.focus()},s.prototype.blur=function(){!this.editor||(this.selection=null,this.editor.blur())},s.displayName="React Quill",s.Quill=yt,s.defaultProps={theme:"snow",modules:{},readOnly:!1},s}(F.Component);function wt(t){Promise.resolve().then(t)}const Dr=t=>{var a;const s=(a=t.current)==null?void 0:a.getEditor();if(!s)return;const n=s.theme.tooltip,r=n.save.bind(n),o=n.hide.bind(n);n.save=function(){const c=s.getSelection(!0),m=this.textbox.value;m&&s.insertEmbed(c.index,"image",m,"user")},n.hide=()=>{n.save=r,n.hide=o,n.hide()},n.edit("image"),n.textbox.placeholder="Enter image URL"},Lr=ne.Quill.import("blots/embed"),_n=ne.Quill.import("ui/icons"),Fr='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><use href="img/teleport.svg#teleport"></use></svg>';_n.navBtn=Fr;class qe extends Lr{static create(s){let n=super.create();return n.setAttribute("type","gps"),n.innerHTML+=_n.navBtn,n.append(s.label),n.setAttribute("class","gps-button"),n.setAttribute("data-label",s.label),n.setAttribute("data-x",s.pos.x),n.setAttribute("data-y",s.pos.y),n.onclick=function(r){r.preventDefault(),r.stopPropagation()},n}static value(s){return{label:s.getAttribute("data-label"),pos:{x:s.getAttribute("data-x"),y:s.getAttribute("data-y")}}}}qe.blotName="navBtn";qe.className="quill-nav-btn";qe.tagName="button";const rt=({form:t,isLoading:s,onIsLoadingChange:n,dataNames:r,hideButton:o})=>{const a=c=>{c.preventDefault(),c.stopPropagation(),n==null||n(!0),L("getCoords").then(m=>{r!=null&&r.x&&t.setValue(r.x.name,parseFloat(m.x.toFixed(2))),r!=null&&r.y&&t.setValue(r.y.name,parseFloat(m.y.toFixed(2))),r!=null&&r.z&&t.setValue(r.z.name,parseFloat(m.z.toFixed(2))),n==null||n(!1)})};return i("div",{className:"mt-2 flex flex-row items-center gap-4",children:[(r==null?void 0:r.x)&&e(P,{control:t.control,name:r.x.name,render:({field:c})=>{var m,d,h;return e(O,{children:i("div",{className:"flex items-center gap-2",children:[e(I,{children:"X"}),e($,{children:e(W,{type:"number",className:"max-w-[110px]",...c,min:(m=r==null?void 0:r.x)==null?void 0:m.min,max:(d=r==null?void 0:r.x)==null?void 0:d.max,step:(h=r==null?void 0:r.x)==null?void 0:h.step})})]})})}}),(r==null?void 0:r.y)&&e(P,{control:t.control,name:r.y.name,render:({field:c})=>{var m,d,h;return e(O,{children:i("div",{className:"flex items-center gap-2",children:[e(I,{children:"Y"}),e($,{children:e(W,{type:"number",className:"max-w-[110px]",...c,min:(m=r==null?void 0:r.x)==null?void 0:m.min,max:(d=r==null?void 0:r.x)==null?void 0:d.max,step:(h=r==null?void 0:r.x)==null?void 0:h.step})})]})})}}),(r==null?void 0:r.z)&&e(P,{control:t.control,name:r.z.name,render:({field:c})=>{var m,d,h;return e(O,{children:i("div",{className:"flex items-center gap-2",children:[e(I,{children:"Z"}),e($,{children:e(W,{type:"number",className:"max-w-[110px]",...c,min:(m=r==null?void 0:r.x)==null?void 0:m.min,max:(d=r==null?void 0:r.x)==null?void 0:d.max,step:(h=r==null?void 0:r.x)==null?void 0:h.step})})]})})}}),!o&&e(B,{isLoading:s,className:"bg-input-bg text-accent hover:bg-input-bg-focus",size:10,icon:e(ot,{}),onClick:c=>a(c)})]})},Rr=({isOpen:t,onOpenChange:s,onCreate:n})=>{const[r,o]=b.exports.useState(!1),a=V(),c=_.object({label:_.string().min(1,a("exceptions.field_required")),x:_.coerce.number().gte(-1e6).lte(1e6),y:_.coerce.number().gte(-1e6).lte(1e6)}),m={label:"",x:0,y:0},d=fe({resolver:ge(c),defaultValues:m,mode:"onChange"}),h=g=>{g||d.reset(),s==null||s(g)},f=g=>{d.reset(),n==null||n({label:g.label,pos:{x:g.x,y:g.y}})};return e(De,{open:t,onOpenChange:h,children:i(ye,{children:[i(xe,{children:[e(_e,{children:a("inputs.nav_btn")}),e(ke,{children:e("span",{className:"block",dangerouslySetInnerHTML:{__html:a("inputs.nav_btn_description")}})}),e(be,{...d,children:i("form",{className:"mt-4 text-text-inactive",autoComplete:"off",children:[e(P,{control:d.control,name:"label",render:({field:g})=>i(O,{className:"mt-2",children:[e(I,{children:a("inputs.label")}),e($,{children:e(W,{className:"w-auto",placeholder:a("placeholders.nav_btn_label"),...g})}),e(D,{})]})}),e(I,{className:"mt-4 block",children:a("inputs.position")}),e(rt,{form:d,isLoading:r,onIsLoadingChange:o,dataNames:{x:{name:"x"},y:{name:"y"}}})]})})]}),i(ve,{className:"mt-4 flex gap-2",children:[e(we,{children:a("inputs.cancel")}),e(Ce,{disabled:r,onClick:d.handleSubmit(f),className:w(ze({variant:"secondary"})),children:a("inputs.create")})]})]})})},jr=ne.Quill,Vr=jr.import("formats/image"),Nt=["alt","height","width","style"];class Mr extends Vr{static formats(s){return Nt.reduce((n,r)=>(s.hasAttribute(r)&&(n[r]=s.getAttribute(r)||""),n),{})}format(s,n){Nt.includes(s)?n?this.domNode.setAttribute(s,n):this.domNode.removeAttribute(s):super.format(s,n)}}const Gr=ne.Quill.import("ui/icons"),Kr='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><use href="img/iframe.svg#iframe"></use></svg>';Gr.iframe=Kr;const Br=ne.Quill.import("blots/embed");class bt extends Br{static create(s){let n=super.create();n.setAttribute("src",s.url),n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("class","iframe-fullsize");let r="bg-primary-light keepHTML";return s.isFullSize&&(r+=" iframe-fullsize"),n.setAttribute("class",r),n}static value(s){return{url:s.getAttribute("src"),isFullSize:s.getAttribute("class")==="iframe-fullsize"}}}bt.blotName="iframe";bt.tagName="iframe";const Hr=({isOpen:t,onOpenChange:s,onCreate:n})=>{const[r]=b.exports.useState(!1),o=V(),a=_.object({url:_.string().min(1,o("exceptions.field_required")),is_full_size:_.boolean()}),c={url:"",is_full_size:!0},m=fe({resolver:ge(a),defaultValues:c,mode:"onChange"}),d=f=>{f||m.reset(),s==null||s(f)},h=f=>{m.reset(),n==null||n({url:f.url,isFullSize:f.is_full_size})};return e(De,{open:t,onOpenChange:d,children:i(ye,{children:[i(xe,{children:[e(_e,{children:o("inputs.iframe")}),i(ke,{children:[e("span",{className:"block",dangerouslySetInnerHTML:{__html:o("inputs.iframe_description")}}),e("p",{className:"mt-2",children:o("inputs.iframe_warning")})]}),e(be,{...m,children:i("form",{className:"mt-4 space-y-6 text-text-inactive",autoComplete:"off",children:[e(P,{control:m.control,name:"url",render:({field:f})=>i(O,{className:"mt-2 space-y-1",children:[e(I,{className:"font-bold",children:o("inputs.url")}),e($,{children:e(W,{className:"w-2/3",placeholder:o("placeholders.iframe_url"),...f})}),e(D,{})]})}),i("div",{children:[e(P,{control:m.control,name:"is_full_size",render:({field:f})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:f.value,onCheckedChange:g=>{f.onChange(g)}})}),e(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:o("inputs.is_full_size")}),e(D,{})]})}),e("p",{className:"text-inactive mt-2 text-sm opacity-60",children:o("inputs.is_full_size_description")})]})]})})]}),i(ve,{className:"mt-4 flex gap-2",children:[e(we,{children:o("inputs.cancel")}),e(Ce,{disabled:r,onClick:m.handleSubmit(h),className:w(ze({variant:"secondary"})),children:o("inputs.create")})]})]})})},Ur=ne.Quill,qr=Ur.import("formats/video"),Et=["alt","height","width","style"];class Jr extends qr{static formats(s){return Et.reduce((n,r)=>(s.hasAttribute(r)&&(n[r]=s.getAttribute(r)||""),n),{})}format(s,n){Et.includes(s)?n?this.domNode.setAttribute(s,n):this.domNode.removeAttribute(s):super.format(s,n)}}const Wr=ne.Quill.import("blots/block/embed");class Je extends Wr{static create(s){return s}static value(s){return s}}Je.blotName="keepHTML";Je.className="keepHTML";Je.tagName="div";const te=ne.Quill;te.register("modules/imageDropAndPaste",Ss);te.register("modules/resize",Ts);te.register("formats/navBtn",qe);te.register("formats/iframe",bt);te.register(Mr,!0);te.register(Jr,!0);te.register(Je,!0);const kn=({content:t,onChange:s})=>{const n=V(),r=b.exports.useRef(null),{userConfig:o,quill:a,setQuill:c,canInsertIFrame:m}=R(),[d,h]=b.exports.useState(!1),[f,g]=b.exports.useState(!1),[x,p]=b.exports.useState(0),y=(o!=null&&o.pageFonts?["Inter",...o==null?void 0:o.pageFonts]:["Inter"]).map(u=>ee(u)),A=b.exports.useMemo(()=>{const u=y.length>0?y:!1;return[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],["blockquote","code-block","link"],[{list:"ordered"},{list:"bullet"}],[{align:[]},{indent:"-1"},{indent:"+1"}],[{script:"sub"},{script:"super"}],[{direction:"rtl"}],[{font:u},{color:[]},{background:[]}],[{image:!0},{video:!0}],["clean"],["navBtn",m&&"iframe"].filter(Boolean)]},[y]),k=function(){var l,C;const u=((C=(l=r.current)==null?void 0:l.getEditor().getSelection())==null?void 0:C.index)||0;p(u),h(!0)},z=function(){var l,C;const u=((C=(l=r.current)==null?void 0:l.getEditor().getSelection())==null?void 0:C.index)||0;p(u),g(!0)},v=()=>{L("notify",{title:n("exceptions.warning"),message:n("notification.image_paste_error"),isError:!0})},j=b.exports.useMemo(()=>({toolbar:{container:A,handlers:{image:()=>Dr(r),navBtn:k,iframe:z}},history:!0,clipboard:!0,resize:{locale:{altTip:n("editor.alt_tip"),inputTip:n("editor.input_tip"),floatLeft:n("editor.float_left"),floatRight:n("editor.float_right"),center:n("editor.center"),restore:n("editor.restore")}},imageDropAndPaste:{handler:v}}),[]),T=()=>{const u=document.createElement("style");return u.type="text/css",u.innerHTML=`
      .ql-snow .ql-tooltip[data-mode='image']::before {
        content: 'Enter URL' !important;
      }
    `,document.head.appendChild(u),u};return b.exports.useEffect(()=>{if(r.current){const u=r.current;c==null||c(u)}},[r]),b.exports.useEffect(()=>{const u=te.import("formats/font");u.whitelist=y,te.register(u,!0)},[y]),b.exports.useEffect(()=>{const u=T();return()=>{document.head.removeChild(u),c==null||c(null)}},[]),i(Y,{children:[e(ne,{ref:r,modules:j,theme:"snow",value:t,onChange:s}),e(Rr,{isOpen:d,onOpenChange:h,onCreate:u=>{a==null||a.getEditor().insertEmbed(x,"navBtn",u),h(!1),p(0)}}),e(Hr,{isOpen:f,onOpenChange:g,onCreate:u=>{a==null||a.getEditor().insertEmbed(x,"iframe",u),g(!1),p(0)}})]})},Yr=t=>{const s=/<iframe.*?>(.*?)<\/iframe>/g,n=t.match(s);return n&&n.forEach(r=>{t.replace(r,"")}),t},Qr=()=>{const t=V(),{setCurrentContentPage:s,setIsLoading:n,setEditedCategoryKey:r,setEditedPageKey:o,setPageCache:a,setLatestUpdatedPageKey:c}=R();return{savePage:d=>{n(!0),d.content=Yr(d.content);const h={...d,attributes:{permissions:{enabled:d.permissions_enabled,jobs:d.permissions_jobs}}};L("saveEndpoint",{data:h,type:re.PAGE}).then(f=>{n(!1),f?(L("notify",{title:t("exceptions.success"),message:t("notification.data_save_success")}),a(g=>{var p;const x=(p=g[h.key])==null?void 0:p.id;return{...g,[h.key]:{...h,id:x}}})):L("notify",{title:t("exceptions.error"),message:t("notification.data_save_error"),isError:!0}),s(N.PAGE_LIST),o(void 0),r(void 0),c(void 0)})}}},Zr=({control:t})=>{const s=V(),{categories:n}=R();if(!("category_key"in t._formValues))throw new Error("category_key is not in control field");const r=Object.values(n).map(o=>({label:o.label,value:o.key}));return e(P,{control:t,name:"category_key",render:({field:o})=>i(O,{className:"w-1/2",children:[e(I,{children:s("inputs.category")}),i(Pe,{onValueChange:o.onChange,value:o.value,children:[e($,{children:e(ue,{children:e(Oe,{})})}),e(me,{children:r.map(a=>e(ie,{value:a.value,children:a.label},a.value))})]}),e(D,{})]})})},St=({})=>{const t=V(),{savePage:s}=Qr(),{categories:n,setCurrentContentPage:r,setIsLoading:o,editedCategoryKey:a,editedPageKey:c,pageCache:m,setPageCache:d,setClickedEmptyCategoryKey:h,latestUpdatedPageKey:f,setLatestUpdatedPageKey:g,clickedEmptyCategoryKey:x}=R(),[p,S]=b.exports.useState(void 0),y=!!p,A=ht.extend({category_key:_.string().refine(E=>!!E,t("exceptions.must_be_selected")),label:_.string().min(3,{message:t("exceptions.must_be_length_3")}),key:_.string().min(3,{message:t("exceptions.must_be_length_3")}).refine(E=>!E.includes(" "),t("exceptions.no_spaces")).refine(E=>y?E===p.key:!0,t("exceptions.cant_be_edited")),order_number:_.coerce.number().gte(0,t("exceptions.equal_higher_than",{number:0})),content:_.string()}),k=(E,u)=>{var G;const l=u||p,C=E?((G=l==null?void 0:l.attributes)==null?void 0:G.permissions)||{}:void 0,M=E?l==null?void 0:l.order_number:1;return{category_key:E?l==null?void 0:l.category_key:x||void 0,label:E?l==null?void 0:l.label:"",key:E?l==null?void 0:l.key:"",order_number:M,is_enabled:E?l==null?void 0:l.is_enabled:!0,content:E?l==null?void 0:l.content:"",permissions_enabled:E&&(C==null?void 0:C.enabled)||!1,permissions_jobs:E?C==null?void 0:C.jobs:[]}},z=k(y),v=fe({resolver:ge(A),defaultValues:z,mode:"onChange"}),j=E=>{if(!y&&(typeof E.key=="string"||!E.key)){const u=ee(E.label);E.key=u}if(!y&&Ir(E.key,n)){L("notify",{title:t("exceptions.warning"),message:t("notification.key_exists"),isError:!0});return}s(E)},T=()=>{const E=v.getValues("label"),u=ee(E);v.setValue("key",u)},K=y?!v.formState.isValid||!v.formState.isDirty:!v.formState.isValid;return b.exports.useEffect(()=>{if(h(void 0),g(void 0),!c||!a){S(void 0),v.reset(k(y));return}return o(!0),Be(c,m,E=>{d(u=>({...u,[E.key]:E})),S(E),v.reset(k(!0,E)),o(!1)},void 0),()=>{S(void 0)}},[]),b.exports.useEffect(()=>{f&&f===c&&L("notify",{title:t("exceptions.warning"),message:t("notification.collision"),isError:!0})},[f]),i("div",{children:[e(le,{onClick:()=>{r(N.PAGE_LIST)},title:t(y?"page.page_editing":"page.page_creating")}),e("div",{className:"mt-6",children:e(be,{...v,children:i("form",{onSubmit:v.handleSubmit(j),className:"space-y-8",children:[e(Zr,{control:v.control}),i("div",{className:"flex gap-6",children:[e($e,{control:v.control,name:"label",onChange:()=>{y||T()},label:t("inputs.label"),placeholder:t("placeholders.page_label")}),e(gt,{control:v.control,name:"key",disabled:y})]}),e($e,{control:v.control,name:"order_number",labelClassName:"inline-flex items-center gap-1",label:i(Y,{children:[t("inputs.order_number"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.order_number")})]}),placeholder:t("placeholders.order_number"),type:"number"}),e(P,{control:v.control,name:"is_enabled",render:({field:E})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:E.value,onCheckedChange:u=>{E.onChange(u)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.enabled"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.enabled")})]}),e(D,{})]})}),e(P,{control:v.control,name:"content",render:({field:E})=>i(O,{children:[e(I,{className:"font-bold",children:t("inputs.content")}),e("p",{className:"text-sm",children:t("tooltip.resize")}),e($,{children:e(kn,{content:E.value,onChange:u=>{E.onChange(u)}})}),e(D,{})]})}),e(ft,{form:v}),i(ce,{disabled:K,variant:"secondary",className:"absolute bottom-4 right-6 z-30 flex items-center gap-2",type:"submit",children:[e(he,{size:"1.2rem"})," ",t("inputs.submit")]})]})})})]})},se=b.exports.forwardRef(({className:t,...s},n)=>i(en,{ref:n,className:w("relative flex w-full touch-none select-none items-center",t),...s,children:[e(Hs,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-input-bg",children:e(Us,{className:"absolute h-full bg-accent"})}),e(qs,{className:"block h-4 w-4 rounded-full bg-white ring-offset-black transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));se.displayName=en.displayName;const tt=t=>{let s=t.toString(16);return s.length==1?"0"+s:s},Tt=(t,s,n)=>"#"+tt(t)+tt(s)+tt(n),At=t=>{const s=parseInt(t.substring(1,3),16),n=parseInt(t.substring(3,5),16),r=parseInt(t.substring(5,7),16);return{r:s,g:n,b:r}},Te="custom_content",It=()=>{const t=V(),{categories:s,points:n,setCurrentContentPage:r,editedPointKey:o,setIsLoading:a,setEditedPointKey:c,userConfig:m}=R(),d=!!o,h=_.coerce.string().refine(u=>!isNaN(parseFloat(u)),t("exceptions.must_be_number")).transform(u=>parseFloat(u)),f=ht.extend({label:_.string().min(3,{message:t("exceptions.must_be_length_3")}),key:_.string().min(3,{message:t("exceptions.must_be_length_3")}).refine(u=>!u.includes(" "),t("exceptions.no_spaces")).refine(u=>d?u===o:!0,t("exceptions.cant_be_edited")).optional(),size:_.number().min(0).max(5),draw_distance:_.number().min(1).max(200),color:_.object({r:_.number().min(0).max(255),g:_.number().min(0).max(255),b:_.number().min(0).max(255),a:_.number().min(0).max(255)}),font:_.number(),help_key:_.string(),content:_.string(),can_navigate:_.boolean(),blip_enabled:_.boolean(),blip_sprite:_.coerce.number(),blip_display_type:_.coerce.number().min(0).max(10),blip_color:_.coerce.number().min(0).max(85),blip_size:_.number().min(.1).max(3),marker_enabled:_.boolean(),marker_type:_.coerce.number().min(0).max(43),marker_color:_.object({r:_.number().min(0).max(255),g:_.number().min(0).max(255),b:_.number().min(0).max(255),a:_.number().min(0).max(255)}),marker_draw_distance:_.number().min(1).max(200),marker_size:_.object({x:h,y:h,z:h}),is_rotation_enabled:_.boolean(),marker_rotation:_.object({x:_.number().min(0).max(360),y:_.number().min(0).max(360),z:_.number().min(0).max(360)}),position:_.object({x:h,y:h,z:h})}),x=(u=>{var J;const l=n.find(Q=>Q.key===o),C=u?((J=l==null?void 0:l.attributes)==null?void 0:J.permissions)||{}:void 0,M=u?l==null?void 0:l.font:0,G=typeof M=="number"?M:0;return{label:u?l==null?void 0:l.label:"",key:u?l==null?void 0:l.key:"",size:u?l==null?void 0:l.size:1,draw_distance:u?l==null?void 0:l.draw_distance:5,color:u?{r:255,g:255,b:255,a:255,...l==null?void 0:l.color}:{r:255,g:255,b:255,a:255},content:u?l==null?void 0:l.content:"",font:G,help_key:u?l==null?void 0:l.help_key:Te,is_enabled:u?l==null?void 0:l.is_enabled:!0,blip_sprite:u?l==null?void 0:l.blip_sprite:162,blip_display_type:u?l==null?void 0:l.blip_display_type:2,blip_color:u?l==null?void 0:l.blip_color:4,blip_size:u?l==null?void 0:l.blip_size:1,blip_enabled:u?l==null?void 0:l.blip_enabled:!1,marker_enabled:u?l==null?void 0:l.marker_enabled:!1,marker_type:u?l==null?void 0:l.marker_type:0,marker_color:u?{r:255,g:255,b:255,a:255,...l==null?void 0:l.marker_color}:{r:255,g:255,b:255,a:255},marker_draw_distance:u?l==null?void 0:l.marker_draw_distance:20,marker_size:u?l==null?void 0:l.marker_size:{x:1,y:1,z:1},is_rotation_enabled:u?l==null?void 0:l.is_rotation_enabled:!1,marker_rotation:u?l==null?void 0:l.marker_rotation:{x:0,y:0,z:0},position:u?l==null?void 0:l.position:{x:0,y:0,z:0},can_navigate:u?l==null?void 0:l.can_navigate:!1,permissions_enabled:u&&(C==null?void 0:C.enabled)||!1,permissions_jobs:u?(C==null?void 0:C.jobs)||[]:[]}})(d),p=fe({resolver:ge(f),defaultValues:x,mode:"onChange"}),S=u=>{a(!0);const l={...u,attributes:{permissions:{enabled:u.permissions_enabled,jobs:u.permissions_jobs}}};L("saveEndpoint",{data:l,type:re.POINT}).then(C=>{a(!1),C?L("notify",{title:t("exceptions.success"),message:t("notification.data_save_success")}):L("notify",{title:t("exceptions.error"),message:t("notification.data_save_error"),isError:!0}),r(N.POINT_LIST),c(void 0)})},y=u=>{if(!d&&(typeof u.key=="string"||!u.key)){const l=ee(u.label);u.key=l}if(!d&&Or(u.key,n)){L("notify",{title:t("exceptions.warning"),message:t("notification.key_exists"),isError:!0});return}S(u)},A=()=>{const u=p.getValues("label"),l=ee(u);p.setValue("key",l)},k=()=>m!=null&&m.gtaFonts?Object.entries(m.gtaFonts).map(([l,C])=>({label:C,value:l})):[],z=p.watch("help_key"),v=b.exports.useCallback(()=>{p.setValue("help_key",Te),L("notify",{title:t("exceptions.error"),message:t("notification.non_existing_page"),isError:!0})},[]),j=()=>{const u=[{label:t("inputs.custom_content"),value:Te,hideKey:!0}];let l=z!==Te;return Object.entries(s).map(([C,M])=>{const{pages:G}=M;!(G!=null&&G.length)||(u.push({label:`-------${M.label}-------`,value:C,disabled:!0,hideKey:!0}),G.map(J=>{J.key===z&&(l=!1),u.push({label:J.label,value:J.key})}))}),l&&v(),u},T=b.exports.useMemo(()=>j(),[s,z,p,v]),K=()=>T.map(u=>i(ie,{value:u.value,disabled:u.disabled,children:[u.label," ",!u.hideKey&&`(${u.value})`]},u.value)),E=b.exports.useMemo(()=>K(),[T]);return b.exports.useEffect(()=>()=>{c(void 0)},[]),i("div",{children:[e(le,{onClick:()=>{r(N.POINT_LIST)},title:t(d?"point.editing_point":"point.creating_point")}),e("p",{className:"text-sm",children:t("point.points_help")}),e("p",{className:"mt-2 text-sm",dangerouslySetInnerHTML:{__html:t("tooltip.arrow_inputs")}}),e("div",{className:"mt-6 pb-2",children:e(be,{...p,children:i("form",{onSubmit:p.handleSubmit(y),className:"space-y-8",children:[i("div",{className:"flex gap-6",children:[e($e,{control:p.control,name:"label",onChange:()=>{d||A()},label:i(Y,{children:[t("inputs.label"),i(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:[t("tooltip.point_label"),e("br",{}),t("tooltip.fivem_formatting")]})]}),labelClassName:"inline-flex items-center gap-1",placeholder:t("placeholders.point_label")}),e(gt,{control:p.control,name:"key",disabled:d})]}),i("div",{className:"grid w-2/3 grid-cols-2 gap-10",children:[e(P,{control:p.control,name:"size",render:({field:u})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:t("inputs.size")}),e("span",{children:p.getValues().size})]}),e($,{children:e(se,{defaultValue:[p.getValues().size],min:0,max:5,step:.1,onValueChange:l=>{u.onChange(l[0])}})}),e(D,{})]})}),e(P,{control:p.control,name:"draw_distance",render:({field:u})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:t("inputs.draw_distance")}),e("span",{children:p.getValues().draw_distance})]}),e($,{children:e(se,{defaultValue:[p.getValues().draw_distance],min:1,max:200,step:1,onValueChange:l=>{u.onChange(l[0])}})}),e(D,{})]})})]}),e(P,{control:p.control,name:"color",render:({field:u})=>{var l,C,M;return i(O,{className:"flex flex-row items-center gap-2 space-y-0",children:[e(I,{children:e("span",{children:t("inputs.color")})}),e($,{children:e(W,{className:"h-9 w-8 cursor-pointer p-1",type:"color",...u,value:Tt((l=p.getValues().color)==null?void 0:l.r,(C=p.getValues().color)==null?void 0:C.g,(M=p.getValues().color)==null?void 0:M.b),onChange:G=>{const{r:J,g:Q,b:X}=At(G.target.value);u.onChange({r:J,g:Q,b:X,a:255})}})}),e(D,{})]})}}),e(P,{control:p.control,name:"font",render:({field:u})=>{var l;return i(O,{className:"w-1/2",children:[e(I,{children:t("inputs.marker_font")}),i("div",{className:"flex gap-2",children:[i(Pe,{onValueChange:C=>{u.onChange(Number(C))},value:(l=u==null?void 0:u.value)==null?void 0:l.toString(),children:[e($,{children:e(ue,{children:e(Oe,{})})}),e(me,{children:k().map(C=>i(ie,{value:C.value,children:["(",C.value,") ",C.label]},C.value))})]}),e("img",{className:"h-10 w-fit rounded-lg",src:`img/fonts/${p.getValues().font}.png`})]}),e(D,{})]})}}),e(P,{control:p.control,name:"help_key",render:({field:u})=>{var l;return i(O,{className:"w-1/2",children:[i(I,{className:"inline-flex items-center gap-1",children:[t("inputs.open_page"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.open_page")})]}),i(Pe,{onValueChange:u.onChange,value:(l=u==null?void 0:u.value)==null?void 0:l.toString(),children:[e($,{children:e(ue,{children:e(Oe,{})})}),e(me,{children:E})]}),e(D,{})]})}}),p.getValues().help_key===Te&&e(P,{control:p.control,name:"content",render:({field:u})=>i(O,{children:[e(I,{className:"font-bold",children:t("inputs.content")}),e("p",{className:"text-sm",children:t("tooltip.resize")}),e($,{children:e(kn,{content:u.value,onChange:l=>{u.onChange(l)}})}),e(D,{})]})}),e("div",{children:e(P,{control:p.control,name:"blip_enabled",render:({field:u})=>i(Y,{children:[i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:u.value,onCheckedChange:l=>{u.onChange(l)}})}),i(I,{className:"inline-flex items-center gap-1 pl-2",children:[t("inputs.blip_enabled"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.blip_enabled")})]}),e(D,{})]}),e(ae,{in:u.value,mountOnEnter:!0,timeout:100,classNames:"fade",unmountOnExit:!0,children:i("div",{className:"mt-2 w-min rounded-md bg-primary-dark p-4",children:[i("div",{className:"flex gap-8",children:[e(P,{control:p.control,name:"blip_sprite",render:({field:l})=>i(O,{children:[i(I,{className:"inline-flex items-center gap-1",children:[t("inputs.blip_sprite"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.blip_sprite")})]}),e($,{children:e(W,{type:"number",className:"w-24",min:"0",...l})}),e(D,{})]})}),e(P,{control:p.control,name:"blip_display_type",render:({field:l})=>i(O,{children:[i(I,{className:"inline-flex items-center gap-1",children:[t("inputs.blip_display_type"),i(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:[e("span",{children:t("tooltip.blip_display_type_rest")}),i("table",{className:"mt-2 border-separate border-spacing-x-4 !text-sm",children:[e("thead",{children:i("tr",{children:[e("th",{children:t("tooltip.blip_display_type_id")}),e("th",{children:t("tooltip.blip_display_type_behaviour")})]})}),i("tbody",{children:[i("tr",{children:[e("td",{children:"0"}),e("td",{children:t("tooltip.blip_display_type_0")})]}),i("tr",{children:[e("td",{children:"2"}),e("td",{children:t("tooltip.blip_display_type_2")})]}),i("tr",{children:[e("td",{children:"3, 4"}),e("td",{children:t("tooltip.blip_display_type_3")})]}),i("tr",{children:[e("td",{children:"5"}),e("td",{children:t("tooltip.blip_display_type_5")})]}),i("tr",{children:[e("td",{children:"8"}),e("td",{children:t("tooltip.blip_display_type_8")})]})]})]})]})]}),e($,{children:e(W,{type:"number",min:"0",max:"10",className:"w-24",...l})}),e(D,{})]})}),e(P,{control:p.control,name:"blip_color",render:({field:l})=>i(O,{children:[i(I,{className:"inline-flex items-center gap-1",children:[t("inputs.blip_color"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.blip_color")})]}),e($,{children:e(W,{type:"number",min:"0",max:"85",className:"w-24",...l})}),e(D,{})]})})]}),e("div",{className:"mt-4 w-2/3",children:e(P,{control:p.control,name:"blip_size",render:({field:l})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:t("inputs.blip_size")}),e("span",{children:p.getValues().blip_size})]}),e($,{children:e(se,{defaultValue:[p.getValues().blip_size],min:.1,max:3,step:.1,onValueChange:C=>{l.onChange(C[0])}})}),e(D,{})]})})})]})})]})})}),e("div",{children:e(P,{control:p.control,name:"marker_enabled",render:({field:u})=>i(Y,{children:[i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:u.value,onCheckedChange:l=>{u.onChange(l)}})}),e(I,{className:"pl-2",children:t("inputs.marker_enabled")}),e(D,{})]}),e(ae,{in:u.value,mountOnEnter:!0,timeout:100,classNames:"fade",unmountOnExit:!0,children:i("div",{className:"mt-2 w-min rounded-md bg-primary-dark p-4",children:[i("div",{className:"flex items-center gap-6",children:[e(P,{control:p.control,name:"marker_type",render:({field:l})=>i(O,{className:"flex items-center gap-2 space-y-0",children:[i(I,{className:"inline-flex items-center gap-1",children:[t("inputs.marker_type"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.marker_type")})]}),e($,{children:e(W,{type:"number",className:"w-24",min:"0",max:"43",...l})}),e(D,{})]})}),e(P,{control:p.control,name:"marker_color",render:({field:l})=>{var C,M,G;return i(O,{className:"flex flex-row items-center gap-2 space-y-0",children:[e(I,{children:e("span",{children:t("inputs.marker_color")})}),e($,{children:e(W,{className:"h-9 w-8 cursor-pointer p-1",type:"color",...l,value:Tt((C=p.getValues().marker_color)==null?void 0:C.r,(M=p.getValues().marker_color)==null?void 0:M.g,(G=p.getValues().marker_color)==null?void 0:G.b),onChange:J=>{const{r:Q,g:X,b:Ne}=At(J.target.value);l.onChange({r:Q,g:X,b:Ne,a:255})}})}),e(D,{})]})}})]}),e("div",{className:"mt-4 w-2/3",children:e(P,{control:p.control,name:"marker_draw_distance",render:({field:l})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:t("inputs.marker_draw_distance")}),e("span",{children:p.getValues().marker_draw_distance})]}),e($,{children:e(se,{defaultValue:[p.getValues().marker_draw_distance],min:1,max:200,step:1,onValueChange:C=>{l.onChange(C[0])}})}),e(D,{})]})})}),i("div",{className:"mt-8 w-full",children:[e(I,{className:"font-bold",children:t("inputs.marker_size")}),e(rt,{form:p,isLoading:!1,dataNames:{x:{name:"marker_size.x",min:0,max:1e3,step:.1},y:{name:"marker_size.y",min:0,max:1e3,step:.1},z:{name:"marker_size.z",min:0,max:1e3,step:.1}},hideButton:!0})]}),e("div",{className:"mt-8",children:e(P,{control:p.control,name:"is_rotation_enabled",render:({field:l})=>i(Y,{children:[i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:l.value,onCheckedChange:C=>{l.onChange(C)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.marker_rotation"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.marker_rotation")})]}),e(D,{})]}),e(ae,{in:l.value,mountOnEnter:!0,timeout:100,classNames:"fade",unmountOnExit:!0,children:i("div",{className:"mt-4 grid grid-cols-3 items-center gap-10",children:[e(P,{control:p.control,name:"marker_rotation.x",render:({field:C})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:"X"}),i("span",{children:[p.getValues().marker_rotation.x,"\xB0"]})]}),e($,{children:e(se,{defaultValue:[p.getValues().marker_rotation.x],min:0,max:360,step:1,onValueChange:M=>{C.onChange(M[0])}})}),e(D,{})]})}),e(P,{control:p.control,name:"marker_rotation.y",render:({field:C})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:"Y"}),i("span",{children:[p.getValues().marker_rotation.y,"\xB0"]})]}),e($,{children:e(se,{defaultValue:[p.getValues().marker_rotation.y],min:0,max:360,step:1,onValueChange:M=>{C.onChange(M[0])}})}),e(D,{})]})}),e(P,{control:p.control,name:"marker_rotation.z",render:({field:C})=>i(O,{children:[i(I,{className:"inline-flex w-full items-center justify-between gap-1",children:[e("span",{children:"Z"}),i("span",{children:[p.getValues().marker_rotation.z,"\xB0"]})]}),e($,{children:e(se,{defaultValue:[p.getValues().marker_rotation.z],min:0,max:360,step:1,onValueChange:M=>{C.onChange(M[0])}})}),e(D,{})]})})]})})]})})})]})})]})})}),i("div",{children:[e(I,{className:"font-bold",children:t("inputs.position")}),e(rt,{form:p,isLoading:!1,dataNames:{x:{name:"position.x"},y:{name:"position.y"},z:{name:"position.z"}}})]}),e(P,{control:p.control,name:"is_enabled",render:({field:u})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:u.value,onCheckedChange:l=>{u.onChange(l)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.enabled"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.enabled")})]}),e(D,{})]})}),e(P,{control:p.control,name:"can_navigate",render:({field:u})=>i(O,{className:"flex cursor-pointer items-center space-y-0",children:[e($,{children:e(Z,{checked:u.value,onCheckedChange:l=>{u.onChange(l)}})}),i(I,{className:"inline-flex cursor-pointer items-center gap-1 pl-2",children:[t("inputs.can_navigate"),e(U,{icon:e(H,{size:"1.2rem",strokeWidth:1.5}),children:t("tooltip.can_navigate")})]}),e(D,{})]})}),e(ft,{form:p}),i(ce,{variant:"secondary",className:"absolute bottom-4 right-6 z-30 flex items-center gap-2",type:"submit",children:[e(he,{size:"1.2rem"})," ",t("inputs.submit")]})]})})})]})},Xr=({label:t,content:s,pageKey:n,query:r,accuracy:o,collapsed:a,onCollapseClick:c})=>{const m=Object.keys(s).length>1,d=Object.keys(s)[0],h=b.exports.useRef(null),{setCurrentContentPage:f,setCurrentPage:g,setSearchResults:x,pageCache:p,setIsLoading:S,setPageCache:y}=R(),A=Object.entries(s),k=(T,K)=>{y(E=>({...E,[T]:K}))},z=()=>{f(N.PAGE_CONTENT),S(!0),Be(n,p,function(T){g(T),k(n,T),S(!1),f(N.PAGE_CONTENT),x(void 0)},void 0)},v=(T,K)=>{const E=new RegExp(K,"gi"),u=T.match(E);return u?T.replace(E,`<span class="text-accent">${u[0]}</span>`):T},j=T=>{!m||(T.stopPropagation(),T.preventDefault(),c==null||c(!a))};return b.exports.useEffect(()=>{const T=h.current;T&&(a?(T.style.maxHeight=`${T.scrollHeight}px`,setTimeout(()=>{T.style.maxHeight="1.8rem"},10)):(T.style.maxHeight="1.8rem",setTimeout(()=>{T.style.maxHeight=`${T.scrollHeight}px`},10)))},[a]),i("button",{className:"group relative w-full overflow-hidden rounded-md bg-primary-light px-4 py-3 text-left transition-[filter] hover:brightness-105",onClick:z,children:[i("div",{className:"-mx-4 -my-3 flex cursor-pointer items-center justify-between gap-3 px-4 py-3",onClick:T=>j(T),children:[e("h2",{className:"overflow-x-hidden break-words font-medium",dangerouslySetInnerHTML:{__html:v(t,r)}}),m&&e("div",{className:w("-m-2 p-2 transition-transform",{"scale-y-[-1]":a}),children:e(zt,{size:"1.4rem"})})]}),A.length?e("div",{className:"mt-2 max-h-0 space-y-2 text-sm italic text-text-inactive duration-300 ease-in-out",ref:h,children:A.map(([T,K])=>K&&e("p",{className:w("transition-opacity duration-300",{"opacity-40 ":m&&a&&T!==d}),dangerouslySetInnerHTML:{__html:`"...${v(K,r)}..."`}},T))}):null,o&&e("div",{className:"pointer-events-none absolute bottom-2 right-2 mt-2 rounded-md bg-primary-dark px-1 py-0.5 text-right text-xs text-text-inactive opacity-0 transition-opacity group-hover:opacity-40",children:o})]})},eo=()=>{var h;const t=V(),{searchResults:s,setCurrentContentPage:n,setSearchResults:r}=R(),[o,a]=b.exports.useState(void 0),c=()=>{n(N.PAGE_CONTENT),r(void 0)},m=Object.entries((s==null?void 0:s.results)||{}).sort((f,g)=>g[1].accuracy-f[1].accuracy),d={...s,results:m};return i("div",{children:[e(le,{title:t("search.title"),onClick:c}),d&&e("p",{dangerouslySetInnerHTML:{__html:t("search.result_count",{count:Object.keys(d==null?void 0:d.results).length||0,query:(d==null?void 0:d.query)||""})}}),e("div",{className:"mt-4 flex flex-col gap-2",children:(h=d==null?void 0:d.results)!=null&&h.length?d.results.map(([f,g])=>e(Xr,{content:g.content||[],label:g.label,pageKey:f,query:d.query||"",accuracy:g.accuracy,collapsed:o!==f,onCollapseClick:x=>a(x?void 0:f)},f)):null})]})},to=()=>{const{currentContentPage:t,isLoading:s,setLatestUpdatedPageKey:n,playerJob:r,currentPage:o,customContent:a,categories:c}=R(),{hasPermission:m}=hn();if(s)return e(kr,{});const d=Object.values(c).find(g=>g.key===(o==null?void 0:o.category_key)),f=(()=>a!==void 0?a:m(d==null?void 0:d.attributes,r)&&m(o==null?void 0:o.attributes,r)?o==null?void 0:o.content:null)();if(t===N.PAGE_CONTENT&&f===null)return e(Xe,{});switch(b.exports.useEffect(()=>{n(void 0)},[t]),t){case N.NOT_FOUND:return e(Xe,{});case N.ADMIN_HOMEPAGE:return e(fr,{});case N.PAGE_CONTENT:return e(pr,{content:f||""});case N.CATEGORY_LIST:return e(xr,{});case N.PAGE_LIST:return e(vr,{});case N.POINT_LIST:return e(_r,{});case N.CATEGORY_ADD:return e(Ct,{});case N.CATEGORY_EDIT:return e(Ct,{});case N.PAGE_ADD:return e(St,{});case N.PAGE_EDIT:return e(St,{});case N.POINT_ADD:return e(It,{});case N.POINT_EDIT:return e(It,{});case N.SEARCH_RESULTS:return e(eo,{});default:return e(Xe,{})}},no=5,so=1e6;let nt=0;function ro(){return nt=(nt+1)%Number.MAX_VALUE,nt.toString()}const st=new Map,Pt=t=>{if(st.has(t))return;const s=setTimeout(()=>{st.delete(t),Ie({type:"REMOVE_TOAST",toastId:t})},so);st.set(t,s)},oo=(t,s)=>{switch(s.type){case"ADD_TOAST":return{...t,toasts:[s.toast,...t.toasts].slice(0,no)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(n=>n.id===s.toast.id?{...n,...s.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=s;return n?Pt(n):t.toasts.forEach(r=>{Pt(r.id)}),{...t,toasts:t.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return s.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(n=>n.id!==s.toastId)}}},Re=[];let je={toasts:[]};function Ie(t){je=oo(je,t),Re.forEach(s=>{s(je)})}function ao({...t}){const s=ro(),n=o=>Ie({type:"UPDATE_TOAST",toast:{...o,id:s}}),r=()=>Ie({type:"DISMISS_TOAST",toastId:s});return Ie({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:o=>{o||r()}}}),{id:s,dismiss:r,update:n}}function Cn(){const[t,s]=b.exports.useState(je);return b.exports.useEffect(()=>(Re.push(s),()=>{const n=Re.indexOf(s);n>-1&&Re.splice(n,1)}),[t]),{...t,toast:ao,dismiss:n=>Ie({type:"DISMISS_TOAST",toastId:n})}}const io=()=>{const{deletedItem:t,setDeletedItem:s,isLoading:n,setIsLoading:r}=R(),o=V(),a=()=>{!t||n||(r(!0),L("deleteEndpoint",{key:t==null?void 0:t.key,type:t==null?void 0:t.type}).then(m=>{r(!1),s(void 0),m===!0?L("notify",{title:o("exceptions.success"),message:o("notification.data_delete_success")}):L("notify",{title:o("exceptions.error"),message:o("notification.data_delete_error"),isError:!0})}))};return e(De,{open:!!t,onOpenChange:m=>{m||s(void 0)},children:i(ye,{children:[i(xe,{children:[e(_e,{children:o("general.are_you_sure")}),i(ke,{children:[e("strong",{className:"mt-2 block",dangerouslySetInnerHTML:{__html:o("inputs.deleting",{name:(t==null?void 0:t.label)||""})}}),(t==null?void 0:t.type)===re.CATEGORY&&e("span",{className:"mt-2 block",dangerouslySetInnerHTML:{__html:o("general.lost_pages")}}),e("span",{className:"block",dangerouslySetInnerHTML:{__html:o("general.irreversible")}})]})]}),i(ve,{className:"flex gap-2",children:[e(we,{children:o("inputs.cancel")}),e(Ce,{onClick:a,children:o("inputs.delete")})]})]})})},lo=()=>{const{teleportToPoint:t,setTeleportToPoint:s,isLoading:n,setIsLoading:r}=R(),o=V(),a=()=>{!t||n||(r(!0),L("teleport",{key:t==null?void 0:t.key}).then(m=>{r(!1),s(void 0),m===!0?L("notify",{title:o("exceptions.success"),message:o("notification.teleport_success")}):L("notify",{title:o("exceptions.error"),message:o("notification.teleport_error"),isError:!0})}))};return e(De,{open:!!t,onOpenChange:m=>{m||s(void 0)},children:i(ye,{children:[i(xe,{children:[e(_e,{children:o("general.are_you_sure")}),e(ke,{children:e("span",{className:"block",dangerouslySetInnerHTML:{__html:o("point.teleport",{point:(t==null?void 0:t.label)||""})}})})]}),i(ve,{className:"flex gap-2",children:[e(we,{children:o("inputs.cancel")}),e(Ce,{onClick:a,children:o("point.teleportNow")})]})]})})};de.OPEN;const co=()=>{const t=V(),{setIsLoading:s,setCurrentContentPage:n,setScreen:r,setCurrentPage:o,setIsAdmin:a,firstPageKey:c,pageCache:m,setCustomContent:d,setServerJobs:h,setPlayerJob:f,setIsDataFinishedLoading:g,setPageCache:x}=R(),[p,S]=b.exports.useState(!1),y=(k,z)=>{x(v=>({...v,[k]:z}))};return{handleOpen:k=>{var v;const z=k==null?void 0:k.data;switch(z&&(a(z.isAdmin),z.jobsData&&(f({name:z.jobsData.playerJob,grade:z.jobsData.playerGrade}),h(z.jobsData.serverJobs)),g(!!z.isDataFinishedLoading)),k.type){case de.OPEN:d(void 0),S(!0),r(q.GUIDEBOOK),c&&o(m[c]),n(N.PAGE_CONTENT);break;case de.OPEN_ADMIN:d(void 0),S(!0),r(q.ADMIN),n(N.CATEGORY_LIST);break;case de.OPEN_SPECIFIC_PAGE:if(d(void 0),S(!0),r(q.GUIDEBOOK),n(N.PAGE_CONTENT),s(!0),((v=k==null?void 0:k.data)==null?void 0:v.customContent)!==void 0){const j=k.data.customContent;r(q.CUSTOM_CONTENT),n(N.PAGE_CONTENT),d(j.length?j:t("page.empty_custom_content")),s(!1)}else Be(k.data.key,m,function(j){o(j),y(k.data.key,j),s(!1),n(N.PAGE_CONTENT)},void 0);break;case de.CLOSE:S(!1);break}},isOpened:p}},uo=(t,s)=>{const r=Object.fromEntries(Object.entries(t||s).sort(([,o],[,a])=>(o.order_number||0)-(a.order_number||0)));return Object.entries(r).forEach(([,o])=>{const{pages:a}=o;a&&(a==null?void 0:a.length)>1&&a.sort((c,m)=>{var f,g;const d=(f=c.order_number)!=null?f:0,h=(g=m.order_number)!=null?g:0;return d-h})}),r},mo=()=>{const{setIsLoading:t,categories:s,setCategories:n,setCurrentContentPage:r,setPageCache:o,setPageScale:a,setCurrentPage:c,setFirstPageKey:m,setCanInsertIFrame:d,setIsCopyDisabled:h,setIsJobPermissionEnabled:f,setIsDataFinishedLoading:g,setPoints:x,isDataFinishedLoading:p,setIsJobPermissionDisabledForAdmin:S,setSearchCooldown:y,currentPage:A}=R();return{setInitData:v=>{t(!0);const j=v==null?void 0:v.pageScale,T=v==null?void 0:v.isCopyDisabled,K=v==null?void 0:v.IFrameInsertIntoPage,E=v==null?void 0:v.isJobPermissionEnabled,u=v==null?void 0:v.isDataFinishedLoading,l=v==null?void 0:v.isJobPermissionDisabledForAdmin,C=v==null?void 0:v.searchCooldown;!p&&u!==void 0&&g(u),j&&a(j),T!==void 0&&h(T),K!==void 0&&d(K),E!==void 0&&f(E),l!==void 0&&S(l),C!==void 0&&y(C),v!=null&&v.firstPage?(o(G=>({...G,[v.firstPage.key]:v.firstPage})),A||c(v.firstPage),m(v.firstPage.key)):r(N.NOT_FOUND);const M=uo(v.categories,s);n(M),t(!1)},handleSetPoints:v=>{t(!0),x(v),t(!1)}}},po=t=>{const s=t!=null&&t.pageFonts?["Inter",...t==null?void 0:t.pageFonts]:["Inter"],n=s.map(o=>ee(o)),r=document.createElement("style");return r.type="text/css",r.innerHTML=`
      ${n.map((o,a)=>`
        .ql-font span[data-value="${o}"]::before, .ql-font span[data-label="${o}"]::before {
          font-family: "${s[a]}";
          content: "${s[a]}" !important;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 85%;
        }

        .ql-font-${o} {
          font-family: "${s[a]}";
        }
      `).join("")}
    `,document.head.appendChild(r),r},ho=()=>{const{toast:t}=Cn(),{userConfig:s,setUserConfig:n,currentScreen:r,setTranslations:o,pageScale:a,setLatestUpdatedPageKey:c,pageCache:m,isDataFinishedLoading:d}=R(),{handleOpen:h,isOpened:f}=co(),{setInitData:g,handleSetPoints:x}=mo(),p=b.exports.useRef("fade"),S=k=>{c(k),m[k]&&delete m[k]};oe("setInitData",g),oe("setPoints",x),oe("setTranslation",o),oe("openGuidebook",h),oe("refreshPage",S),oe("notify",k=>{t({title:k.title,description:k.message,variant:k.isError?"destructive":"default",duration:1e4})});const y=()=>{if(r===q.GUIDEBOOK)return e(ur,{});if(r===q.ADMIN)return e(mr,{});if(r===q.CUSTOM_CONTENT)return null},A=()=>{p.current="scale-down",L("close",{pageScale:a}),setTimeout(()=>{p.current="fade"},300)};return b.exports.useEffect(()=>{let k;return(async()=>{var K;const j=await Xs(),T=j.activeTheme&&((K=j.themes)==null?void 0:K[j.activeTheme])?j.activeTheme:"dark";ar(T),n(j),k=po(j)})(),()=>{document.head.removeChild(k)}},[]),s?i(Y,{children:[e(rr,{}),e(or,{className:"font-main",style:dt()?{backgroundImage:"url(https://cdn.wccftech.com/wp-content/uploads/2015/03/GTA-V-1-1030x579.jpg)",backgroundSize:"cover"}:{},children:i(yr,{delayDuration:100,children:[e(ae,{in:f,timeout:100,classNames:p.current,unmountOnExit:!0,children:e(tr,{menu:y(),contentElement:e(to,{}),onCloseButtonClick:A})}),e(io,{}),e(lo,{})]})})]}):null};const fo=Js,wn=b.exports.forwardRef(({className:t,...s},n)=>e(tn,{ref:n,className:w("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...s}));wn.displayName=tn.displayName;const go=at("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border border-slate-200 p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full dark:border-slate-800",{variants:{variant:{default:"border-primary-light bg-primary text-text dark:bg-slate-950 dark:text-slate-50",destructive:"destructive group border-error bg-error text-slate-50"}},defaultVariants:{variant:"default"}}),Nn=b.exports.forwardRef(({className:t,variant:s,...n},r)=>e(nn,{ref:r,className:w(go({variant:s}),t),...n}));Nn.displayName=nn.displayName;const bo=b.exports.forwardRef(({className:t,...s},n)=>e(sn,{ref:n,className:w("inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-slate-200 bg-transparent px-3 text-sm font-medium ring-offset-white transition-colors hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-slate-100/40 group-[.destructive]:hover:border-red-500/30 group-[.destructive]:hover:bg-red-500 group-[.destructive]:hover:text-slate-50 group-[.destructive]:focus:ring-red-500 dark:border-slate-800 dark:ring-offset-slate-950 dark:hover:bg-slate-800 dark:focus:ring-slate-300 dark:group-[.destructive]:border-slate-800/40 dark:group-[.destructive]:hover:border-red-900/30 dark:group-[.destructive]:hover:bg-red-900 dark:group-[.destructive]:hover:text-slate-50 dark:group-[.destructive]:focus:ring-red-900",t),...s}));bo.displayName=sn.displayName;const En=b.exports.forwardRef(({className:t,...s},n)=>e(rn,{ref:n,className:w("absolute right-2 top-2 rounded-md p-1 text-text transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-slate-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...s,children:e(Me,{className:"h-4 w-4"})}));En.displayName=rn.displayName;const Sn=b.exports.forwardRef(({className:t,...s},n)=>e(on,{ref:n,className:w("text-sm font-semibold",t),...s}));Sn.displayName=on.displayName;const Tn=b.exports.forwardRef(({className:t,...s},n)=>e(an,{ref:n,className:w("text-sm opacity-90",t),...s}));Tn.displayName=an.displayName;function yo(){const{toasts:t}=Cn();return i(fo,{children:[t.map(function({id:s,title:n,description:r,action:o,variant:a,...c}){return i(Nn,{variant:a,...c,className:"mb-2",children:[i("div",{className:"grid gap-1",children:[n&&i(Sn,{className:"flex items-center gap-1",children:[a==="default"&&e(he,{size:"1.2rem"}),a==="destructive"&&e(Me,{size:"1.2rem"}),n]}),r&&e(Tn,{children:r})]}),o,e(En,{})]},s)}),e(wn,{})]})}const xo={...console,assert:function(){}};window.console=xo;ts.createRoot(document.getElementById("root")).render(e(Ys,{children:i(Zs,{children:[e(ho,{}),e(yo,{})]})}));
