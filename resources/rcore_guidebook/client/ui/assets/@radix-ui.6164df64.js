import{r as t,_ as E,$ as Yo,a as Xo,o as Go,s as qo,l as jo,f as <PERSON><PERSON>,b as <PERSON>,h as Qo,c as Nt,d as <PERSON>,e as se}from"./vendor.b9589c43.js";import{r as Te,u as en,a as tn,b as on}from"./@react-dom.5e90ab5f.js";function nn(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function kt(...e){return n=>e.forEach(o=>nn(o,n))}function O(...e){return t.exports.useCallback(kt(...e),e)}const he=t.exports.forwardRef((e,n)=>{const{children:o,...r}=e,s=t.exports.Children.toArray(o),a=s.find(rn);if(a){const i=a.props.children,c=s.map(l=>l===a?t.exports.Children.count(i)>1?t.exports.Children.only(null):t.exports.isValidElement(i)?i.props.children:null:l);return t.exports.createElement(Ze,E({},r,{ref:n}),t.exports.isValidElement(i)?t.exports.cloneElement(i,void 0,c):null)}return t.exports.createElement(Ze,E({},r,{ref:n}),o)});he.displayName="Slot";const Ze=t.exports.forwardRef((e,n)=>{const{children:o,...r}=e;return t.exports.isValidElement(o)?t.exports.cloneElement(o,{...sn(r,o.props),ref:n?kt(n,o.ref):o.ref}):t.exports.Children.count(o)>1?t.exports.Children.only(null):null});Ze.displayName="SlotClone";const at=({children:e})=>t.exports.createElement(t.exports.Fragment,null,e);function rn(e){return t.exports.isValidElement(e)&&e.type===at}function sn(e,n){const o={...n};for(const r in n){const s=e[r],a=n[r];/^on[A-Z]/.test(r)?s&&a?o[r]=(...c)=>{a(...c),s(...c)}:s&&(o[r]=s):r==="style"?o[r]={...s,...a}:r==="className"&&(o[r]=[s,a].filter(Boolean).join(" "))}return{...e,...o}}const an=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],A=an.reduce((e,n)=>{const o=t.exports.forwardRef((r,s)=>{const{asChild:a,...i}=r,c=a?he:n;return t.exports.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),t.exports.createElement(c,E({},i,{ref:s}))});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{});function Ft(e,n){e&&Te.exports.flushSync(()=>e.dispatchEvent(n))}const J=Boolean(globalThis==null?void 0:globalThis.document)?t.exports.useLayoutEffect:()=>{};function cn(e,n){return t.exports.useReducer((o,r)=>{const s=n[o][r];return s!=null?s:o},e)}const re=e=>{const{present:n,children:o}=e,r=ln(n),s=typeof o=="function"?o({present:r.isPresent}):t.exports.Children.only(o),a=O(r.ref,s.ref);return typeof o=="function"||r.isPresent?t.exports.cloneElement(s,{ref:a}):null};re.displayName="Presence";function ln(e){const[n,o]=t.exports.useState(),r=t.exports.useRef({}),s=t.exports.useRef(e),a=t.exports.useRef("none"),i=e?"mounted":"unmounted",[c,l]=cn(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return t.exports.useEffect(()=>{const d=De(r.current);a.current=c==="mounted"?d:"none"},[c]),J(()=>{const d=r.current,f=s.current;if(f!==e){const b=a.current,x=De(d);e?l("MOUNT"):x==="none"||(d==null?void 0:d.display)==="none"?l("UNMOUNT"):l(f&&b!==x?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,l]),J(()=>{if(n){const d=u=>{const x=De(r.current).includes(u.animationName);u.target===n&&x&&Te.exports.flushSync(()=>l("ANIMATION_END"))},f=u=>{u.target===n&&(a.current=De(r.current))};return n.addEventListener("animationstart",f),n.addEventListener("animationcancel",d),n.addEventListener("animationend",d),()=>{n.removeEventListener("animationstart",f),n.removeEventListener("animationcancel",d),n.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[n,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.exports.useCallback(d=>{d&&(r.current=getComputedStyle(d)),o(d)},[])}}function De(e){return(e==null?void 0:e.animationName)||"none"}function dn(e,n){const o=t.exports.createContext(n);function r(a){const{children:i,...c}=a,l=t.exports.useMemo(()=>c,Object.values(c));return t.exports.createElement(o.Provider,{value:l},i)}function s(a){const i=t.exports.useContext(o);if(i)return i;if(n!==void 0)return n;throw new Error(`\`${a}\` must be used within \`${e}\``)}return r.displayName=e+"Provider",[r,s]}function ae(e,n=[]){let o=[];function r(a,i){const c=t.exports.createContext(i),l=o.length;o=[...o,i];function d(u){const{scope:b,children:x,...v}=u,p=(b==null?void 0:b[e][l])||c,$=t.exports.useMemo(()=>v,Object.values(v));return t.exports.createElement(p.Provider,{value:$},x)}function f(u,b){const x=(b==null?void 0:b[e][l])||c,v=t.exports.useContext(x);if(v)return v;if(i!==void 0)return i;throw new Error(`\`${u}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,f]}const s=()=>{const a=o.map(i=>t.exports.createContext(i));return function(c){const l=(c==null?void 0:c[e])||a;return t.exports.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return s.scopeName=e,[r,un(s,...n)]}function un(...e){const n=e[0];if(e.length===1)return n;const o=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(a){const i=r.reduce((c,{useScope:l,scopeName:d})=>{const u=l(a)[`__scope${d}`];return{...c,...u}},{});return t.exports.useMemo(()=>({[`__scope${n.scopeName}`]:i}),[i])}};return o.scopeName=n.scopeName,o}function z(e){const n=t.exports.useRef(e);return t.exports.useEffect(()=>{n.current=e}),t.exports.useMemo(()=>(...o)=>{var r;return(r=n.current)===null||r===void 0?void 0:r.call(n,...o)},[])}const fn=t.exports.createContext(void 0);function ct(e){const n=t.exports.useContext(fn);return e||n||"ltr"}function ye(e,[n,o]){return Math.min(o,Math.max(n,e))}function y(e,n,{checkForDefaultPrevented:o=!0}={}){return function(s){if(e==null||e(s),o===!1||!s.defaultPrevented)return n==null?void 0:n(s)}}function pn(e,n){return t.exports.useReducer((o,r)=>{const s=n[o][r];return s!=null?s:o},e)}const Ht="ScrollArea",[Vt,_a]=ae(Ht),[$n,te]=Vt(Ht),xn=t.exports.forwardRef((e,n)=>{const{__scopeScrollArea:o,type:r="hover",dir:s,scrollHideDelay:a=600,...i}=e,[c,l]=t.exports.useState(null),[d,f]=t.exports.useState(null),[u,b]=t.exports.useState(null),[x,v]=t.exports.useState(null),[p,$]=t.exports.useState(null),[g,m]=t.exports.useState(0),[h,w]=t.exports.useState(0),[C,I]=t.exports.useState(!1),[P,T]=t.exports.useState(!1),_=O(n,N=>l(N)),M=ct(s);return t.exports.createElement($n,{scope:o,type:r,dir:M,scrollHideDelay:a,scrollArea:c,viewport:d,onViewportChange:f,content:u,onContentChange:b,scrollbarX:x,onScrollbarXChange:v,scrollbarXEnabled:C,onScrollbarXEnabledChange:I,scrollbarY:p,onScrollbarYChange:$,scrollbarYEnabled:P,onScrollbarYEnabledChange:T,onCornerWidthChange:m,onCornerHeightChange:w},t.exports.createElement(A.div,E({dir:M},i,{ref:_,style:{position:"relative",["--radix-scroll-area-corner-width"]:g+"px",["--radix-scroll-area-corner-height"]:h+"px",...e.style}})))}),mn="ScrollAreaViewport",bn=t.exports.forwardRef((e,n)=>{const{__scopeScrollArea:o,children:r,...s}=e,a=te(mn,o),i=t.exports.useRef(null),c=O(n,i,a.onViewportChange);return t.exports.createElement(t.exports.Fragment,null,t.exports.createElement("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"}}),t.exports.createElement(A.div,E({"data-radix-scroll-area-viewport":""},s,{ref:c,style:{overflowX:a.scrollbarXEnabled?"scroll":"hidden",overflowY:a.scrollbarYEnabled?"scroll":"hidden",...e.style}}),t.exports.createElement("div",{ref:a.onContentChange,style:{minWidth:"100%",display:"table"}},r)))}),ue="ScrollAreaScrollbar",Ra=t.exports.forwardRef((e,n)=>{const{forceMount:o,...r}=e,s=te(ue,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=s,c=e.orientation==="horizontal";return t.exports.useEffect(()=>(c?a(!0):i(!0),()=>{c?a(!1):i(!1)}),[c,a,i]),s.type==="hover"?t.exports.createElement(vn,E({},r,{ref:n,forceMount:o})):s.type==="scroll"?t.exports.createElement(hn,E({},r,{ref:n,forceMount:o})):s.type==="auto"?t.exports.createElement(Kt,E({},r,{ref:n,forceMount:o})):s.type==="always"?t.exports.createElement(it,E({},r,{ref:n})):null}),vn=t.exports.forwardRef((e,n)=>{const{forceMount:o,...r}=e,s=te(ue,e.__scopeScrollArea),[a,i]=t.exports.useState(!1);return t.exports.useEffect(()=>{const c=s.scrollArea;let l=0;if(c){const d=()=>{window.clearTimeout(l),i(!0)},f=()=>{l=window.setTimeout(()=>i(!1),s.scrollHideDelay)};return c.addEventListener("pointerenter",d),c.addEventListener("pointerleave",f),()=>{window.clearTimeout(l),c.removeEventListener("pointerenter",d),c.removeEventListener("pointerleave",f)}}},[s.scrollArea,s.scrollHideDelay]),t.exports.createElement(re,{present:o||a},t.exports.createElement(Kt,E({"data-state":a?"visible":"hidden"},r,{ref:n})))}),hn=t.exports.forwardRef((e,n)=>{const{forceMount:o,...r}=e,s=te(ue,e.__scopeScrollArea),a=e.orientation==="horizontal",i=Ne(()=>l("SCROLL_END"),100),[c,l]=pn("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return t.exports.useEffect(()=>{if(c==="idle"){const d=window.setTimeout(()=>l("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(d)}},[c,s.scrollHideDelay,l]),t.exports.useEffect(()=>{const d=s.viewport,f=a?"scrollLeft":"scrollTop";if(d){let u=d[f];const b=()=>{const x=d[f];u!==x&&(l("SCROLL"),i()),u=x};return d.addEventListener("scroll",b),()=>d.removeEventListener("scroll",b)}},[s.viewport,a,l,i]),t.exports.createElement(re,{present:o||c!=="hidden"},t.exports.createElement(it,E({"data-state":c==="hidden"?"hidden":"visible"},r,{ref:n,onPointerEnter:y(e.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:y(e.onPointerLeave,()=>l("POINTER_LEAVE"))})))}),Kt=t.exports.forwardRef((e,n)=>{const o=te(ue,e.__scopeScrollArea),{forceMount:r,...s}=e,[a,i]=t.exports.useState(!1),c=e.orientation==="horizontal",l=Ne(()=>{if(o.viewport){const d=o.viewport.offsetWidth<o.viewport.scrollWidth,f=o.viewport.offsetHeight<o.viewport.scrollHeight;i(c?d:f)}},10);return ge(o.viewport,l),ge(o.content,l),t.exports.createElement(re,{present:r||a},t.exports.createElement(it,E({"data-state":a?"visible":"hidden"},s,{ref:n})))}),it=t.exports.forwardRef((e,n)=>{const{orientation:o="vertical",...r}=e,s=te(ue,e.__scopeScrollArea),a=t.exports.useRef(null),i=t.exports.useRef(0),[c,l]=t.exports.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=zt(c.viewport,c.content),f={...r,sizes:c,onSizesChange:l,hasThumb:Boolean(d>0&&d<1),onThumbChange:b=>a.current=b,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:b=>i.current=b};function u(b,x){return Pn(b,i.current,c,x)}return o==="horizontal"?t.exports.createElement(gn,E({},f,{ref:n,onThumbPositionChange:()=>{if(s.viewport&&a.current){const b=s.viewport.scrollLeft,x=gt(b,c,s.dir);a.current.style.transform=`translate3d(${x}px, 0, 0)`}},onWheelScroll:b=>{s.viewport&&(s.viewport.scrollLeft=b)},onDragScroll:b=>{s.viewport&&(s.viewport.scrollLeft=u(b,s.dir))}})):o==="vertical"?t.exports.createElement(En,E({},f,{ref:n,onThumbPositionChange:()=>{if(s.viewport&&a.current){const b=s.viewport.scrollTop,x=gt(b,c);a.current.style.transform=`translate3d(0, ${x}px, 0)`}},onWheelScroll:b=>{s.viewport&&(s.viewport.scrollTop=b)},onDragScroll:b=>{s.viewport&&(s.viewport.scrollTop=u(b))}})):null}),gn=t.exports.forwardRef((e,n)=>{const{sizes:o,onSizesChange:r,...s}=e,a=te(ue,e.__scopeScrollArea),[i,c]=t.exports.useState(),l=t.exports.useRef(null),d=O(n,l,a.onScrollbarXChange);return t.exports.useEffect(()=>{l.current&&c(getComputedStyle(l.current))},[l]),t.exports.createElement(Bt,E({"data-orientation":"horizontal"},s,{ref:d,sizes:o,style:{bottom:0,left:a.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:a.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,["--radix-scroll-area-thumb-width"]:Me(o)+"px",...e.style},onThumbPointerDown:f=>e.onThumbPointerDown(f.x),onDragScroll:f=>e.onDragScroll(f.x),onWheelScroll:(f,u)=>{if(a.viewport){const b=a.viewport.scrollLeft+f.deltaX;e.onWheelScroll(b),Xt(b,u)&&f.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&r({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:Ie(i.paddingLeft),paddingEnd:Ie(i.paddingRight)}})}}))}),En=t.exports.forwardRef((e,n)=>{const{sizes:o,onSizesChange:r,...s}=e,a=te(ue,e.__scopeScrollArea),[i,c]=t.exports.useState(),l=t.exports.useRef(null),d=O(n,l,a.onScrollbarYChange);return t.exports.useEffect(()=>{l.current&&c(getComputedStyle(l.current))},[l]),t.exports.createElement(Bt,E({"data-orientation":"vertical"},s,{ref:d,sizes:o,style:{top:0,right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)",["--radix-scroll-area-thumb-height"]:Me(o)+"px",...e.style},onThumbPointerDown:f=>e.onThumbPointerDown(f.y),onDragScroll:f=>e.onDragScroll(f.y),onWheelScroll:(f,u)=>{if(a.viewport){const b=a.viewport.scrollTop+f.deltaY;e.onWheelScroll(b),Xt(b,u)&&f.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&r({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:Ie(i.paddingTop),paddingEnd:Ie(i.paddingBottom)}})}}))}),[wn,Wt]=Vt(ue),Bt=t.exports.forwardRef((e,n)=>{const{__scopeScrollArea:o,sizes:r,hasThumb:s,onThumbChange:a,onThumbPointerUp:i,onThumbPointerDown:c,onThumbPositionChange:l,onDragScroll:d,onWheelScroll:f,onResize:u,...b}=e,x=te(ue,o),[v,p]=t.exports.useState(null),$=O(n,_=>p(_)),g=t.exports.useRef(null),m=t.exports.useRef(""),h=x.viewport,w=r.content-r.viewport,C=z(f),I=z(l),P=Ne(u,10);function T(_){if(g.current){const M=_.clientX-g.current.left,N=_.clientY-g.current.top;d({x:M,y:N})}}return t.exports.useEffect(()=>{const _=M=>{const N=M.target;(v==null?void 0:v.contains(N))&&C(M,w)};return document.addEventListener("wheel",_,{passive:!1}),()=>document.removeEventListener("wheel",_,{passive:!1})},[h,v,w,C]),t.exports.useEffect(I,[r,I]),ge(v,P),ge(x.content,P),t.exports.createElement(wn,{scope:o,scrollbar:v,hasThumb:s,onThumbChange:z(a),onThumbPointerUp:z(i),onThumbPositionChange:I,onThumbPointerDown:z(c)},t.exports.createElement(A.div,E({},b,{ref:$,style:{position:"absolute",...b.style},onPointerDown:y(e.onPointerDown,_=>{_.button===0&&(_.target.setPointerCapture(_.pointerId),g.current=v.getBoundingClientRect(),m.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),T(_))}),onPointerMove:y(e.onPointerMove,T),onPointerUp:y(e.onPointerUp,_=>{const M=_.target;M.hasPointerCapture(_.pointerId)&&M.releasePointerCapture(_.pointerId),document.body.style.webkitUserSelect=m.current,x.viewport&&(x.viewport.style.scrollBehavior=""),g.current=null})})))}),Je="ScrollAreaThumb",Da=t.exports.forwardRef((e,n)=>{const{forceMount:o,...r}=e,s=Wt(Je,e.__scopeScrollArea);return t.exports.createElement(re,{present:o||s.hasThumb},t.exports.createElement(Sn,E({ref:n},r)))}),Sn=t.exports.forwardRef((e,n)=>{const{__scopeScrollArea:o,style:r,...s}=e,a=te(Je,o),i=Wt(Je,o),{onThumbPositionChange:c}=i,l=O(n,u=>i.onThumbChange(u)),d=t.exports.useRef(),f=Ne(()=>{d.current&&(d.current(),d.current=void 0)},100);return t.exports.useEffect(()=>{const u=a.viewport;if(u){const b=()=>{if(f(),!d.current){const x=Tn(u,c);d.current=x,c()}};return c(),u.addEventListener("scroll",b),()=>u.removeEventListener("scroll",b)}},[a.viewport,f,c]),t.exports.createElement(A.div,E({"data-state":i.hasThumb?"visible":"hidden"},s,{ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:y(e.onPointerDownCapture,u=>{const x=u.target.getBoundingClientRect(),v=u.clientX-x.left,p=u.clientY-x.top;i.onThumbPointerDown({x:v,y:p})}),onPointerUp:y(e.onPointerUp,i.onThumbPointerUp)}))}),Ut="ScrollAreaCorner",Cn=t.exports.forwardRef((e,n)=>{const o=te(Ut,e.__scopeScrollArea),r=Boolean(o.scrollbarX&&o.scrollbarY);return o.type!=="scroll"&&r?t.exports.createElement(yn,E({},e,{ref:n})):null}),yn=t.exports.forwardRef((e,n)=>{const{__scopeScrollArea:o,...r}=e,s=te(Ut,o),[a,i]=t.exports.useState(0),[c,l]=t.exports.useState(0),d=Boolean(a&&c);return ge(s.scrollbarX,()=>{var f;const u=((f=s.scrollbarX)===null||f===void 0?void 0:f.offsetHeight)||0;s.onCornerHeightChange(u),l(u)}),ge(s.scrollbarY,()=>{var f;const u=((f=s.scrollbarY)===null||f===void 0?void 0:f.offsetWidth)||0;s.onCornerWidthChange(u),i(u)}),d?t.exports.createElement(A.div,E({},r,{ref:n,style:{width:a,height:c,position:"absolute",right:s.dir==="ltr"?0:void 0,left:s.dir==="rtl"?0:void 0,bottom:0,...e.style}})):null});function Ie(e){return e?parseInt(e,10):0}function zt(e,n){const o=e/n;return isNaN(o)?0:o}function Me(e){const n=zt(e.viewport,e.content),o=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-o)*n;return Math.max(r,18)}function Pn(e,n,o,r="ltr"){const s=Me(o),a=s/2,i=n||a,c=s-i,l=o.scrollbar.paddingStart+i,d=o.scrollbar.size-o.scrollbar.paddingEnd-c,f=o.content-o.viewport,u=r==="ltr"?[0,f]:[f*-1,0];return Yt([l,d],u)(e)}function gt(e,n,o="ltr"){const r=Me(n),s=n.scrollbar.paddingStart+n.scrollbar.paddingEnd,a=n.scrollbar.size-s,i=n.content-n.viewport,c=a-r,l=o==="ltr"?[0,i]:[i*-1,0],d=ye(e,l);return Yt([0,i],[0,c])(d)}function Yt(e,n){return o=>{if(e[0]===e[1]||n[0]===n[1])return n[0];const r=(n[1]-n[0])/(e[1]-e[0]);return n[0]+r*(o-e[0])}}function Xt(e,n){return e>0&&e<n}const Tn=(e,n=()=>{})=>{let o={left:e.scrollLeft,top:e.scrollTop},r=0;return function s(){const a={left:e.scrollLeft,top:e.scrollTop},i=o.left!==a.left,c=o.top!==a.top;(i||c)&&n(),o=a,r=window.requestAnimationFrame(s)}(),()=>window.cancelAnimationFrame(r)};function Ne(e,n){const o=z(e),r=t.exports.useRef(0);return t.exports.useEffect(()=>()=>window.clearTimeout(r.current),[]),t.exports.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(o,n)},[o,n])}function ge(e,n){const o=z(n);J(()=>{let r=0;if(e){const s=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(o)});return s.observe(e),()=>{window.cancelAnimationFrame(r),s.unobserve(e)}}},[e,o])}const Aa=xn,Oa=bn,Ia=Cn;function _n(e,n=globalThis==null?void 0:globalThis.document){const o=z(e);t.exports.useEffect(()=>{const r=s=>{s.key==="Escape"&&o(s)};return n.addEventListener("keydown",r),()=>n.removeEventListener("keydown",r)},[o,n])}const Qe="dismissableLayer.update",Rn="dismissableLayer.pointerDownOutside",Dn="dismissableLayer.focusOutside";let Et;const Gt=t.exports.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Le=t.exports.forwardRef((e,n)=>{var o;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:i,onInteractOutside:c,onDismiss:l,...d}=e,f=t.exports.useContext(Gt),[u,b]=t.exports.useState(null),x=(o=u==null?void 0:u.ownerDocument)!==null&&o!==void 0?o:globalThis==null?void 0:globalThis.document,[,v]=t.exports.useState({}),p=O(n,T=>b(T)),$=Array.from(f.layers),[g]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),m=$.indexOf(g),h=u?$.indexOf(u):-1,w=f.layersWithOutsidePointerEventsDisabled.size>0,C=h>=m,I=On(T=>{const _=T.target,M=[...f.branches].some(N=>N.contains(_));!C||M||(a==null||a(T),c==null||c(T),T.defaultPrevented||l==null||l())},x),P=In(T=>{const _=T.target;[...f.branches].some(N=>N.contains(_))||(i==null||i(T),c==null||c(T),T.defaultPrevented||l==null||l())},x);return _n(T=>{h===f.layers.size-1&&(s==null||s(T),!T.defaultPrevented&&l&&(T.preventDefault(),l()))},x),t.exports.useEffect(()=>{if(!!u)return r&&(f.layersWithOutsidePointerEventsDisabled.size===0&&(Et=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(u)),f.layers.add(u),wt(),()=>{r&&f.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=Et)}},[u,x,r,f]),t.exports.useEffect(()=>()=>{!u||(f.layers.delete(u),f.layersWithOutsidePointerEventsDisabled.delete(u),wt())},[u,f]),t.exports.useEffect(()=>{const T=()=>v({});return document.addEventListener(Qe,T),()=>document.removeEventListener(Qe,T)},[]),t.exports.createElement(A.div,E({},d,{ref:p,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:y(e.onFocusCapture,P.onFocusCapture),onBlurCapture:y(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:y(e.onPointerDownCapture,I.onPointerDownCapture)}))}),An=t.exports.forwardRef((e,n)=>{const o=t.exports.useContext(Gt),r=t.exports.useRef(null),s=O(n,r);return t.exports.useEffect(()=>{const a=r.current;if(a)return o.branches.add(a),()=>{o.branches.delete(a)}},[o.branches]),t.exports.createElement(A.div,E({},e,{ref:s}))});function On(e,n=globalThis==null?void 0:globalThis.document){const o=z(e),r=t.exports.useRef(!1),s=t.exports.useRef(()=>{});return t.exports.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let d=function(){qt(Rn,o,l,{discrete:!0})};const l={originalEvent:c};c.pointerType==="touch"?(n.removeEventListener("click",s.current),s.current=d,n.addEventListener("click",s.current,{once:!0})):d()}else n.removeEventListener("click",s.current);r.current=!1},i=window.setTimeout(()=>{n.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),n.removeEventListener("pointerdown",a),n.removeEventListener("click",s.current)}},[n,o]),{onPointerDownCapture:()=>r.current=!0}}function In(e,n=globalThis==null?void 0:globalThis.document){const o=z(e),r=t.exports.useRef(!1);return t.exports.useEffect(()=>{const s=a=>{a.target&&!r.current&&qt(Dn,o,{originalEvent:a},{discrete:!1})};return n.addEventListener("focusin",s),()=>n.removeEventListener("focusin",s)},[n,o]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function wt(){const e=new CustomEvent(Qe);document.dispatchEvent(e)}function qt(e,n,o,{discrete:r}){const s=o.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:o});n&&s.addEventListener(e,n,{once:!0}),r?Ft(s,a):s.dispatchEvent(a)}const Mn=Le,Nn=An,Ln=Yo["useId".toString()]||(()=>{});let kn=0;function ve(e){const[n,o]=t.exports.useState(Ln());return J(()=>{e||o(r=>r!=null?r:String(kn++))},[e]),e||(n?`radix-${n}`:"")}function lt(e){const[n,o]=t.exports.useState(void 0);return J(()=>{if(e){o({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const a=s[0];let i,c;if("borderBoxSize"in a){const l=a.borderBoxSize,d=Array.isArray(l)?l[0]:l;i=d.inlineSize,c=d.blockSize}else i=e.offsetWidth,c=e.offsetHeight;o({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else o(void 0)},[e]),n}const jt="Popper",[Zt,ke]=ae(jt),[Fn,Jt]=Zt(jt),Hn=e=>{const{__scopePopper:n,children:o}=e,[r,s]=t.exports.useState(null);return t.exports.createElement(Fn,{scope:n,anchor:r,onAnchorChange:s},o)},Vn="PopperAnchor",Kn=t.exports.forwardRef((e,n)=>{const{__scopePopper:o,virtualRef:r,...s}=e,a=Jt(Vn,o),i=t.exports.useRef(null),c=O(n,i);return t.exports.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:t.exports.createElement(A.div,E({},s,{ref:c}))}),Qt="PopperContent",[Wn,Ma]=Zt(Qt),Bn=t.exports.forwardRef((e,n)=>{var o,r,s,a,i,c,l,d;const{__scopePopper:f,side:u="bottom",sideOffset:b=0,align:x="center",alignOffset:v=0,arrowPadding:p=0,avoidCollisions:$=!0,collisionBoundary:g=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:w=!1,updatePositionStrategy:C="optimized",onPlaced:I,...P}=e,T=Jt(Qt,f),[_,M]=t.exports.useState(null),N=O(n,le=>M(le)),[H,B]=t.exports.useState(null),S=lt(H),D=(o=S==null?void 0:S.width)!==null&&o!==void 0?o:0,F=(r=S==null?void 0:S.height)!==null&&r!==void 0?r:0,U=u+(x!=="center"?"-"+x:""),X=typeof m=="number"?m:{top:0,right:0,bottom:0,left:0,...m},W=Array.isArray(g)?g:[g],q=W.length>0,Y={padding:X,boundary:W.filter(Un),altBoundary:q},{refs:Q,floatingStyles:oe,placement:ne,isPositioned:ie,middlewareData:ee}=en({strategy:"fixed",placement:U,whileElementsMounted:(...le)=>Xo(...le,{animationFrame:C==="always"}),elements:{reference:T.anchor},middleware:[Go({mainAxis:b+F,alignmentAxis:v}),$&&qo({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?jo():void 0,...Y}),$&&Zo({...Y}),Jo({...Y,apply:({elements:le,rects:be,availableWidth:Ce,availableHeight:Bo})=>{const{width:Uo,height:zo}=be.reference,Re=le.floating.style;Re.setProperty("--radix-popper-available-width",`${Ce}px`),Re.setProperty("--radix-popper-available-height",`${Bo}px`),Re.setProperty("--radix-popper-anchor-width",`${Uo}px`),Re.setProperty("--radix-popper-anchor-height",`${zo}px`)}}),H&&tn({element:H,padding:p}),zn({arrowWidth:D,arrowHeight:F}),w&&Qo({strategy:"referenceHidden",...Y})]}),[R,V]=eo(ne),G=z(I);J(()=>{ie&&(G==null||G())},[ie,G]);const K=(s=ee.arrow)===null||s===void 0?void 0:s.x,L=(a=ee.arrow)===null||a===void 0?void 0:a.y,k=((i=ee.arrow)===null||i===void 0?void 0:i.centerOffset)!==0,[j,Z]=t.exports.useState();return J(()=>{_&&Z(window.getComputedStyle(_).zIndex)},[_]),t.exports.createElement("div",{ref:Q.setFloating,"data-radix-popper-content-wrapper":"",style:{...oe,transform:ie?oe.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:j,["--radix-popper-transform-origin"]:[(c=ee.transformOrigin)===null||c===void 0?void 0:c.x,(l=ee.transformOrigin)===null||l===void 0?void 0:l.y].join(" ")},dir:e.dir},t.exports.createElement(Wn,{scope:f,placedSide:R,onArrowChange:B,arrowX:K,arrowY:L,shouldHideArrow:k},t.exports.createElement(A.div,E({"data-side":R,"data-align":V},P,{ref:N,style:{...P.style,animation:ie?void 0:"none",opacity:(d=ee.hide)!==null&&d!==void 0&&d.referenceHidden?0:void 0}}))))});function Un(e){return e!==null}const zn=e=>({name:"transformOrigin",options:e,fn(n){var o,r,s,a,i;const{placement:c,rects:l,middlewareData:d}=n,u=((o=d.arrow)===null||o===void 0?void 0:o.centerOffset)!==0,b=u?0:e.arrowWidth,x=u?0:e.arrowHeight,[v,p]=eo(c),$={start:"0%",center:"50%",end:"100%"}[p],g=((r=(s=d.arrow)===null||s===void 0?void 0:s.x)!==null&&r!==void 0?r:0)+b/2,m=((a=(i=d.arrow)===null||i===void 0?void 0:i.y)!==null&&a!==void 0?a:0)+x/2;let h="",w="";return v==="bottom"?(h=u?$:`${g}px`,w=`${-x}px`):v==="top"?(h=u?$:`${g}px`,w=`${l.floating.height+x}px`):v==="right"?(h=`${-x}px`,w=u?$:`${m}px`):v==="left"&&(h=`${l.floating.width+x}px`,w=u?$:`${m}px`),{data:{x:h,y:w}}}});function eo(e){const[n,o="center"]=e.split("-");return[n,o]}const to=Hn,oo=Kn,no=Bn,dt=t.exports.forwardRef((e,n)=>{var o;const{container:r=globalThis==null||(o=globalThis.document)===null||o===void 0?void 0:o.body,...s}=e;return r?on.createPortal(t.exports.createElement(A.div,E({},s,{ref:n})),r):null});function xe({prop:e,defaultProp:n,onChange:o=()=>{}}){const[r,s]=Yn({defaultProp:n,onChange:o}),a=e!==void 0,i=a?e:r,c=z(o),l=t.exports.useCallback(d=>{if(a){const u=typeof d=="function"?d(e):d;u!==e&&c(u)}else s(d)},[a,e,s,c]);return[i,l]}function Yn({defaultProp:e,onChange:n}){const o=t.exports.useState(e),[r]=o,s=t.exports.useRef(r),a=z(n);return t.exports.useEffect(()=>{s.current!==r&&(a(r),s.current=r)},[r,s,a]),o}const Fe=t.exports.forwardRef((e,n)=>t.exports.createElement(A.span,E({},e,{ref:n,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}))),Xn=Fe,[He,Na]=ae("Tooltip",[ke]),ut=ke(),Gn="TooltipProvider",qn=700,et="tooltip.open",[jn,ft]=He(Gn),Zn=e=>{const{__scopeTooltip:n,delayDuration:o=qn,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:a}=e,[i,c]=t.exports.useState(!0),l=t.exports.useRef(!1),d=t.exports.useRef(0);return t.exports.useEffect(()=>{const f=d.current;return()=>window.clearTimeout(f)},[]),t.exports.createElement(jn,{scope:n,isOpenDelayed:i,delayDuration:o,onOpen:t.exports.useCallback(()=>{window.clearTimeout(d.current),c(!1)},[]),onClose:t.exports.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:t.exports.useCallback(f=>{l.current=f},[]),disableHoverableContent:s},a)},pt="Tooltip",[Jn,Ve]=He(pt),Qn=e=>{const{__scopeTooltip:n,children:o,open:r,defaultOpen:s=!1,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,l=ft(pt,e.__scopeTooltip),d=ut(n),[f,u]=t.exports.useState(null),b=ve(),x=t.exports.useRef(0),v=i!=null?i:l.disableHoverableContent,p=c!=null?c:l.delayDuration,$=t.exports.useRef(!1),[g=!1,m]=xe({prop:r,defaultProp:s,onChange:P=>{P?(l.onOpen(),document.dispatchEvent(new CustomEvent(et))):l.onClose(),a==null||a(P)}}),h=t.exports.useMemo(()=>g?$.current?"delayed-open":"instant-open":"closed",[g]),w=t.exports.useCallback(()=>{window.clearTimeout(x.current),$.current=!1,m(!0)},[m]),C=t.exports.useCallback(()=>{window.clearTimeout(x.current),m(!1)},[m]),I=t.exports.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{$.current=!0,m(!0)},p)},[p,m]);return t.exports.useEffect(()=>()=>window.clearTimeout(x.current),[]),t.exports.createElement(to,d,t.exports.createElement(Jn,{scope:n,contentId:b,open:g,stateAttribute:h,trigger:f,onTriggerChange:u,onTriggerEnter:t.exports.useCallback(()=>{l.isOpenDelayed?I():w()},[l.isOpenDelayed,I,w]),onTriggerLeave:t.exports.useCallback(()=>{v?C():window.clearTimeout(x.current)},[C,v]),onOpen:w,onClose:C,disableHoverableContent:v},o))},St="TooltipTrigger",er=t.exports.forwardRef((e,n)=>{const{__scopeTooltip:o,...r}=e,s=Ve(St,o),a=ft(St,o),i=ut(o),c=t.exports.useRef(null),l=O(n,c,s.onTriggerChange),d=t.exports.useRef(!1),f=t.exports.useRef(!1),u=t.exports.useCallback(()=>d.current=!1,[]);return t.exports.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),t.exports.createElement(oo,E({asChild:!0},i),t.exports.createElement(A.button,E({"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute},r,{ref:l,onPointerMove:y(e.onPointerMove,b=>{b.pointerType!=="touch"&&!f.current&&!a.isPointerInTransitRef.current&&(s.onTriggerEnter(),f.current=!0)}),onPointerLeave:y(e.onPointerLeave,()=>{s.onTriggerLeave(),f.current=!1}),onPointerDown:y(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:y(e.onFocus,()=>{d.current||s.onOpen()}),onBlur:y(e.onBlur,s.onClose),onClick:y(e.onClick,s.onClose)})))}),tr="TooltipPortal",[La,or]=He(tr,{forceMount:void 0}),Pe="TooltipContent",nr=t.exports.forwardRef((e,n)=>{const o=or(Pe,e.__scopeTooltip),{forceMount:r=o.forceMount,side:s="top",...a}=e,i=Ve(Pe,e.__scopeTooltip);return t.exports.createElement(re,{present:r||i.open},i.disableHoverableContent?t.exports.createElement(ro,E({side:s},a,{ref:n})):t.exports.createElement(rr,E({side:s},a,{ref:n})))}),rr=t.exports.forwardRef((e,n)=>{const o=Ve(Pe,e.__scopeTooltip),r=ft(Pe,e.__scopeTooltip),s=t.exports.useRef(null),a=O(n,s),[i,c]=t.exports.useState(null),{trigger:l,onClose:d}=o,f=s.current,{onPointerInTransitChange:u}=r,b=t.exports.useCallback(()=>{c(null),u(!1)},[u]),x=t.exports.useCallback((v,p)=>{const $=v.currentTarget,g={x:v.clientX,y:v.clientY},m=ar(g,$.getBoundingClientRect()),h=cr(g,m),w=ir(p.getBoundingClientRect()),C=dr([...h,...w]);c(C),u(!0)},[u]);return t.exports.useEffect(()=>()=>b(),[b]),t.exports.useEffect(()=>{if(l&&f){const v=$=>x($,f),p=$=>x($,l);return l.addEventListener("pointerleave",v),f.addEventListener("pointerleave",p),()=>{l.removeEventListener("pointerleave",v),f.removeEventListener("pointerleave",p)}}},[l,f,x,b]),t.exports.useEffect(()=>{if(i){const v=p=>{const $=p.target,g={x:p.clientX,y:p.clientY},m=(l==null?void 0:l.contains($))||(f==null?void 0:f.contains($)),h=!lr(g,i);m?b():h&&(b(),d())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,f,i,d,b]),t.exports.createElement(ro,E({},e,{ref:a}))}),[sr,ka]=He(pt,{isInside:!1}),ro=t.exports.forwardRef((e,n)=>{const{__scopeTooltip:o,children:r,"aria-label":s,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,l=Ve(Pe,o),d=ut(o),{onClose:f}=l;return t.exports.useEffect(()=>(document.addEventListener(et,f),()=>document.removeEventListener(et,f)),[f]),t.exports.useEffect(()=>{if(l.trigger){const u=b=>{const x=b.target;x!=null&&x.contains(l.trigger)&&f()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[l.trigger,f]),t.exports.createElement(Le,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:u=>u.preventDefault(),onDismiss:f},t.exports.createElement(no,E({"data-state":l.stateAttribute},d,c,{ref:n,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}}),t.exports.createElement(at,null,r),t.exports.createElement(sr,{scope:o,isInside:!0},t.exports.createElement(Xn,{id:l.contentId,role:"tooltip"},s||r))))});function ar(e,n){const o=Math.abs(n.top-e.y),r=Math.abs(n.bottom-e.y),s=Math.abs(n.right-e.x),a=Math.abs(n.left-e.x);switch(Math.min(o,r,s,a)){case a:return"left";case s:return"right";case o:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function cr(e,n,o=5){const r=[];switch(n){case"top":r.push({x:e.x-o,y:e.y+o},{x:e.x+o,y:e.y+o});break;case"bottom":r.push({x:e.x-o,y:e.y-o},{x:e.x+o,y:e.y-o});break;case"left":r.push({x:e.x+o,y:e.y-o},{x:e.x+o,y:e.y+o});break;case"right":r.push({x:e.x-o,y:e.y-o},{x:e.x-o,y:e.y+o});break}return r}function ir(e){const{top:n,right:o,bottom:r,left:s}=e;return[{x:s,y:n},{x:o,y:n},{x:o,y:r},{x:s,y:r}]}function lr(e,n){const{x:o,y:r}=e;let s=!1;for(let a=0,i=n.length-1;a<n.length;i=a++){const c=n[a].x,l=n[a].y,d=n[i].x,f=n[i].y;l>r!=f>r&&o<(d-c)*(r-l)/(f-l)+c&&(s=!s)}return s}function dr(e){const n=e.slice();return n.sort((o,r)=>o.x<r.x?-1:o.x>r.x?1:o.y<r.y?-1:o.y>r.y?1:0),ur(n)}function ur(e){if(e.length<=1)return e.slice();const n=[];for(let r=0;r<e.length;r++){const s=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))n.pop();else break}n.push(s)}n.pop();const o=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;o.length>=2;){const a=o[o.length-1],i=o[o.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))o.pop();else break}o.push(s)}return o.pop(),n.length===1&&o.length===1&&n[0].x===o[0].x&&n[0].y===o[0].y?n:n.concat(o)}const Fa=Zn,Ha=Qn,Va=er,Ka=nr,fr=t.exports.forwardRef((e,n)=>t.exports.createElement(A.label,E({},e,{ref:n,onMouseDown:o=>{var r;(r=e.onMouseDown)===null||r===void 0||r.call(e,o),!o.defaultPrevented&&o.detail>1&&o.preventDefault()}}))),Wa=fr;function $t(e){const n=t.exports.useRef({value:e,previous:e});return t.exports.useMemo(()=>(n.current.value!==e&&(n.current.previous=n.current.value,n.current.value=e),n.current.previous),[e])}const so="Checkbox",[pr,Ba]=ae(so),[$r,xr]=pr(so),mr=t.exports.forwardRef((e,n)=>{const{__scopeCheckbox:o,name:r,checked:s,defaultChecked:a,required:i,disabled:c,value:l="on",onCheckedChange:d,...f}=e,[u,b]=t.exports.useState(null),x=O(n,h=>b(h)),v=t.exports.useRef(!1),p=u?Boolean(u.closest("form")):!0,[$=!1,g]=xe({prop:s,defaultProp:a,onChange:d}),m=t.exports.useRef($);return t.exports.useEffect(()=>{const h=u==null?void 0:u.form;if(h){const w=()=>g(m.current);return h.addEventListener("reset",w),()=>h.removeEventListener("reset",w)}},[u,g]),t.exports.createElement($r,{scope:o,state:$,disabled:c},t.exports.createElement(A.button,E({type:"button",role:"checkbox","aria-checked":$e($)?"mixed":$,"aria-required":i,"data-state":ao($),"data-disabled":c?"":void 0,disabled:c,value:l},f,{ref:x,onKeyDown:y(e.onKeyDown,h=>{h.key==="Enter"&&h.preventDefault()}),onClick:y(e.onClick,h=>{g(w=>$e(w)?!0:!w),p&&(v.current=h.isPropagationStopped(),v.current||h.stopPropagation())})})),p&&t.exports.createElement(hr,{control:u,bubbles:!v.current,name:r,value:l,checked:$,required:i,disabled:c,style:{transform:"translateX(-100%)"}}))}),br="CheckboxIndicator",vr=t.exports.forwardRef((e,n)=>{const{__scopeCheckbox:o,forceMount:r,...s}=e,a=xr(br,o);return t.exports.createElement(re,{present:r||$e(a.state)||a.state===!0},t.exports.createElement(A.span,E({"data-state":ao(a.state),"data-disabled":a.disabled?"":void 0},s,{ref:n,style:{pointerEvents:"none",...e.style}})))}),hr=e=>{const{control:n,checked:o,bubbles:r=!0,...s}=e,a=t.exports.useRef(null),i=$t(o),c=lt(n);return t.exports.useEffect(()=>{const l=a.current,d=window.HTMLInputElement.prototype,u=Object.getOwnPropertyDescriptor(d,"checked").set;if(i!==o&&u){const b=new Event("click",{bubbles:r});l.indeterminate=$e(o),u.call(l,$e(o)?!1:o),l.dispatchEvent(b)}},[i,o,r]),t.exports.createElement("input",E({type:"checkbox","aria-hidden":!0,defaultChecked:$e(o)?!1:o},s,{tabIndex:-1,ref:a,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function $e(e){return e==="indeterminate"}function ao(e){return $e(e)?"indeterminate":e?"checked":"unchecked"}const Ua=mr,za=vr,Xe="focusScope.autoFocusOnMount",Ge="focusScope.autoFocusOnUnmount",Ct={bubbles:!1,cancelable:!0},co=t.exports.forwardRef((e,n)=>{const{loop:o=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:a,...i}=e,[c,l]=t.exports.useState(null),d=z(s),f=z(a),u=t.exports.useRef(null),b=O(n,p=>l(p)),x=t.exports.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;t.exports.useEffect(()=>{if(r){let p=function(h){if(x.paused||!c)return;const w=h.target;c.contains(w)?u.current=w:fe(u.current,{select:!0})},$=function(h){if(x.paused||!c)return;const w=h.relatedTarget;w!==null&&(c.contains(w)||fe(u.current,{select:!0}))},g=function(h){if(document.activeElement===document.body)for(const C of h)C.removedNodes.length>0&&fe(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",$);const m=new MutationObserver(g);return c&&m.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",$),m.disconnect()}}},[r,c,x.paused]),t.exports.useEffect(()=>{if(c){Pt.add(x);const p=document.activeElement;if(!c.contains(p)){const g=new CustomEvent(Xe,Ct);c.addEventListener(Xe,d),c.dispatchEvent(g),g.defaultPrevented||(gr(yr(io(c)),{select:!0}),document.activeElement===p&&fe(c))}return()=>{c.removeEventListener(Xe,d),setTimeout(()=>{const g=new CustomEvent(Ge,Ct);c.addEventListener(Ge,f),c.dispatchEvent(g),g.defaultPrevented||fe(p!=null?p:document.body,{select:!0}),c.removeEventListener(Ge,f),Pt.remove(x)},0)}}},[c,d,f,x]);const v=t.exports.useCallback(p=>{if(!o&&!r||x.paused)return;const $=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,g=document.activeElement;if($&&g){const m=p.currentTarget,[h,w]=Er(m);h&&w?!p.shiftKey&&g===w?(p.preventDefault(),o&&fe(h,{select:!0})):p.shiftKey&&g===h&&(p.preventDefault(),o&&fe(w,{select:!0})):g===m&&p.preventDefault()}},[o,r,x.paused]);return t.exports.createElement(A.div,E({tabIndex:-1},i,{ref:b,onKeyDown:v}))});function gr(e,{select:n=!1}={}){const o=document.activeElement;for(const r of e)if(fe(r,{select:n}),document.activeElement!==o)return}function Er(e){const n=io(e),o=yt(n,e),r=yt(n.reverse(),e);return[o,r]}function io(e){const n=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)n.push(o.currentNode);return n}function yt(e,n){for(const o of e)if(!wr(o,{upTo:n}))return o}function wr(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Sr(e){return e instanceof HTMLInputElement&&"select"in e}function fe(e,{select:n=!1}={}){if(e&&e.focus){const o=document.activeElement;e.focus({preventScroll:!0}),e!==o&&Sr(e)&&n&&e.select()}}const Pt=Cr();function Cr(){let e=[];return{add(n){const o=e[0];n!==o&&(o==null||o.pause()),e=Tt(e,n),e.unshift(n)},remove(n){var o;e=Tt(e,n),(o=e[0])===null||o===void 0||o.resume()}}}function Tt(e,n){const o=[...e],r=o.indexOf(n);return r!==-1&&o.splice(r,1),o}function yr(e){return e.filter(n=>n.tagName!=="A")}let qe=0;function lo(){t.exports.useEffect(()=>{var e,n;const o=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=o[0])!==null&&e!==void 0?e:_t()),document.body.insertAdjacentElement("beforeend",(n=o[1])!==null&&n!==void 0?n:_t()),qe++,()=>{qe===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),qe--}},[])}function _t(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const uo="Dialog",[fo,po]=ae(uo),[Pr,ce]=fo(uo),Tr=e=>{const{__scopeDialog:n,children:o,open:r,defaultOpen:s,onOpenChange:a,modal:i=!0}=e,c=t.exports.useRef(null),l=t.exports.useRef(null),[d=!1,f]=xe({prop:r,defaultProp:s,onChange:a});return t.exports.createElement(Pr,{scope:n,triggerRef:c,contentRef:l,contentId:ve(),titleId:ve(),descriptionId:ve(),open:d,onOpenChange:f,onOpenToggle:t.exports.useCallback(()=>f(u=>!u),[f]),modal:i},o)},$o="DialogPortal",[_r,xo]=fo($o,{forceMount:void 0}),Rr=e=>{const{__scopeDialog:n,forceMount:o,children:r,container:s}=e,a=ce($o,n);return t.exports.createElement(_r,{scope:n,forceMount:o},t.exports.Children.map(r,i=>t.exports.createElement(re,{present:o||a.open},t.exports.createElement(dt,{asChild:!0,container:s},i))))},tt="DialogOverlay",Dr=t.exports.forwardRef((e,n)=>{const o=xo(tt,e.__scopeDialog),{forceMount:r=o.forceMount,...s}=e,a=ce(tt,e.__scopeDialog);return a.modal?t.exports.createElement(re,{present:r||a.open},t.exports.createElement(Ar,E({},s,{ref:n}))):null}),Ar=t.exports.forwardRef((e,n)=>{const{__scopeDialog:o,...r}=e,s=ce(tt,o);return t.exports.createElement(Nt,{as:he,allowPinchZoom:!0,shards:[s.contentRef]},t.exports.createElement(A.div,E({"data-state":vo(s.open)},r,{ref:n,style:{pointerEvents:"auto",...r.style}})))}),Ee="DialogContent",Or=t.exports.forwardRef((e,n)=>{const o=xo(Ee,e.__scopeDialog),{forceMount:r=o.forceMount,...s}=e,a=ce(Ee,e.__scopeDialog);return t.exports.createElement(re,{present:r||a.open},a.modal?t.exports.createElement(Ir,E({},s,{ref:n})):t.exports.createElement(Mr,E({},s,{ref:n})))}),Ir=t.exports.forwardRef((e,n)=>{const o=ce(Ee,e.__scopeDialog),r=t.exports.useRef(null),s=O(n,o.contentRef,r);return t.exports.useEffect(()=>{const a=r.current;if(a)return Lt(a)},[]),t.exports.createElement(mo,E({},e,{ref:s,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:y(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),(i=o.triggerRef.current)===null||i===void 0||i.focus()}),onPointerDownOutside:y(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:y(e.onFocusOutside,a=>a.preventDefault())}))}),Mr=t.exports.forwardRef((e,n)=>{const o=ce(Ee,e.__scopeDialog),r=t.exports.useRef(!1),s=t.exports.useRef(!1);return t.exports.createElement(mo,E({},e,{ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i;if((i=e.onCloseAutoFocus)===null||i===void 0||i.call(e,a),!a.defaultPrevented){var c;r.current||(c=o.triggerRef.current)===null||c===void 0||c.focus(),a.preventDefault()}r.current=!1,s.current=!1},onInteractOutside:a=>{var i,c;(i=e.onInteractOutside)===null||i===void 0||i.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const l=a.target;((c=o.triggerRef.current)===null||c===void 0?void 0:c.contains(l))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&s.current&&a.preventDefault()}}))}),mo=t.exports.forwardRef((e,n)=>{const{__scopeDialog:o,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,...i}=e,c=ce(Ee,o),l=t.exports.useRef(null),d=O(n,l);return lo(),t.exports.createElement(t.exports.Fragment,null,t.exports.createElement(co,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:a},t.exports.createElement(Le,E({role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":vo(c.open)},i,{ref:d,onDismiss:()=>c.onOpenChange(!1)}))),!1)}),bo="DialogTitle",Nr=t.exports.forwardRef((e,n)=>{const{__scopeDialog:o,...r}=e,s=ce(bo,o);return t.exports.createElement(A.h2,E({id:s.titleId},r,{ref:n}))}),Lr="DialogDescription",kr=t.exports.forwardRef((e,n)=>{const{__scopeDialog:o,...r}=e,s=ce(Lr,o);return t.exports.createElement(A.p,E({id:s.descriptionId},r,{ref:n}))}),Fr="DialogClose",Hr=t.exports.forwardRef((e,n)=>{const{__scopeDialog:o,...r}=e,s=ce(Fr,o);return t.exports.createElement(A.button,E({type:"button"},r,{ref:n,onClick:y(e.onClick,()=>s.onOpenChange(!1))}))});function vo(e){return e?"open":"closed"}const Vr="DialogTitleWarning",[Kr,Ya]=dn(Vr,{contentName:Ee,titleName:bo,docsSlug:"dialog"}),Wr=Tr,Br=Rr,Ur=Dr,zr=Or,Yr=Nr,Xr=kr,ho=Hr,Gr="AlertDialog",[qr,Xa]=ae(Gr,[po]),pe=po(),jr=e=>{const{__scopeAlertDialog:n,...o}=e,r=pe(n);return t.exports.createElement(Wr,E({},r,o,{modal:!0}))},Zr=e=>{const{__scopeAlertDialog:n,...o}=e,r=pe(n);return t.exports.createElement(Br,E({},r,o))},Jr=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,...r}=e,s=pe(o);return t.exports.createElement(Ur,E({},s,r,{ref:n}))}),go="AlertDialogContent",[Qr,es]=qr(go),ts=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,children:r,...s}=e,a=pe(o),i=t.exports.useRef(null),c=O(n,i),l=t.exports.useRef(null);return t.exports.createElement(Kr,{contentName:go,titleName:os,docsSlug:"alert-dialog"},t.exports.createElement(Qr,{scope:o,cancelRef:l},t.exports.createElement(zr,E({role:"alertdialog"},a,s,{ref:c,onOpenAutoFocus:y(s.onOpenAutoFocus,d=>{var f;d.preventDefault(),(f=l.current)===null||f===void 0||f.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault()}),t.exports.createElement(at,null,r),!1)))}),os="AlertDialogTitle",ns=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,...r}=e,s=pe(o);return t.exports.createElement(Yr,E({},s,r,{ref:n}))}),rs=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,...r}=e,s=pe(o);return t.exports.createElement(Xr,E({},s,r,{ref:n}))}),ss=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,...r}=e,s=pe(o);return t.exports.createElement(ho,E({},s,r,{ref:n}))}),as="AlertDialogCancel",cs=t.exports.forwardRef((e,n)=>{const{__scopeAlertDialog:o,...r}=e,{cancelRef:s}=es(as,o),a=pe(o),i=O(n,s);return t.exports.createElement(ho,E({},a,r,{ref:i}))}),Ga=jr,qa=Zr,ja=Jr,Za=ts,Ja=ss,Qa=cs,ec=ns,tc=rs;function xt(e){const n=e+"CollectionProvider",[o,r]=ae(n),[s,a]=o(n,{collectionRef:{current:null},itemMap:new Map}),i=x=>{const{scope:v,children:p}=x,$=se.useRef(null),g=se.useRef(new Map).current;return se.createElement(s,{scope:v,itemMap:g,collectionRef:$},p)},c=e+"CollectionSlot",l=se.forwardRef((x,v)=>{const{scope:p,children:$}=x,g=a(c,p),m=O(v,g.collectionRef);return se.createElement(he,{ref:m},$)}),d=e+"CollectionItemSlot",f="data-radix-collection-item",u=se.forwardRef((x,v)=>{const{scope:p,children:$,...g}=x,m=se.useRef(null),h=O(v,m),w=a(d,p);return se.useEffect(()=>(w.itemMap.set(m,{ref:m,...g}),()=>void w.itemMap.delete(m))),se.createElement(he,{[f]:"",ref:h},$)});function b(x){const v=a(e+"CollectionConsumer",x);return se.useCallback(()=>{const $=v.collectionRef.current;if(!$)return[];const g=Array.from($.querySelectorAll(`[${f}]`));return Array.from(v.itemMap.values()).sort((w,C)=>g.indexOf(w.ref.current)-g.indexOf(C.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:l,ItemSlot:u},b,r]}const is=[" ","Enter","ArrowUp","ArrowDown"],ls=[" ","Enter"],Ke="Select",[We,mt,ds]=xt(Ke),[Se,oc]=ae(Ke,[ds,ke]),bt=ke(),[us,me]=Se(Ke),[fs,ps]=Se(Ke),$s=e=>{const{__scopeSelect:n,children:o,open:r,defaultOpen:s,onOpenChange:a,value:i,defaultValue:c,onValueChange:l,dir:d,name:f,autoComplete:u,disabled:b,required:x}=e,v=bt(n),[p,$]=t.exports.useState(null),[g,m]=t.exports.useState(null),[h,w]=t.exports.useState(!1),C=ct(d),[I=!1,P]=xe({prop:r,defaultProp:s,onChange:a}),[T,_]=xe({prop:i,defaultProp:c,onChange:l}),M=t.exports.useRef(null),N=p?Boolean(p.closest("form")):!0,[H,B]=t.exports.useState(new Set),S=Array.from(H).map(D=>D.props.value).join(";");return t.exports.createElement(to,v,t.exports.createElement(us,{required:x,scope:n,trigger:p,onTriggerChange:$,valueNode:g,onValueNodeChange:m,valueNodeHasChildren:h,onValueNodeHasChildrenChange:w,contentId:ve(),value:T,onValueChange:_,open:I,onOpenChange:P,dir:C,triggerPointerDownPosRef:M,disabled:b},t.exports.createElement(We.Provider,{scope:n},t.exports.createElement(fs,{scope:e.__scopeSelect,onNativeOptionAdd:t.exports.useCallback(D=>{B(F=>new Set(F).add(D))},[]),onNativeOptionRemove:t.exports.useCallback(D=>{B(F=>{const U=new Set(F);return U.delete(D),U})},[])},o)),N?t.exports.createElement(Co,{key:S,"aria-hidden":!0,required:x,tabIndex:-1,name:f,autoComplete:u,value:T,onChange:D=>_(D.target.value),disabled:b},T===void 0?t.exports.createElement("option",{value:""}):null,Array.from(H)):null))},xs="SelectTrigger",ms=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,disabled:r=!1,...s}=e,a=bt(o),i=me(xs,o),c=i.disabled||r,l=O(n,i.onTriggerChange),d=mt(o),[f,u,b]=yo(v=>{const p=d().filter(m=>!m.disabled),$=p.find(m=>m.value===i.value),g=Po(p,v,$);g!==void 0&&i.onValueChange(g.value)}),x=()=>{c||(i.onOpenChange(!0),b())};return t.exports.createElement(oo,E({asChild:!0},a),t.exports.createElement(A.button,E({type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":So(i.value)?"":void 0},s,{ref:l,onClick:y(s.onClick,v=>{v.currentTarget.focus()}),onPointerDown:y(s.onPointerDown,v=>{const p=v.target;p.hasPointerCapture(v.pointerId)&&p.releasePointerCapture(v.pointerId),v.button===0&&v.ctrlKey===!1&&(x(),i.triggerPointerDownPosRef.current={x:Math.round(v.pageX),y:Math.round(v.pageY)},v.preventDefault())}),onKeyDown:y(s.onKeyDown,v=>{const p=f.current!=="";!(v.ctrlKey||v.altKey||v.metaKey)&&v.key.length===1&&u(v.key),!(p&&v.key===" ")&&is.includes(v.key)&&(x(),v.preventDefault())})})))}),bs="SelectValue",vs=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,className:r,style:s,children:a,placeholder:i="",...c}=e,l=me(bs,o),{onValueNodeHasChildrenChange:d}=l,f=a!==void 0,u=O(n,l.onValueNodeChange);return J(()=>{d(f)},[d,f]),t.exports.createElement(A.span,E({},c,{ref:u,style:{pointerEvents:"none"}}),So(l.value)?t.exports.createElement(t.exports.Fragment,null,i):a)}),hs=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,children:r,...s}=e;return t.exports.createElement(A.span,E({"aria-hidden":!0},s,{ref:n}),r||"\u25BC")}),gs=e=>t.exports.createElement(dt,E({asChild:!0},e)),we="SelectContent",Es=t.exports.forwardRef((e,n)=>{const o=me(we,e.__scopeSelect),[r,s]=t.exports.useState();if(J(()=>{s(new DocumentFragment)},[]),!o.open){const a=r;return a?Te.exports.createPortal(t.exports.createElement(Eo,{scope:e.__scopeSelect},t.exports.createElement(We.Slot,{scope:e.__scopeSelect},t.exports.createElement("div",null,e.children))),a):null}return t.exports.createElement(ws,E({},e,{ref:n}))}),de=10,[Eo,Be]=Se(we),ws=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,position:r="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:a,onPointerDownOutside:i,side:c,sideOffset:l,align:d,alignOffset:f,arrowPadding:u,collisionBoundary:b,collisionPadding:x,sticky:v,hideWhenDetached:p,avoidCollisions:$,...g}=e,m=me(we,o),[h,w]=t.exports.useState(null),[C,I]=t.exports.useState(null),P=O(n,R=>w(R)),[T,_]=t.exports.useState(null),[M,N]=t.exports.useState(null),H=mt(o),[B,S]=t.exports.useState(!1),D=t.exports.useRef(!1);t.exports.useEffect(()=>{if(h)return Lt(h)},[h]),lo();const F=t.exports.useCallback(R=>{const[V,...G]=H().map(k=>k.ref.current),[K]=G.slice(-1),L=document.activeElement;for(const k of R)if(k===L||(k==null||k.scrollIntoView({block:"nearest"}),k===V&&C&&(C.scrollTop=0),k===K&&C&&(C.scrollTop=C.scrollHeight),k==null||k.focus(),document.activeElement!==L))return},[H,C]),U=t.exports.useCallback(()=>F([T,h]),[F,T,h]);t.exports.useEffect(()=>{B&&U()},[B,U]);const{onOpenChange:X,triggerPointerDownPosRef:W}=m;t.exports.useEffect(()=>{if(h){let R={x:0,y:0};const V=K=>{var L,k,j,Z;R={x:Math.abs(Math.round(K.pageX)-((L=(k=W.current)===null||k===void 0?void 0:k.x)!==null&&L!==void 0?L:0)),y:Math.abs(Math.round(K.pageY)-((j=(Z=W.current)===null||Z===void 0?void 0:Z.y)!==null&&j!==void 0?j:0))}},G=K=>{R.x<=10&&R.y<=10?K.preventDefault():h.contains(K.target)||X(!1),document.removeEventListener("pointermove",V),W.current=null};return W.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",G,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",G,{capture:!0})}}},[h,X,W]),t.exports.useEffect(()=>{const R=()=>X(!1);return window.addEventListener("blur",R),window.addEventListener("resize",R),()=>{window.removeEventListener("blur",R),window.removeEventListener("resize",R)}},[X]);const[q,Y]=yo(R=>{const V=H().filter(L=>!L.disabled),G=V.find(L=>L.ref.current===document.activeElement),K=Po(V,R,G);K&&setTimeout(()=>K.ref.current.focus())}),Q=t.exports.useCallback((R,V,G)=>{const K=!D.current&&!G;(m.value!==void 0&&m.value===V||K)&&(_(R),K&&(D.current=!0))},[m.value]),oe=t.exports.useCallback(()=>h==null?void 0:h.focus(),[h]),ne=t.exports.useCallback((R,V,G)=>{const K=!D.current&&!G;(m.value!==void 0&&m.value===V||K)&&N(R)},[m.value]),ie=r==="popper"?Rt:Ss,ee=ie===Rt?{side:c,sideOffset:l,align:d,alignOffset:f,arrowPadding:u,collisionBoundary:b,collisionPadding:x,sticky:v,hideWhenDetached:p,avoidCollisions:$}:{};return t.exports.createElement(Eo,{scope:o,content:h,viewport:C,onViewportChange:I,itemRefCallback:Q,selectedItem:T,onItemLeave:oe,itemTextRefCallback:ne,focusSelectedItem:U,selectedItemText:M,position:r,isPositioned:B,searchRef:q},t.exports.createElement(Nt,{as:he,allowPinchZoom:!0},t.exports.createElement(co,{asChild:!0,trapped:m.open,onMountAutoFocus:R=>{R.preventDefault()},onUnmountAutoFocus:y(s,R=>{var V;(V=m.trigger)===null||V===void 0||V.focus({preventScroll:!0}),R.preventDefault()})},t.exports.createElement(Le,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:R=>R.preventDefault(),onDismiss:()=>m.onOpenChange(!1)},t.exports.createElement(ie,E({role:"listbox",id:m.contentId,"data-state":m.open?"open":"closed",dir:m.dir,onContextMenu:R=>R.preventDefault()},g,ee,{onPlaced:()=>S(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...g.style},onKeyDown:y(g.onKeyDown,R=>{const V=R.ctrlKey||R.altKey||R.metaKey;if(R.key==="Tab"&&R.preventDefault(),!V&&R.key.length===1&&Y(R.key),["ArrowUp","ArrowDown","Home","End"].includes(R.key)){let K=H().filter(L=>!L.disabled).map(L=>L.ref.current);if(["ArrowUp","End"].includes(R.key)&&(K=K.slice().reverse()),["ArrowUp","ArrowDown"].includes(R.key)){const L=R.target,k=K.indexOf(L);K=K.slice(k+1)}setTimeout(()=>F(K)),R.preventDefault()}})}))))))}),Ss=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,onPlaced:r,...s}=e,a=me(we,o),i=Be(we,o),[c,l]=t.exports.useState(null),[d,f]=t.exports.useState(null),u=O(n,P=>f(P)),b=mt(o),x=t.exports.useRef(!1),v=t.exports.useRef(!0),{viewport:p,selectedItem:$,selectedItemText:g,focusSelectedItem:m}=i,h=t.exports.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&p&&$&&g){const P=a.trigger.getBoundingClientRect(),T=d.getBoundingClientRect(),_=a.valueNode.getBoundingClientRect(),M=g.getBoundingClientRect();if(a.dir!=="rtl"){const L=M.left-T.left,k=_.left-L,j=P.left-k,Z=P.width+j,le=Math.max(Z,T.width),be=window.innerWidth-de,Ce=ye(k,[de,be-le]);c.style.minWidth=Z+"px",c.style.left=Ce+"px"}else{const L=T.right-M.right,k=window.innerWidth-_.right-L,j=window.innerWidth-P.right-k,Z=P.width+j,le=Math.max(Z,T.width),be=window.innerWidth-de,Ce=ye(k,[de,be-le]);c.style.minWidth=Z+"px",c.style.right=Ce+"px"}const N=b(),H=window.innerHeight-de*2,B=p.scrollHeight,S=window.getComputedStyle(d),D=parseInt(S.borderTopWidth,10),F=parseInt(S.paddingTop,10),U=parseInt(S.borderBottomWidth,10),X=parseInt(S.paddingBottom,10),W=D+F+B+X+U,q=Math.min($.offsetHeight*5,W),Y=window.getComputedStyle(p),Q=parseInt(Y.paddingTop,10),oe=parseInt(Y.paddingBottom,10),ne=P.top+P.height/2-de,ie=H-ne,ee=$.offsetHeight/2,R=$.offsetTop+ee,V=D+F+R,G=W-V;if(V<=ne){const L=$===N[N.length-1].ref.current;c.style.bottom="0px";const k=d.clientHeight-p.offsetTop-p.offsetHeight,j=Math.max(ie,ee+(L?oe:0)+k+U),Z=V+j;c.style.height=Z+"px"}else{const L=$===N[0].ref.current;c.style.top="0px";const j=Math.max(ne,D+p.offsetTop+(L?Q:0)+ee)+G;c.style.height=j+"px",p.scrollTop=V-ne+p.offsetTop}c.style.margin=`${de}px 0`,c.style.minHeight=q+"px",c.style.maxHeight=H+"px",r==null||r(),requestAnimationFrame(()=>x.current=!0)}},[b,a.trigger,a.valueNode,c,d,p,$,g,a.dir,r]);J(()=>h(),[h]);const[w,C]=t.exports.useState();J(()=>{d&&C(window.getComputedStyle(d).zIndex)},[d]);const I=t.exports.useCallback(P=>{P&&v.current===!0&&(h(),m==null||m(),v.current=!1)},[h,m]);return t.exports.createElement(Cs,{scope:o,contentWrapper:c,shouldExpandOnScrollRef:x,onScrollButtonChange:I},t.exports.createElement("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:w}},t.exports.createElement(A.div,E({},s,{ref:u,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}}))))}),Rt=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,align:r="start",collisionPadding:s=de,...a}=e,i=bt(o);return t.exports.createElement(no,E({},i,a,{ref:n,align:r,collisionPadding:s,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}))}),[Cs,ys]=Se(we,{}),Dt="SelectViewport",Ps=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,...r}=e,s=Be(Dt,o),a=ys(Dt,o),i=O(n,s.onViewportChange),c=t.exports.useRef(0);return t.exports.createElement(t.exports.Fragment,null,t.exports.createElement("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),t.exports.createElement(We.Slot,{scope:o},t.exports.createElement(A.div,E({"data-radix-select-viewport":"",role:"presentation"},r,{ref:i,style:{position:"relative",flex:1,overflow:"auto",...r.style},onScroll:y(r.onScroll,l=>{const d=l.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:u}=a;if(u!=null&&u.current&&f){const b=Math.abs(c.current-d.scrollTop);if(b>0){const x=window.innerHeight-de*2,v=parseFloat(f.style.minHeight),p=parseFloat(f.style.height),$=Math.max(v,p);if($<x){const g=$+b,m=Math.min(x,g),h=g-m;f.style.height=m+"px",f.style.bottom==="0px"&&(d.scrollTop=h>0?h:0,f.style.justifyContent="flex-end")}}}c.current=d.scrollTop})}))))}),Ts="SelectGroup",[nc,_s]=Se(Ts),Rs="SelectLabel",Ds=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,...r}=e,s=_s(Rs,o);return t.exports.createElement(A.div,E({id:s.id},r,{ref:n}))}),ot="SelectItem",[As,wo]=Se(ot),Os=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,value:r,disabled:s=!1,textValue:a,...i}=e,c=me(ot,o),l=Be(ot,o),d=c.value===r,[f,u]=t.exports.useState(a!=null?a:""),[b,x]=t.exports.useState(!1),v=O(n,g=>{var m;return(m=l.itemRefCallback)===null||m===void 0?void 0:m.call(l,g,r,s)}),p=ve(),$=()=>{s||(c.onValueChange(r),c.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return t.exports.createElement(As,{scope:o,value:r,disabled:s,textId:p,isSelected:d,onItemTextChange:t.exports.useCallback(g=>{u(m=>{var h;return m||((h=g==null?void 0:g.textContent)!==null&&h!==void 0?h:"").trim()})},[])},t.exports.createElement(We.ItemSlot,{scope:o,value:r,disabled:s,textValue:f},t.exports.createElement(A.div,E({role:"option","aria-labelledby":p,"data-highlighted":b?"":void 0,"aria-selected":d&&b,"data-state":d?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1},i,{ref:v,onFocus:y(i.onFocus,()=>x(!0)),onBlur:y(i.onBlur,()=>x(!1)),onPointerUp:y(i.onPointerUp,$),onPointerMove:y(i.onPointerMove,g=>{if(s){var m;(m=l.onItemLeave)===null||m===void 0||m.call(l)}else g.currentTarget.focus({preventScroll:!0})}),onPointerLeave:y(i.onPointerLeave,g=>{if(g.currentTarget===document.activeElement){var m;(m=l.onItemLeave)===null||m===void 0||m.call(l)}}),onKeyDown:y(i.onKeyDown,g=>{var m;((m=l.searchRef)===null||m===void 0?void 0:m.current)!==""&&g.key===" "||(ls.includes(g.key)&&$(),g.key===" "&&g.preventDefault())})}))))}),Ae="SelectItemText",Is=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,className:r,style:s,...a}=e,i=me(Ae,o),c=Be(Ae,o),l=wo(Ae,o),d=ps(Ae,o),[f,u]=t.exports.useState(null),b=O(n,g=>u(g),l.onItemTextChange,g=>{var m;return(m=c.itemTextRefCallback)===null||m===void 0?void 0:m.call(c,g,l.value,l.disabled)}),x=f==null?void 0:f.textContent,v=t.exports.useMemo(()=>t.exports.createElement("option",{key:l.value,value:l.value,disabled:l.disabled},x),[l.disabled,l.value,x]),{onNativeOptionAdd:p,onNativeOptionRemove:$}=d;return J(()=>(p(v),()=>$(v)),[p,$,v]),t.exports.createElement(t.exports.Fragment,null,t.exports.createElement(A.span,E({id:l.textId},a,{ref:b})),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Te.exports.createPortal(a.children,i.valueNode):null)}),Ms="SelectItemIndicator",Ns=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,...r}=e;return wo(Ms,o).isSelected?t.exports.createElement(A.span,E({"aria-hidden":!0},r,{ref:n})):null}),Ls=t.exports.forwardRef((e,n)=>{const{__scopeSelect:o,...r}=e;return t.exports.createElement(A.div,E({"aria-hidden":!0},r,{ref:n}))});function So(e){return e===""||e===void 0}const Co=t.exports.forwardRef((e,n)=>{const{value:o,...r}=e,s=t.exports.useRef(null),a=O(n,s),i=$t(o);return t.exports.useEffect(()=>{const c=s.current,l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==o&&f){const u=new Event("change",{bubbles:!0});f.call(c,o),c.dispatchEvent(u)}},[i,o]),t.exports.createElement(Fe,{asChild:!0},t.exports.createElement("select",E({},r,{ref:a,defaultValue:o})))});Co.displayName="BubbleSelect";function yo(e){const n=z(e),o=t.exports.useRef(""),r=t.exports.useRef(0),s=t.exports.useCallback(i=>{const c=o.current+i;n(c),function l(d){o.current=d,window.clearTimeout(r.current),d!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(c)},[n]),a=t.exports.useCallback(()=>{o.current="",window.clearTimeout(r.current)},[]);return t.exports.useEffect(()=>()=>window.clearTimeout(r.current),[]),[o,s,a]}function Po(e,n,o){const s=n.length>1&&Array.from(n).every(d=>d===n[0])?n[0]:n,a=o?e.indexOf(o):-1;let i=ks(e,Math.max(a,0));s.length===1&&(i=i.filter(d=>d!==o));const l=i.find(d=>d.textValue.toLowerCase().startsWith(s.toLowerCase()));return l!==o?l:void 0}function ks(e,n){return e.map((o,r)=>e[(n+r)%e.length])}const rc=$s,sc=ms,ac=vs,cc=hs,ic=gs,lc=Es,dc=Ps,uc=Ds,fc=Os,pc=Is,$c=Ns,xc=Ls,To=["PageUp","PageDown"],_o=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Ro={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},_e="Slider",[nt,Fs,Hs]=xt(_e),[Do,mc]=ae(_e,[Hs]),[Vs,Ue]=Do(_e),Ks=t.exports.forwardRef((e,n)=>{const{name:o,min:r=0,max:s=100,step:a=1,orientation:i="horizontal",disabled:c=!1,minStepsBetweenThumbs:l=0,defaultValue:d=[r],value:f,onValueChange:u=()=>{},onValueCommit:b=()=>{},inverted:x=!1,...v}=e,[p,$]=t.exports.useState(null),g=O(n,S=>$(S)),m=t.exports.useRef(new Set),h=t.exports.useRef(0),w=i==="horizontal",C=p?Boolean(p.closest("form")):!0,I=w?Ws:Bs,[P=[],T]=xe({prop:f,defaultProp:d,onChange:S=>{var D;(D=[...m.current][h.current])===null||D===void 0||D.focus(),u(S)}}),_=t.exports.useRef(P);function M(S){const D=Js(P,S);B(S,D)}function N(S){B(S,h.current)}function H(){const S=_.current[h.current];P[h.current]!==S&&b(P)}function B(S,D,{commit:F}={commit:!1}){const U=oa(a),X=na(Math.round((S-r)/a)*a+r,U),W=ye(X,[r,s]);T((q=[])=>{const Y=js(q,W,D);if(ta(Y,l*a)){h.current=Y.indexOf(W);const Q=String(Y)!==String(q);return Q&&F&&b(Y),Q?Y:q}else return q})}return t.exports.createElement(Vs,{scope:e.__scopeSlider,disabled:c,min:r,max:s,valueIndexToChangeRef:h,thumbs:m.current,values:P,orientation:i},t.exports.createElement(nt.Provider,{scope:e.__scopeSlider},t.exports.createElement(nt.Slot,{scope:e.__scopeSlider},t.exports.createElement(I,E({"aria-disabled":c,"data-disabled":c?"":void 0},v,{ref:g,onPointerDown:y(v.onPointerDown,()=>{c||(_.current=P)}),min:r,max:s,inverted:x,onSlideStart:c?void 0:M,onSlideMove:c?void 0:N,onSlideEnd:c?void 0:H,onHomeKeyDown:()=>!c&&B(r,0,{commit:!0}),onEndKeyDown:()=>!c&&B(s,P.length-1,{commit:!0}),onStepKeyDown:({event:S,direction:D})=>{if(!c){const X=To.includes(S.key)||S.shiftKey&&_o.includes(S.key)?10:1,W=h.current,q=P[W],Y=a*X*D;B(q+Y,W,{commit:!0})}}})))),C&&P.map((S,D)=>t.exports.createElement(qs,{key:D,name:o?o+(P.length>1?"[]":""):void 0,value:S})))}),[Ao,Oo]=Do(_e,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Ws=t.exports.forwardRef((e,n)=>{const{min:o,max:r,dir:s,inverted:a,onSlideStart:i,onSlideMove:c,onSlideEnd:l,onStepKeyDown:d,...f}=e,[u,b]=t.exports.useState(null),x=O(n,h=>b(h)),v=t.exports.useRef(),p=ct(s),$=p==="ltr",g=$&&!a||!$&&a;function m(h){const w=v.current||u.getBoundingClientRect(),C=[0,w.width],P=vt(C,g?[o,r]:[r,o]);return v.current=w,P(h-w.left)}return t.exports.createElement(Ao,{scope:e.__scopeSlider,startEdge:g?"left":"right",endEdge:g?"right":"left",direction:g?1:-1,size:"width"},t.exports.createElement(Io,E({dir:p,"data-orientation":"horizontal"},f,{ref:x,style:{...f.style,["--radix-slider-thumb-transform"]:"translateX(-50%)"},onSlideStart:h=>{const w=m(h.clientX);i==null||i(w)},onSlideMove:h=>{const w=m(h.clientX);c==null||c(w)},onSlideEnd:()=>{v.current=void 0,l==null||l()},onStepKeyDown:h=>{const C=Ro[g?"from-left":"from-right"].includes(h.key);d==null||d({event:h,direction:C?-1:1})}})))}),Bs=t.exports.forwardRef((e,n)=>{const{min:o,max:r,inverted:s,onSlideStart:a,onSlideMove:i,onSlideEnd:c,onStepKeyDown:l,...d}=e,f=t.exports.useRef(null),u=O(n,f),b=t.exports.useRef(),x=!s;function v(p){const $=b.current||f.current.getBoundingClientRect(),g=[0,$.height],h=vt(g,x?[r,o]:[o,r]);return b.current=$,h(p-$.top)}return t.exports.createElement(Ao,{scope:e.__scopeSlider,startEdge:x?"bottom":"top",endEdge:x?"top":"bottom",size:"height",direction:x?1:-1},t.exports.createElement(Io,E({"data-orientation":"vertical"},d,{ref:u,style:{...d.style,["--radix-slider-thumb-transform"]:"translateY(50%)"},onSlideStart:p=>{const $=v(p.clientY);a==null||a($)},onSlideMove:p=>{const $=v(p.clientY);i==null||i($)},onSlideEnd:()=>{b.current=void 0,c==null||c()},onStepKeyDown:p=>{const g=Ro[x?"from-bottom":"from-top"].includes(p.key);l==null||l({event:p,direction:g?-1:1})}})))}),Io=t.exports.forwardRef((e,n)=>{const{__scopeSlider:o,onSlideStart:r,onSlideMove:s,onSlideEnd:a,onHomeKeyDown:i,onEndKeyDown:c,onStepKeyDown:l,...d}=e,f=Ue(_e,o);return t.exports.createElement(A.span,E({},d,{ref:n,onKeyDown:y(e.onKeyDown,u=>{u.key==="Home"?(i(u),u.preventDefault()):u.key==="End"?(c(u),u.preventDefault()):To.concat(_o).includes(u.key)&&(l(u),u.preventDefault())}),onPointerDown:y(e.onPointerDown,u=>{const b=u.target;b.setPointerCapture(u.pointerId),u.preventDefault(),f.thumbs.has(b)?b.focus():r(u)}),onPointerMove:y(e.onPointerMove,u=>{u.target.hasPointerCapture(u.pointerId)&&s(u)}),onPointerUp:y(e.onPointerUp,u=>{const b=u.target;b.hasPointerCapture(u.pointerId)&&(b.releasePointerCapture(u.pointerId),a(u))})}))}),Us="SliderTrack",zs=t.exports.forwardRef((e,n)=>{const{__scopeSlider:o,...r}=e,s=Ue(Us,o);return t.exports.createElement(A.span,E({"data-disabled":s.disabled?"":void 0,"data-orientation":s.orientation},r,{ref:n}))}),At="SliderRange",Ys=t.exports.forwardRef((e,n)=>{const{__scopeSlider:o,...r}=e,s=Ue(At,o),a=Oo(At,o),i=t.exports.useRef(null),c=O(n,i),l=s.values.length,d=s.values.map(b=>Mo(b,s.min,s.max)),f=l>1?Math.min(...d):0,u=100-Math.max(...d);return t.exports.createElement(A.span,E({"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0},r,{ref:c,style:{...e.style,[a.startEdge]:f+"%",[a.endEdge]:u+"%"}}))}),Ot="SliderThumb",Xs=t.exports.forwardRef((e,n)=>{const o=Fs(e.__scopeSlider),[r,s]=t.exports.useState(null),a=O(n,c=>s(c)),i=t.exports.useMemo(()=>r?o().findIndex(c=>c.ref.current===r):-1,[o,r]);return t.exports.createElement(Gs,E({},e,{ref:a,index:i}))}),Gs=t.exports.forwardRef((e,n)=>{const{__scopeSlider:o,index:r,...s}=e,a=Ue(Ot,o),i=Oo(Ot,o),[c,l]=t.exports.useState(null),d=O(n,$=>l($)),f=lt(c),u=a.values[r],b=u===void 0?0:Mo(u,a.min,a.max),x=Zs(r,a.values.length),v=f==null?void 0:f[i.size],p=v?Qs(v,b,i.direction):0;return t.exports.useEffect(()=>{if(c)return a.thumbs.add(c),()=>{a.thumbs.delete(c)}},[c,a.thumbs]),t.exports.createElement("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[i.startEdge]:`calc(${b}% + ${p}px)`}},t.exports.createElement(nt.ItemSlot,{scope:e.__scopeSlider},t.exports.createElement(A.span,E({role:"slider","aria-label":e["aria-label"]||x,"aria-valuemin":a.min,"aria-valuenow":u,"aria-valuemax":a.max,"aria-orientation":a.orientation,"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,tabIndex:a.disabled?void 0:0},s,{ref:d,style:u===void 0?{display:"none"}:e.style,onFocus:y(e.onFocus,()=>{a.valueIndexToChangeRef.current=r})}))))}),qs=e=>{const{value:n,...o}=e,r=t.exports.useRef(null),s=$t(n);return t.exports.useEffect(()=>{const a=r.current,i=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(i,"value").set;if(s!==n&&l){const d=new Event("input",{bubbles:!0});l.call(a,n),a.dispatchEvent(d)}},[s,n]),t.exports.createElement("input",E({style:{display:"none"}},o,{ref:r,defaultValue:n}))};function js(e=[],n,o){const r=[...e];return r[o]=n,r.sort((s,a)=>s-a)}function Mo(e,n,o){const a=100/(o-n)*(e-n);return ye(a,[0,100])}function Zs(e,n){return n>2?`Value ${e+1} of ${n}`:n===2?["Minimum","Maximum"][e]:void 0}function Js(e,n){if(e.length===1)return 0;const o=e.map(s=>Math.abs(s-n)),r=Math.min(...o);return o.indexOf(r)}function Qs(e,n,o){const r=e/2,a=vt([0,50],[0,r]);return(r-a(n)*o)*o}function ea(e){return e.slice(0,-1).map((n,o)=>e[o+1]-n)}function ta(e,n){if(n>0){const o=ea(e);return Math.min(...o)>=n}return!0}function vt(e,n){return o=>{if(e[0]===e[1]||n[0]===n[1])return n[0];const r=(n[1]-n[0])/(e[1]-e[0]);return n[0]+r*(o-e[0])}}function oa(e){return(String(e).split(".")[1]||"").length}function na(e,n){const o=Math.pow(10,n);return Math.round(e*o)/o}const bc=Ks,vc=zs,hc=Ys,gc=Xs,No="ToastProvider",[ht,ra,sa]=xt("Toast"),[Lo,Ec]=ae("Toast",[sa]),[aa,ze]=Lo(No),ko=e=>{const{__scopeToast:n,label:o="Notification",duration:r=5e3,swipeDirection:s="right",swipeThreshold:a=50,children:i}=e,[c,l]=t.exports.useState(null),[d,f]=t.exports.useState(0),u=t.exports.useRef(!1),b=t.exports.useRef(!1);return t.exports.createElement(ht.Provider,{scope:n},t.exports.createElement(aa,{scope:n,label:o,duration:r,swipeDirection:s,swipeThreshold:a,toastCount:d,viewport:c,onViewportChange:l,onToastAdd:t.exports.useCallback(()=>f(x=>x+1),[]),onToastRemove:t.exports.useCallback(()=>f(x=>x-1),[]),isFocusedToastEscapeKeyDownRef:u,isClosePausedRef:b},i))};ko.propTypes={label(e){if(e.label&&typeof e.label=="string"&&!e.label.trim()){const n=`Invalid prop \`label\` supplied to \`${No}\`. Expected non-empty \`string\`.`;return new Error(n)}return null}};const ca="ToastViewport",ia=["F8"],rt="toast.viewportPause",st="toast.viewportResume",la=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,hotkey:r=ia,label:s="Notifications ({hotkey})",...a}=e,i=ze(ca,o),c=ra(o),l=t.exports.useRef(null),d=t.exports.useRef(null),f=t.exports.useRef(null),u=t.exports.useRef(null),b=O(n,u,i.onViewportChange),x=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=i.toastCount>0;t.exports.useEffect(()=>{const $=g=>{var m;r.every(w=>g[w]||g.code===w)&&((m=u.current)===null||m===void 0||m.focus())};return document.addEventListener("keydown",$),()=>document.removeEventListener("keydown",$)},[r]),t.exports.useEffect(()=>{const $=l.current,g=u.current;if(v&&$&&g){const m=()=>{if(!i.isClosePausedRef.current){const I=new CustomEvent(rt);g.dispatchEvent(I),i.isClosePausedRef.current=!0}},h=()=>{if(i.isClosePausedRef.current){const I=new CustomEvent(st);g.dispatchEvent(I),i.isClosePausedRef.current=!1}},w=I=>{!$.contains(I.relatedTarget)&&h()},C=()=>{$.contains(document.activeElement)||h()};return $.addEventListener("focusin",m),$.addEventListener("focusout",w),$.addEventListener("pointermove",m),$.addEventListener("pointerleave",C),window.addEventListener("blur",m),window.addEventListener("focus",h),()=>{$.removeEventListener("focusin",m),$.removeEventListener("focusout",w),$.removeEventListener("pointermove",m),$.removeEventListener("pointerleave",C),window.removeEventListener("blur",m),window.removeEventListener("focus",h)}}},[v,i.isClosePausedRef]);const p=t.exports.useCallback(({tabbingDirection:$})=>{const m=c().map(h=>{const w=h.ref.current,C=[w,...ya(w)];return $==="forwards"?C:C.reverse()});return($==="forwards"?m.reverse():m).flat()},[c]);return t.exports.useEffect(()=>{const $=u.current;if($){const g=m=>{const h=m.altKey||m.ctrlKey||m.metaKey;if(m.key==="Tab"&&!h){const T=document.activeElement,_=m.shiftKey;if(m.target===$&&_){var C;(C=d.current)===null||C===void 0||C.focus();return}const H=p({tabbingDirection:_?"backwards":"forwards"}),B=H.findIndex(S=>S===T);if(je(H.slice(B+1)))m.preventDefault();else{var I,P;_?(I=d.current)===null||I===void 0||I.focus():(P=f.current)===null||P===void 0||P.focus()}}};return $.addEventListener("keydown",g),()=>$.removeEventListener("keydown",g)}},[c,p]),t.exports.createElement(Nn,{ref:l,role:"region","aria-label":s.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:v?void 0:"none"}},v&&t.exports.createElement(It,{ref:d,onFocusFromOutsideViewport:()=>{const $=p({tabbingDirection:"forwards"});je($)}}),t.exports.createElement(ht.Slot,{scope:o},t.exports.createElement(A.ol,E({tabIndex:-1},a,{ref:b}))),v&&t.exports.createElement(It,{ref:f,onFocusFromOutsideViewport:()=>{const $=p({tabbingDirection:"backwards"});je($)}}))}),da="ToastFocusProxy",It=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,onFocusFromOutsideViewport:r,...s}=e,a=ze(da,o);return t.exports.createElement(Fe,E({"aria-hidden":!0,tabIndex:0},s,{ref:n,style:{position:"fixed"},onFocus:i=>{var c;const l=i.relatedTarget;!((c=a.viewport)!==null&&c!==void 0&&c.contains(l))&&r()}}))}),Ye="Toast",ua="toast.swipeStart",fa="toast.swipeMove",pa="toast.swipeCancel",$a="toast.swipeEnd",xa=t.exports.forwardRef((e,n)=>{const{forceMount:o,open:r,defaultOpen:s,onOpenChange:a,...i}=e,[c=!0,l]=xe({prop:r,defaultProp:s,onChange:a});return t.exports.createElement(re,{present:o||c},t.exports.createElement(Fo,E({open:c},i,{ref:n,onClose:()=>l(!1),onPause:z(e.onPause),onResume:z(e.onResume),onSwipeStart:y(e.onSwipeStart,d=>{d.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:y(e.onSwipeMove,d=>{const{x:f,y:u}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","move"),d.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${u}px`)}),onSwipeCancel:y(e.onSwipeCancel,d=>{d.currentTarget.setAttribute("data-swipe","cancel"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:y(e.onSwipeEnd,d=>{const{x:f,y:u}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","end"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${u}px`),l(!1)})})))}),[ma,ba]=Lo(Ye,{onClose(){}}),Fo=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,type:r="foreground",duration:s,open:a,onClose:i,onEscapeKeyDown:c,onPause:l,onResume:d,onSwipeStart:f,onSwipeMove:u,onSwipeCancel:b,onSwipeEnd:x,...v}=e,p=ze(Ye,o),[$,g]=t.exports.useState(null),m=O(n,S=>g(S)),h=t.exports.useRef(null),w=t.exports.useRef(null),C=s||p.duration,I=t.exports.useRef(0),P=t.exports.useRef(C),T=t.exports.useRef(0),{onToastAdd:_,onToastRemove:M}=p,N=z(()=>{var S;($==null?void 0:$.contains(document.activeElement))&&((S=p.viewport)===null||S===void 0||S.focus()),i()}),H=t.exports.useCallback(S=>{!S||S===1/0||(window.clearTimeout(T.current),I.current=new Date().getTime(),T.current=window.setTimeout(N,S))},[N]);t.exports.useEffect(()=>{const S=p.viewport;if(S){const D=()=>{H(P.current),d==null||d()},F=()=>{const U=new Date().getTime()-I.current;P.current=P.current-U,window.clearTimeout(T.current),l==null||l()};return S.addEventListener(rt,F),S.addEventListener(st,D),()=>{S.removeEventListener(rt,F),S.removeEventListener(st,D)}}},[p.viewport,C,l,d,H]),t.exports.useEffect(()=>{a&&!p.isClosePausedRef.current&&H(C)},[a,C,p.isClosePausedRef,H]),t.exports.useEffect(()=>(_(),()=>M()),[_,M]);const B=t.exports.useMemo(()=>$?Wo($):null,[$]);return p.viewport?t.exports.createElement(t.exports.Fragment,null,B&&t.exports.createElement(va,{__scopeToast:o,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0},B),t.exports.createElement(ma,{scope:o,onClose:N},Te.exports.createPortal(t.exports.createElement(ht.ItemSlot,{scope:o},t.exports.createElement(Mn,{asChild:!0,onEscapeKeyDown:y(c,()=>{p.isFocusedToastEscapeKeyDownRef.current||N(),p.isFocusedToastEscapeKeyDownRef.current=!1})},t.exports.createElement(A.li,E({role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":a?"open":"closed","data-swipe-direction":p.swipeDirection},v,{ref:m,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:y(e.onKeyDown,S=>{S.key==="Escape"&&(c==null||c(S.nativeEvent),S.nativeEvent.defaultPrevented||(p.isFocusedToastEscapeKeyDownRef.current=!0,N()))}),onPointerDown:y(e.onPointerDown,S=>{S.button===0&&(h.current={x:S.clientX,y:S.clientY})}),onPointerMove:y(e.onPointerMove,S=>{if(!h.current)return;const D=S.clientX-h.current.x,F=S.clientY-h.current.y,U=Boolean(w.current),X=["left","right"].includes(p.swipeDirection),W=["left","up"].includes(p.swipeDirection)?Math.min:Math.max,q=X?W(0,D):0,Y=X?0:W(0,F),Q=S.pointerType==="touch"?10:2,oe={x:q,y:Y},ne={originalEvent:S,delta:oe};U?(w.current=oe,Oe(fa,u,ne,{discrete:!1})):Mt(oe,p.swipeDirection,Q)?(w.current=oe,Oe(ua,f,ne,{discrete:!1}),S.target.setPointerCapture(S.pointerId)):(Math.abs(D)>Q||Math.abs(F)>Q)&&(h.current=null)}),onPointerUp:y(e.onPointerUp,S=>{const D=w.current,F=S.target;if(F.hasPointerCapture(S.pointerId)&&F.releasePointerCapture(S.pointerId),w.current=null,h.current=null,D){const U=S.currentTarget,X={originalEvent:S,delta:D};Mt(D,p.swipeDirection,p.swipeThreshold)?Oe($a,x,X,{discrete:!0}):Oe(pa,b,X,{discrete:!0}),U.addEventListener("click",W=>W.preventDefault(),{once:!0})}})})))),p.viewport))):null});Fo.propTypes={type(e){if(e.type&&!["foreground","background"].includes(e.type)){const n=`Invalid prop \`type\` supplied to \`${Ye}\`. Expected \`foreground | background\`.`;return new Error(n)}return null}};const va=e=>{const{__scopeToast:n,children:o,...r}=e,s=ze(Ye,n),[a,i]=t.exports.useState(!1),[c,l]=t.exports.useState(!1);return Sa(()=>i(!0)),t.exports.useEffect(()=>{const d=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(d)},[]),c?null:t.exports.createElement(dt,{asChild:!0},t.exports.createElement(Fe,r,a&&t.exports.createElement(t.exports.Fragment,null,s.label," ",o)))},ha=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,...r}=e;return t.exports.createElement(A.div,E({},r,{ref:n}))}),ga=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,...r}=e;return t.exports.createElement(A.div,E({},r,{ref:n}))}),Ea="ToastAction",Ho=t.exports.forwardRef((e,n)=>{const{altText:o,...r}=e;return o?t.exports.createElement(Ko,{altText:o,asChild:!0},t.exports.createElement(Vo,E({},r,{ref:n}))):null});Ho.propTypes={altText(e){return e.altText?null:new Error(`Missing prop \`altText\` expected on \`${Ea}\``)}};const wa="ToastClose",Vo=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,...r}=e,s=ba(wa,o);return t.exports.createElement(Ko,{asChild:!0},t.exports.createElement(A.button,E({type:"button"},r,{ref:n,onClick:y(e.onClick,s.onClose)})))}),Ko=t.exports.forwardRef((e,n)=>{const{__scopeToast:o,altText:r,...s}=e;return t.exports.createElement(A.div,E({"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0},s,{ref:n}))});function Wo(e){const n=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&n.push(r.textContent),Ca(r)){const s=r.ariaHidden||r.hidden||r.style.display==="none",a=r.dataset.radixToastAnnounceExclude==="";if(!s)if(a){const i=r.dataset.radixToastAnnounceAlt;i&&n.push(i)}else n.push(...Wo(r))}}),n}function Oe(e,n,o,{discrete:r}){const s=o.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:o});n&&s.addEventListener(e,n,{once:!0}),r?Ft(s,a):s.dispatchEvent(a)}const Mt=(e,n,o=0)=>{const r=Math.abs(e.x),s=Math.abs(e.y),a=r>s;return n==="left"||n==="right"?a&&r>o:!a&&s>o};function Sa(e=()=>{}){const n=z(e);J(()=>{let o=0,r=0;return o=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(n)),()=>{window.cancelAnimationFrame(o),window.cancelAnimationFrame(r)}},[n])}function Ca(e){return e.nodeType===e.ELEMENT_NODE}function ya(e){const n=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)n.push(o.currentNode);return n}function je(e){const n=document.activeElement;return e.some(o=>o===n?!0:(o.focus(),document.activeElement!==n))}const wc=ko,Sc=la,Cc=xa,yc=ha,Pc=ga,Tc=Ho,_c=Vo;export{Aa as $,fc as A,$c as B,pc as C,xc as D,rc as E,ac as F,bc as G,vc as H,hc as I,gc as J,Sc as K,Cc as L,Tc as M,_c as N,yc as O,Pc as P,wc as Q,Oa as a,Ia as b,Ra as c,Da as d,Ka as e,Fa as f,Ha as g,Va as h,he as i,Wa as j,Ua as k,za as l,ja as m,Za as n,ec as o,tc as p,Ja as q,Qa as r,qa as s,Ga as t,sc as u,cc as v,ic as w,lc as x,dc as y,uc as z};
