window.appConfig = {
  activeTheme: 'dark', // available themes: dark, light, twilight, cyber, neon, azure
  pageFonts: ['Times New Roman', 'Impact', 'Courier New', 'Comic Sans MS'], // used in page content editor
  gtaFonts: {
    // Fonts used for help point using SetTextFont native function
    // don't change unless you know how to load custom fonts into GTA
    // example: 0: "ChaletLondon", 0 is font ID for SetTextFont, "ChaletLondon" is font name shown in guidebook
    0: 'ChaletLondon',
    1: 'HouseScript',
    2: 'Monospace',
    4: 'CharletComprimeCologne',
    7: 'Pricedown'
  },
  themes: {
    dark: {
      primary: '#13181f',
      primaryLight: '#191f28',
      primaryDark: '#0b0f14',
      secondary: '#1D2228',
      secondaryLight: '#262d35',
      text: '#E1E2E2',
      textInactive: '#9ea7b8',
      accent: '#ff9100',
      accentDark: '#614010',
      error: '#c90707',
      success: '#36f920',
      dataRowIcon: '#afafaf',
      inputBg: '#1D2228',
      inputBorder: '#1D2228',
      inputText: '#E1E2E2',
      inputBgFocus: '#272c33',
      buttonSecondaryBg: '#e7ebf1',
      buttonSecondaryHover: '#f1f3f6',
      buttonSecondaryText: '#333',
      buttonGPSBg: '#e7ebf1',
      buttonGPSHover: '#f1f3f6',
      buttonGPSText: '#333',
      tooltipBg: '#e7ebf1',
      tooltipText: '#333',
      tooltipBorder: '#9ea7b8',
      editorBorder: '#414447',
      selectionBg: '#ff9100',
      selectionText: '#000',
      controlsShadow: '#080b0f',
      destructiveButtonBg: '#c90707',
      destructiveButtonHover: '#f44336',
      destructiveButtonText: '#FFFFFF'
    },
    light: {
      primary: '#E1E6EA',
      primaryLight: '#F0F4F7',
      primaryDark: '#C5CDD3',
      secondary: '#D6DEE4',
      secondaryLight: '#E5EBF0',
      text: '#333333',
      textInactive: '#6D737A',
      accent: '#ff9100',
      accentDark: '#C77C2F',
      error: '#c90707',
      success: '#36f920',
      dataRowIcon: '#50575E',
      inputBg: '#F0F4F7',
      inputBorder: '#D6DEE4',
      inputText: '#333333',
      inputBgFocus: '#E1E6EA',
      buttonSecondaryBg: '#333333',
      buttonSecondaryHover: '#4D4D4D',
      buttonSecondaryText: '#FFFFFF',
      buttonGPSBg: '#333333',
      buttonGPSHover: '#4D4D4D',
      buttonGPSText: '#FFFFFF',
      tooltipBg: '#333333',
      tooltipText: '#FFFFFF',
      tooltipBorder: '#6D737A',
      editorBorder: '#B0B6BC',
      selectionBg: '#ff9100',
      selectionText: '#FFFFFF',
      controlsShadow: '#C5CDD3',
      destructiveButtonBg: '#c90707',
      destructiveButtonHover: '#f44336',
      destructiveButtonText: '#FFFFFF'
    },
    twilight: {
      primary: '#1C1C28',
      primaryLight: '#282838',
      primaryDark: '#12121D',
      secondary: '#282838',
      secondaryLight: '#343449',
      text: '#CFCFE2',
      textInactive: '#8A8A9D',
      accent: '#AB47BC',
      accentDark: '#5E2759',
      error: '#c90707',
      success: '#36f920',
      dataRowIcon: '#50575E',
      inputBg: '#262330',
      inputBorder: '#2e2142',
      inputText: '#E1E2E2',
      inputBgFocus: '#272c33',
      buttonSecondaryBg: '#333333',
      buttonSecondaryHover: '#4D4D4D',
      buttonSecondaryText: '#FFFFFF',
      buttonGPSBg: '#e7ebf1',
      buttonGPSHover: '#f1f3f6',
      buttonGPSText: '#333',
      tooltipBg: '#333333',
      tooltipText: '#FFFFFF',
      tooltipBorder: '#6D737A',
      editorBorder: '#B0B6BC',
      selectionBg: '#ff9100',
      selectionText: '#FFFFFF',
      controlsShadow: '#080b0f',
      destructiveButtonBg: '#AB47BC',
      destructiveButtonHover: '#5E2759',
      destructiveButtonText: '#FFFFFF'
    },
    cyber: {
      primary: '#0C0C0C',
      primaryLight: '#191919',
      primaryDark: '#000000',
      secondary: '#191919',
      secondaryLight: '#262626',
      text: '#E1E1E1',
      textInactive: '#8A8A8A',
      accent: '#00FF00',
      accentDark: '#00CC00',
      error: '#c90707',
      success: '#36f920',
      dataRowIcon: '#50575E',
      inputBg: '#172420',
      inputBorder: '#2c6a40',
      inputText: '#E1E2E2',
      inputBgFocus: '#272c33',
      buttonSecondaryBg: '#333333',
      buttonSecondaryHover: '#4D4D4D',
      buttonSecondaryText: '#FFFFFF',
      buttonGPSBg: '#e7ebf1',
      buttonGPSHover: '#f1f3f6',
      buttonGPSText: '#333',
      tooltipBg: '#333333',
      tooltipText: '#FFFFFF',
      tooltipBorder: '#6D737A',
      editorBorder: '#B0B6BC',
      selectionBg: '#00FF00',
      selectionText: '#FFFFFF',
      controlsShadow: '#080b0f',
      destructiveButtonBg: '#00FF00',
      destructiveButtonHover: '#00CC00',
      destructiveButtonText: '#000'
    },
    neon: {
      primary: '#13181f',
      primaryLight: '#191f28',
      primaryDark: '#0b0f14',
      secondary: '#1D2228',
      secondaryLight: '#262d35',
      text: '#E1E2E2',
      textInactive: '#9ea7b8',
      accent: '#FF00FF',
      accentDark: '#B300B3',
      error: '#FF3333',
      success: '#33FF33',
      dataRowIcon: '#a900a9',
      inputBg: '#2c142c',
      inputBorder: '#FF00FF',
      inputText: '#E1E2E2',
      inputBgFocus: '#4D4D4D',
      buttonSecondaryBg: '#282828',
      buttonSecondaryHover: '#3B3B3B',
      buttonSecondaryText: '#D0D0D0',
      buttonGPSBg: '#00FFFF',
      buttonGPSHover: '#00B3B3',
      buttonGPSText: '#FFFFFF',
      tooltipBg: '#333333',
      tooltipText: '#D0D0D0',
      tooltipBorder: '#666666',
      editorBorder: '#666666',
      selectionBg: '#FF00FF',
      selectionText: '#D0D0D0',
      controlsShadow: '#1E1E1E',
      destructiveButtonBg: '#FF00FF',
      destructiveButtonHover: '#B300B3',
      destructiveButtonText: '#000'
    },
    azure: {
      primary: '#13181f',
      primaryLight: '#191f28',
      primaryDark: '#0b0f14',
      secondary: '#1D2228',
      secondaryLight: '#262d35',
      text: '#E1E2E2',
      textInactive: '#9ea7b8',
      accent: '#00C6FF',
      accentDark: '#0086B3',
      error: '#FF3333',
      success: '#33FF33',
      dataRowIcon: '#0086B3',
      inputBg: '#141c2c',
      inputBorder: '#2f4060',
      inputText: '#E1E2E2',
      inputBgFocus: '#4D4D4D',
      buttonSecondaryBg: '#e7ebf1',
      buttonSecondaryHover: '#f1f3f6',
      buttonSecondaryText: '#333',
      buttonGPSBg: '#e7ebf1',
      buttonGPSHover: '#f1f3f6',
      buttonGPSText: '#333',
      tooltipBg: '#333333',
      tooltipText: '#E1E2E2',
      tooltipBorder: '#666666',
      editorBorder: '#666666',
      selectionBg: '#0086B3',
      selectionText: '#f1f3f6',
      controlsShadow: '#1E1E1E',
      destructiveButtonBg: '#00C6FF',
      destructiveButtonHover: '#0086B3',
      destructiveButtonText: '#000'
    }
  }
};
