local function permissionName(name)
    return string.format('%s_%s', GetCurrentResourceName(), name)
end

Ace = {}
Ace.AddPrincipal = function(child, parent)
--     dbg.info('add_principal %s %s', child, parent)
--     ExecuteCommand(string.format('add_principal %s %s', child, parent))
end
Ace.RemovePrincipal = function(child, parent)
--     dbg.info('remove_principal %s %s', child, parent)
--     ExecuteCommand(string.format('remove_principal %s %s', child, parent))
end
Ace.Allow = function(identifier, resource)
--     dbg.info('add_ace %s %s allow', identifier, resource)
--     ExecuteCommand(string.format('add_ace %s %s allow', identifier, permissionName(resource)))
end
Ace.Deny = function(identifier, resource)
--     dbg.info('add_ace %s %s deny', identifier, resource)
--     ExecuteCommand(string.format('add_ace %s %s deny', identifier, permissionName(resource)))
end
Ace.Can = function(source, rule)
  if PermissionMap['group.admin'] and table.contains(PermissionMap['group.admin'], rule) then
    local character = exports.blrp_core:character(source)
    return character.hasOrInheritsGroup('admin') and not exports.blrp_tablet:staffMuted(character.get('identifier'))
  end

  return true
end

Ace.CanGroup = function(group, rule)
  if group == 'group.admin' then
    local character = exports.blrp_core:character(source)
    return character.hasOrInheritsGroup('admin') and not exports.blrp_tablet:staffMuted(character.get('identifier'))
  end

  return true
end

-- Helper function to check if a table contains a value
function table.contains(table, element)
  for _, value in pairs(table) do
    if value == element then
      return true
    end
  end
  return false
end
