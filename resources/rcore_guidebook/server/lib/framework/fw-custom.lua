CreateThread(function()
    if Config.Framework ~= nil and Config.Framework ~= 1 and Config.Framework ~= 2 then
        ShowNotification = function(src, msg)
            return
        end

        GetPlayersJobName = function(serverId)
            return nil
        end

        GetPlayersJobGrade = function(serverId)
            return nil
        end

        GetAllServerJobs = function()
            if allServerJobs then
                return allServerJobs
            end

            local rawJobs = exports.vrp:GetGroupDefinitions()
            local jobList = {}
            local otherList = {}

            -- Helper function to check if a job should be removed
            local function isJobRemoved(jobData)
                return jobData._config and jobData._config.removed == true
            end

            -- Separate jobs with gtype = "job" and others while filtering out removed jobs
            for jobKey, jobData in pairs(rawJobs) do
                if not isJobRemoved(jobData) then
                    local grades = {
                        [0] = {
                            name = "Default",
                            key = 0,
                        }
                    }

                    local jobEntry = {
                        name = jobKey,
                        grades = grades,
                        isJob = jobData._config and jobData._config.gtype == "job"
                    }

                    if jobEntry.isJob then
                        table.insert(jobList, jobEntry)
                    else
                        table.insert(otherList, jobEntry)
                    end
                end
            end

            -- Sort jobs alphabetically
            table.sort(jobList, function(a, b) return a.name < b.name end)
            table.sort(otherList, function(a, b) return a.name < b.name end)

            -- Merge the two lists
            local sortedJobs = {}
            for _, job in ipairs(jobList) do
                sortedJobs[job.name] = job
            end
            for _, job in ipairs(otherList) do
                sortedJobs[job.name] = job
            end

            allServerJobs = sortedJobs
            return sortedJobs
        end
    end
end)
