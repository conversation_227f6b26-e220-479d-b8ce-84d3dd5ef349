--Do not edit any values/keys in this file.

Frameworks = {
    ESX = 'ESX',
    QBCORE = 'QBCORE',
    STANDALONE = 'STANDALONE',
}

LogLevel = {
    INFO = 'info',
    CRITICAL = 'critical',
    PERMISSION = 'permission',
}

RoomState = {
    OPEN = 1,
    CLOSED = 0,
}

DataTypes = {
    PAGE = 'PAGE',
    CATEGORY = 'CATEGORY',
    TELEPORT = 'TELEPORT',
    POINT = 'POINT',
    SEARCH = 'SEARCH',
}

Permissions = {
    OPEN_PAGE = 'page.open',             --open help pages
    OPEN_CONTROL = 'page.open_control',  --Open administration of documentation
    EDIT_PAGE = 'page.edit',             --edit page for admin
    CREATE_PAGE = 'page.create',         --create new page
    DELETE_PAGE = 'page.delete',         --delete help page
    CREATE_CATEGORY = 'category.create', --Create category for pages
    EDIT_CATEGORY = 'category.edit',     --Create category for pages
    DELETE_CATEGORY = 'category.delete', --Delete category
    SEND_HELP = 'page.send',             --Send help
    CREATE_POINT = 'point.create',
    EDIT_POINT = 'point.edit',
    DELETE_POINT = 'point.delete',
    TELEPORT = 'teleport',
    NAVIGATE = 'navigate',
}

GameType = {
    FIVEM = 'fivem',
    REDM = 'redm',
}
