function HasPermission(attributes, playerSource, isAdmin)
  if Config.DisableDataPermissions or (isAdmin and Config.DisableDataPermissionsForAdmin) then
      return true
  end

  if not attributes then
      return true
  end

  if not attributes.permissions or not attributes.permissions.enabled or not attributes.permissions.jobs or not #attributes.permissions.jobs then
      return true
  end

  if not playerSource and not attributes then
      return true
  end

  if not playerSource then
      return false
  end

  local character = exports.blrp_core:character(playerSource)
  -- check if attributes.permissions.jobs contains any job with jobName == playerJob.name
  local job = false

  for _, jobData in pairs(attributes.permissions.jobs) do
      job = character.hasOrInheritsGroup(jobData.jobName)
      print('checking job', jobData.jobName, jobData.jobName, job)
      if job then
        break
      end
  end

  return job and job
end
