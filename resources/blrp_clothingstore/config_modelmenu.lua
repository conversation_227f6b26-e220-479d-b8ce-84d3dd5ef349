config_modelmenu = {
  locations = {
    { coords = vector3(-274.832,6226.528,31.696), }, -- Paleto Bay
    { coords = vector3(1928.808,3732.760,32.845), }, -- Sandy Shores
    { coords = vector3(-815.260, -187.800, 37.569), }, -- Rockford Hills
    { coords = vector3(-33.103,-156.183,57.077), }, -- <PERSON>wick
    { coords = vector3(-1279.266,-1116.008,6.990), }, -- V<PERSON><PERSON>cci Canals
    { coords = vector3(1216.093,-472.511,66.208), }, -- <PERSON>row Park
    { coords = vector3(138.214,-1704.530,29.292), }, -- <PERSON>
    { coords = vector3(-1405.869, -1021.562, 23.244), }, -- Admin Shop
  },

  categories = {
    ['Multiplayer Models'] = {
      'mp_m_freemode_01',
      'mp_f_freemode_01',
    },

    ['Male Models'] = {
      'mp_m_g_vagfun_01',
      'a_m_m_afriamer_01',
      'a_m_m_beach_01',
      'a_m_m_beach_02',
      'a_m_m_bevhills_01',
      'a_m_m_bevhills_02',
      'a_m_m_business_01',
      'a_m_m_eastsa_01',
      'a_m_m_eastsa_02',
      'a_m_m_fatlatin_01',
      'a_m_m_genfat_01',
      'a_m_m_genfat_02',
      'a_m_m_golfer_01',
      'a_m_m_hillbilly_02',
      'a_m_m_indian_01',
      'a_m_m_ktown_01',
      'a_m_m_malibu_01',
      'a_m_m_mexcntry_01',
      'a_m_m_mexlabor_01',
      'a_m_m_og_boss_01',
      'a_m_m_paparazzi_01',
      'a_m_m_polynesian_01',
      'a_m_m_rurmeth_01',
      'a_m_m_salton_01',
      'a_m_m_salton_02',
      'a_m_m_salton_03',
      'a_m_m_salton_04',
      'a_m_m_skater_01',
      'a_m_m_skidrow_01',
      'a_m_m_socenlat_01',
      'a_m_m_soucent_01',
      'a_m_m_soucent_02',
      'a_m_m_soucent_03',
      'a_m_m_soucent_04',
      'a_m_m_stlat_02',
      'a_m_m_tennis_01',
      'a_m_m_tourist_01',
      'a_m_m_tramp_01',
      'a_m_m_trampbeac_01',
      'a_m_m_tranvest_01',
      'a_m_m_tranvest_02',
      'a_m_o_beach_01',
      'a_m_o_genstreet_01',
      'a_m_o_ktown_01',
      'a_m_o_salton_01',
      'a_m_o_soucent_01',
      'a_m_o_soucent_02',
      'a_m_o_soucent_03',
      'a_m_o_tramp_01',
      'a_m_y_beach_01',
      'a_m_y_beach_02',
      'a_m_y_beach_03',
      'a_m_y_beachvesp_01',
      'a_m_y_beachvesp_02',
      'a_m_y_bevhills_01',
      'a_m_y_bevhills_02',
      'a_m_y_breakdance_01',
      'a_m_y_busicas_01',
      'a_m_y_business_01',
      'a_m_y_business_02',
      'a_m_y_business_03',
      'a_m_y_cyclist_01',
      'a_m_y_dhill_01',
      'a_m_y_downtown_01',
      'a_m_y_eastsa_01',
      'a_m_y_eastsa_02',
      'a_m_y_epsilon_01',
      'a_m_y_epsilon_02',
      'a_m_y_gay_01',
      'a_m_y_gay_02',
      'a_m_y_genstreet_01',
      'a_m_y_genstreet_02',
      'a_m_y_golfer_01',
      'a_m_y_hasjew_01',
      'a_m_y_hiker_01',
      'a_m_y_hippy_01',
      'a_m_y_hipster_01',
      'a_m_y_hipster_02',
      'a_m_y_hipster_03',
      'a_m_y_indian_01',
      'a_m_y_jetski_01',
      'a_m_y_juggalo_01',
      'a_m_y_ktown_01',
      'a_m_y_ktown_02',
      'a_m_y_latino_01',
      'a_m_y_mexthug_01',
      'a_m_y_motox_01',
      'a_m_y_motox_02',
      'a_m_y_musclbeac_01',
      'a_m_y_musclbeac_02',
      'a_m_y_polynesian_01',
      'a_m_y_roadcyc_01',
      'a_m_y_runner_01',
      'a_m_y_runner_02',
      'a_m_y_salton_01',
      'a_m_y_skater_01',
      'a_m_y_skater_02',
      'a_m_y_soucent_01',
      'a_m_y_soucent_02',
      'a_m_y_soucent_03',
      'a_m_y_soucent_04',
      'a_m_y_stbla_01',
      'a_m_y_stbla_02',
      'a_m_y_stlat_01',
      'a_m_y_stwhi_01',
      'a_m_y_stwhi_02',
      'a_m_y_sunbathe_01',
      'a_m_y_surfer_01',
      'a_m_y_vindouche_01',
      'a_m_y_vinewood_01',
      'a_m_y_vinewood_02',
      'a_m_y_vinewood_03',
      'a_m_y_vinewood_04',
      'a_m_y_yoga_01',
      'cs_bankman',
      'cs_barry',
      'cs_beverly',
      'cs_brad',
      'cs_carbuyer',
      'cs_chengsr',
      'cs_chrisformage',
      'cs_clay',
      'cs_dale',
      'cs_davenorton',
      'cs_devin',
      'cs_dom',
      'cs_dreyfuss',
      'cs_drfriedlander',
      'cs_fabien',
      'cs_floyd',
      'cs_hunter',
      'cs_jimmyboston',
      'cs_jimmydisanto',
      'cs_joeminuteman',
      'cs_johnnyklebitz',
      'cs_josef',
      'cs_josh',
      'cs_lazlow',
      'cs_lestercrest',
      'cs_lifeinvad_01',
      'cs_manuel',
      'cs_martinmadrazo',
      'cs_milton',
      'cs_movpremmale',
      'cs_mrk',
      'cs_nervousron',
      'cs_nigel',
      'cs_old_man1a',
      'cs_old_man2',
      'cs_omega',
      'cs_orleans',
      'cs_paper',
      'cs_priest',
      'cs_prolsec_02',
      'cs_russiandrunk',
      'cs_siemonyetarian',
      'cs_solomon',
      'cs_stevehains',
      'cs_stretch',
      'cs_taocheng',
      'cs_taostranslator',
      'cs_tenniscoach',
      'cs_terry',
      'cs_tom',
      'cs_tomepsilon',
      'cs_wade',
      'cs_zimbor',
      'csb_anton',
      'csb_ballasog',
      'csb_burgerdrug',
      'csb_car3guy1',
      'csb_car3guy2',
      'csb_chef',
      'csb_chin_goon',
      'csb_cletus',
      'csb_customer',
      'csb_fos_rep',
      'csb_g',
      'csb_groom',
      'csb_grove_str_dlr',
      'csb_hao',
      'csb_hugh',
      'csb_imran',
      'csb_janitor',
      'csb_ortega',
      'csb_oscar',
      'csb_porndudes',
      'csb_prologuedriver',
      'csb_ramp_gang',
      'csb_ramp_hic',
      'csb_ramp_hipster',
      'csb_ramp_mex',
      'csb_reporter',
      'csb_roccopelosi',
      'csb_trafficwarden',
      'g_m_m_armboss_01',
      'g_m_m_armgoon_01',
      'g_m_m_armlieut_01',
      'g_m_m_chemwork_01',
      'g_m_m_chiboss_01',
      'g_m_m_chicold_01',
      'g_m_m_chigoon_01',
      'g_m_m_chigoon_02',
      'g_m_m_korboss_01',
      'g_m_m_mexboss_01',
      'g_m_m_mexboss_02',
      'g_m_y_armgoon_02',
      'g_m_y_azteca_01',
      'g_m_y_ballaeast_01',
      'g_m_y_ballaorig_01',
      'g_m_y_ballasout_01',
      'g_m_y_famca_01',
      'g_m_y_famdnf_01',
      'g_m_y_famfor_01',
      'g_m_y_korean_01',
      'g_m_y_korean_02',
      'g_m_y_korlieut_01',
      'g_m_y_lost_01',
      'g_m_y_lost_02',
      'g_m_y_lost_03',
      'g_m_y_mexgang_01',
      'g_m_y_mexgoon_01',
      'g_m_y_mexgoon_02',
      'g_m_y_mexgoon_03',
      'g_m_y_pologoon_01',
      'g_m_y_pologoon_02',
      'g_m_y_salvaboss_01',
      'g_m_y_salvagoon_01',
      'g_m_y_salvagoon_02',
      'g_m_y_salvagoon_03',
      'g_m_y_strpunk_01',
      'g_m_y_strpunk_02',
      'hc_driver',
      'hc_gunman',
      'hc_hacker',
      's_m_m_ammucountry',
      's_m_m_autoshop_01',
      's_m_m_autoshop_02',
      's_m_m_bouncer_01',
      's_m_m_cntrybar_01',
      's_m_m_dockwork_01',
      's_m_m_doctor_01',
      's_m_m_gaffer_01',
      's_m_m_gardener_01',
      's_m_m_gentransport',
      's_m_m_hairdress_01',
      's_m_m_highsec_01',
      's_m_m_highsec_02',
      's_m_m_janitor',
      's_m_m_lathandy_01',
      's_m_m_lifeinvad_01',
      's_m_m_linecook',
      's_m_m_lsmetro_01',
      's_m_m_mariachi_01',
      's_m_m_migrant_01',
      's_m_m_movprem_01',
      's_m_m_movspace_01',
      's_m_m_pilot_01',
      's_m_m_pilot_02',
      's_m_m_postal_01',
      's_m_m_postal_02',
      's_m_m_scientist_01',
      's_m_m_strperf_01',
      's_m_m_strpreach_01',
      's_m_m_strvend_01',
      's_m_m_trucker_01',
      's_m_m_ups_01',
      's_m_m_ups_02',
      's_m_o_busker_01',
      's_m_y_airworker',
      's_m_y_ammucity_01',
      's_m_y_armymech_01',
      's_m_y_autopsy_01',
      's_m_y_barman_01',
      's_m_y_baywatch_01',
      's_m_y_busboy_01',
      's_m_y_chef_01',
      's_m_y_clown_01',
      's_m_y_construct_01',
      's_m_y_construct_02',
      's_m_y_dealer_01',
      's_m_y_devinsec_01',
      's_m_y_dockwork_01',
      's_m_y_dwservice_01',
      's_m_y_dwservice_02',
      's_m_y_factory_01',
      's_m_y_garbage',
      's_m_y_grip_01',
      's_m_y_mime',
      's_m_y_pestcont_01',
      's_m_y_pilot_01',
      's_m_y_prismuscl_01',
      's_m_y_robber_01',
      's_m_y_shop_mask',
      's_m_y_strvend_01',
      's_m_y_uscg_01',
      's_m_y_valet_01',
      's_m_y_waiter_01',
      's_m_y_winclean_01',
      's_m_y_xmech_01',
      's_m_y_xmech_02',
      'u_m_m_aldinapoli',
      'u_m_m_bankman',
      'u_m_m_bikehire_01',
      'u_m_m_filmdirector',
      'u_m_m_glenstank_01',
      'u_m_m_griff_01',
      'u_m_m_jesus_01',
      'u_m_m_jewelsec_01',
      'u_m_m_jewelthief',
      'u_m_m_markfost',
      'u_m_m_partytarget',
      'u_m_m_promourn_01',
      'u_m_m_rivalpap',
      'u_m_m_spyactor',
      'u_m_m_willyfist',
      'u_m_o_finguru_01',
      'u_m_o_taphillbilly',
      'u_m_o_tramp_01',
      'u_m_y_abner',
      'u_m_y_antonb',
      'u_m_y_babyd',
      'u_m_y_baygor',
      'u_m_y_burgerdrug_01',
      'u_m_y_chip',
      'u_m_y_cyclist_01',
      'u_m_y_guido_01',
      'u_m_y_gunvend_01',
      'u_m_y_hippie_01',
      'u_m_y_imporage',
      'u_m_y_mani',
      'u_m_y_militarybum',
      'u_m_y_paparazzi',
      'u_m_y_party_01',
      'u_m_y_pogo_01',
      'u_m_y_proldriver_01',
      'u_m_y_rsranger_01',
      'u_m_y_sbike',
      'u_m_y_staggrm_01',
      'u_m_y_tattoo_01',
      'zombie',
      'zombie2',
      'zombie3',
      'zombie4',
      'zombie5',
      'csb_anton', --m
      'csb_ballasog', --m
      'csb_burgerdrug', --m
      'csb_car3guy1', --m
      'csb_car3guy2', --m
      'csb_chef', --m
      'csb_chin_goon', --m
      'csb_cletus', --m
      'csb_customer', --m
      'csb_fos_rep', --m
      'csb_g', --m
      'csb_groom', --m
      'csb_grove_str_dlr', --m
      'csb_hao', --m
      'csb_hugh', --m
      'csb_imran', --m
      'csb_janitor', --m
      'csb_ortega', --m
      'csb_oscar', --m
      'csb_porndudes', --m
      'csb_prologuedriver', --m
      'csb_ramp_gang',  --m
      'csb_ramp_hic', --m
      'csb_ramp_hipster', --m
      'csb_ramp_mex', --m
      'csb_reporter', --m
      'csb_roccopelosi', --m
      'csb_trafficwarden', --m jobs
      'cs_bankman', --m
      'cs_barry', --m
      'cs_beverly', --m
      'cs_brad', --m
      'cs_carbuyer', --m
      'cs_chengsr', --m
      'cs_chrisformage', --m
      'cs_clay', --m
      'cs_dale', --m
      'cs_davenorton', --m
      'cs_devin', --m
      'cs_dom', --m
      'cs_dreyfuss', --m
      'cs_drfriedlander', --m
      'cs_fabien', --m
      'cs_floyd', --m
      'cs_hunter', --m
      'cs_jimmyboston', --m
      'cs_jimmydisanto', --m
      'cs_joeminuteman', --m
      'cs_johnnyklebitz', --m
      'cs_josef', --m
      'cs_josh', --m
      'cs_lazlow', --m
      'cs_lestercrest', --m
      'cs_lifeinvad_01', --m
      'cs_manuel', --m
      'cs_martinmadrazo', --m
      'cs_milton', --m
      'cs_movpremmale', --m
      'cs_mrk', --m
      'cs_nervousron', --m
      'cs_nigel', --m
      'cs_old_man1a', --m
      'cs_old_man2', --m
      'cs_omega', --m
      'cs_orleans', --m
      'cs_paper', --m
      'cs_priest', --m
      'cs_prolsec_02', --m
      'cs_russiandrunk', --m
      'cs_siemonyetarian', --m
      'cs_solomon', --m
      'cs_stevehains', --m
      'cs_stretch', --m
      'cs_taocheng', --m
      'cs_taostranslator', --m
      'cs_tenniscoach', --m
      'cs_terry', --m
      'cs_tom', --m
      'cs_tomepsilon', --m
      'cs_wade', --m
      'cs_zimbor', --m
      'g_m_m_armboss_01',
      'g_m_m_armgoon_01',
      'g_m_m_armlieut_01',
      'g_m_m_chemwork_01',
      'g_m_m_chiboss_01',
      'g_m_m_chicold_01',
      'g_m_m_chigoon_01',
      'g_m_m_chigoon_02',
      'g_m_m_korboss_01',
      'g_m_m_mexboss_01',
      'g_m_m_mexboss_02',
      'g_m_y_armgoon_02',
      'g_m_y_azteca_01',
      'g_m_y_ballaeast_01',
      'g_m_y_ballaorig_01',
      'g_m_y_ballasout_01',
      'g_m_y_famca_01',
      'g_m_y_famdnf_01',
      'g_m_y_famfor_01',
      'g_m_y_korean_01',
      'g_m_y_korean_02',
      'g_m_y_korlieut_01',
      'g_m_y_lost_01',
      'g_m_y_lost_02',
      'g_m_y_lost_03',
      'g_m_y_mexgang_01',
      'g_m_y_mexgoon_01',
      'g_m_y_mexgoon_02',
      'g_m_y_mexgoon_03',
      'g_m_y_pologoon_01',
      'g_m_y_pologoon_02',
      'g_m_y_salvaboss_01',
      'g_m_y_salvagoon_01',
      'g_m_y_salvagoon_02',
      'g_m_y_salvagoon_03',
      'g_m_y_strpunk_01',
      'g_m_y_strpunk_02',
      'hc_driver', --m
      'hc_gunman', --m
      'hc_hacker', --m
      's_m_m_ammucountry',
      's_m_m_autoshop_01',
      's_m_m_autoshop_02',
      's_m_m_bouncer_01',
      's_m_m_cntrybar_01',
      's_m_m_dockwork_01',
      's_m_m_doctor_01',
      's_m_m_gaffer_01',
      's_m_m_gardener_01',
      's_m_m_gentransport',
      's_m_m_hairdress_01',
      's_m_m_highsec_01',
      's_m_m_highsec_02',
      's_m_m_janitor',
      's_m_m_lathandy_01',
      's_m_m_lifeinvad_01',
      's_m_m_linecook',
      's_m_m_lsmetro_01',
      's_m_m_mariachi_01',
      's_m_m_migrant_01',
      's_m_m_movprem_01',
      's_m_m_movspace_01',
      's_m_m_pilot_01',
      's_m_m_pilot_02',
      's_m_m_postal_01',
      's_m_m_postal_02',
      's_m_m_scientist_01',
      's_m_m_strperf_01',
      's_m_m_strpreach_01',
      's_m_m_strvend_01',
      's_m_m_trucker_01',
      's_m_m_ups_01',
      's_m_m_ups_02',
      's_m_o_busker_01',
      's_m_y_airworker',
      's_m_y_ammucity_01',
      's_m_y_armymech_01',
      's_m_y_autopsy_01',
      's_m_y_barman_01',
      's_m_y_baywatch_01',
      's_m_y_busboy_01',
      's_m_y_chef_01',
      's_m_y_clown_01',
      's_m_y_construct_01',
      's_m_y_construct_02',
      's_m_y_dealer_01',
      's_m_y_devinsec_01',
      's_m_y_dockwork_01',
      's_m_y_dwservice_01',
      's_m_y_dwservice_02',
      's_m_y_factory_01',
      's_m_y_garbage',
      's_m_y_grip_01',
      's_m_y_mime',
      's_m_y_pestcont_01',
      's_m_y_pilot_01',
      's_m_y_prismuscl_01',
      's_m_y_robber_01',
      's_m_y_shop_mask',
      's_m_y_strvend_01',
      's_m_y_uscg_01',
      's_m_y_valet_01',
      's_m_y_waiter_01',
      's_m_y_winclean_01',
      's_m_y_xmech_01',
      's_m_y_xmech_02',
      'u_m_m_aldinapoli',
      'u_m_m_bankman',
      'u_m_m_bikehire_01',
      'u_m_m_filmdirector',
      'u_m_m_glenstank_01',
      'u_m_m_griff_01',
      'u_m_m_jesus_01',
      'u_m_m_jewelsec_01',
      'u_m_m_jewelthief',
      'u_m_m_markfost',
      'u_m_m_partytarget',
      'u_m_m_promourn_01',
      'u_m_m_rivalpap',
      'u_m_m_spyactor',
      'u_m_m_willyfist',
      'u_m_o_finguru_01',
      'u_m_o_taphillbilly',
      'u_m_o_tramp_01',
      'u_m_y_abner',
      'u_m_y_antonb',
      'u_m_y_babyd',
      'u_m_y_baygor',
      'u_m_y_burgerdrug_01',
      'u_m_y_chip',
      'u_m_y_cyclist_01',
      'u_m_y_guido_01',
      'u_m_y_gunvend_01',
      'u_m_y_hippie_01',
      'u_m_y_imporage',
      'u_m_y_mani',
      'u_m_y_militarybum',
      'u_m_y_paparazzi',
      'u_m_y_party_01',
      'u_m_y_proldriver_01',
      'u_m_y_rsranger_01',
      'u_m_y_sbike',
      'u_m_y_staggrm_01',
      'u_m_y_tattoo_01',
    },

    ['Female Models'] = {
      'a_f_m_beach_01',
      'a_f_m_bevhills_01',
      'a_f_m_bevhills_02',
      'a_f_m_bodybuild_01',
      'a_f_m_business_02',
      'a_f_m_downtown_01',
      'a_f_m_eastsa_01',
      'a_f_m_eastsa_02',
      'a_f_m_fatbla_01',
      'a_f_m_fatwhite_01',
      'a_f_m_ktown_01',
      'a_f_m_ktown_02',
      'a_f_m_prolhost_01',
      'a_f_m_salton_01',
      'a_f_m_skidrow_01',
      'a_f_m_soucent_01',
      'a_f_m_soucent_02',
      'a_f_m_soucentmc_01',
      'a_f_m_tourist_01',
      'a_f_m_tramp_01',
      'a_f_m_trampbeac_01',
      'a_f_o_genstreet_01',
      'a_f_o_indian_01',
      'a_f_o_ktown_01',
      'a_f_o_salton_01',
      'a_f_o_soucent_01',
      'a_f_o_soucent_02',
      'a_f_y_beach_01',
      'a_f_y_bevhills_01',
      'a_f_y_bevhills_02',
      'a_f_y_bevhills_03',
      'a_f_y_bevhills_04',
      'a_f_y_business_01',
      'a_f_y_business_02',
      'a_f_y_business_03',
      'a_f_y_business_04',
      'a_f_y_eastsa_01',
      'a_f_y_eastsa_02',
      'a_f_y_eastsa_03',
      'a_f_y_epsilon_01',
      'a_f_y_fitness_01',
      'a_f_y_fitness_02',
      'a_f_y_genhot_01',
      'a_f_y_golfer_01',
      'a_f_y_hiker_01',
      'a_f_y_hippie_01',
      'a_f_y_hipster_01',
      'a_f_y_hipster_02',
      'a_f_y_hipster_03',
      'a_f_y_hipster_04',
      'a_f_y_indian_01',
      'a_f_y_juggalo_01',
      'a_f_y_runner_01',
      'a_f_y_rurmeth_01',
      'a_f_y_scdressy_01',
      'a_f_y_skater_01',
      'a_f_y_soucent_01',
      'a_f_y_soucent_02',
      'a_f_y_soucent_03',
      'a_f_y_tennis_01',
      'a_f_y_tourist_01',
      'a_f_y_tourist_02',
      'a_f_y_vinewood_01',
      'a_f_y_vinewood_02',
      'a_f_y_vinewood_03',
      'a_f_y_vinewood_04',
      'a_f_y_yoga_01',
      'cs_amandatownley',
      'cs_ashley',
      'cs_debra',
      'cs_denise',
      'cs_guadalope',
      'cs_gurk',
      'cs_janet',
      'cs_jewelass',
      'cs_magenta',
      'cs_marnie',
      'cs_maryann',
      'cs_michelle',
      'cs_molly',
      'cs_movpremf_01',
      'cs_mrs_thornhill',
      'cs_mrsphillips',
      'cs_natalia',
      'cs_patricia',
      'cs_tanisha',
      'csb_abigail',
      'csb_anita',
      'csb_denise_friend',
      'csb_maude',
      'csb_screen_writer',
      'csb_tonya',
      'g_f_y_ballas_01',
      'g_f_y_families_01',
      'g_f_y_lost_01',
      'g_f_y_vagos_01',
      's_f_m_fembarber',
      's_f_m_maid_01',
      's_f_m_shop_high',
      's_f_m_sweatshop_01',
      's_f_y_airhostess_01',
      's_f_y_bartender_01',
      's_f_y_baywatch_01',
      's_f_y_factory_01',
      's_f_y_hooker_01',
      's_f_y_hooker_02',
      's_f_y_hooker_03',
      's_f_y_migrant_01',
      's_f_y_movprem_01',
      's_f_y_shop_low',
      's_f_y_shop_mid',
      's_f_y_sweatshop_01',
      'u_f_m_corpse_01',
      'u_f_m_miranda',
      'u_f_m_promourn_01',
      'u_f_o_moviestar',
      'u_f_o_prolhost_01',
      'u_f_y_bikerchic',
      'u_f_y_comjane',
      'u_f_y_corpse_01',
      'u_f_y_hotposh_01',
      'u_f_y_jewelass_01',
      'u_f_y_mistress',
      'u_f_y_poppymich',
      'u_f_y_princess',
      'u_f_y_spyactress',
      'cs_tanisha', --f
      'cs_patricia', --f
      'cs_mrsphillips', --f
      'cs_mrs_thornhill', --f
      'cs_natalia', --f
      'cs_molly', --f
      'cs_movpremf_01', --f
      'cs_maryann', --f
      'cs_michelle', --f
      'cs_marnie', --f
      'cs_magenta', --f
      'cs_janet', --f
      'cs_jewelass', --f
      'cs_guadalope', --f
      'cs_gurk',  --f
      'cs_debra', --f
      'cs_denise', --f
      'cs_amandatownley', --f
      'cs_ashley', --f
      'csb_screen_writer', --f
      'csb_tonya', --f
      'csb_maude', --f
      'csb_denise_friend', --f
      'csb_abigail', --f
      'csb_anita', --f
      'g_f_y_ballas_01',
      'g_f_y_families_01',
      'g_f_y_lost_01',
      'g_f_y_vagos_01',
      's_f_m_fembarber',
      's_f_m_maid_01',
      's_f_m_shop_high',
      's_f_m_sweatshop_01',
      's_f_y_airhostess_01',
      's_f_y_bartender_01',
      's_f_y_baywatch_01',
      's_f_y_factory_01',
      's_f_y_hooker_01',
      's_f_y_hooker_02',
      's_f_y_hooker_03',
      's_f_y_migrant_01',
      's_f_y_movprem_01',
      's_f_y_shop_low',
      's_f_y_shop_mid',
      's_f_y_sweatshop_01',
      'u_f_m_corpse_01',
      'u_f_m_miranda',
      'u_f_m_promourn_01',
      'u_f_o_moviestar',
      'u_f_o_prolhost_01',
      'u_f_y_bikerchic',
      'u_f_y_comjane',
      'u_f_y_corpse_01',
      'u_f_y_hotposh_01',
      'u_f_y_jewelass_01',
      'u_f_y_mistress',
      'u_f_y_poppymich',
      'u_f_y_princess',
      'u_f_y_spyactress',
    },

    ['Animal Models'] = {
      'a_c_cat_01',
      'a_c_chickenhawk',
      'a_c_chop',
      'a_c_cow',
      'a_c_coyote',
      'a_c_crow',
      'a_c_deer',
      'a_c_fish',
      'a_c_hen',
      'a_c_husky',
      'a_c_mtlion',
      'a_c_pig',
      'a_c_pigeon',
      'a_c_poodle',
      'a_c_pug',
      'a_c_rat',
      'a_c_retriever',
      'a_c_rhesus',
      'a_c_rottweiler',
      'a_c_westy',
      'a_c_dolphin',
      'a_c_seagull',
      'a_c_killerwhale',
      'a_c_sharktiger',
      's_m_m_movspace_01',
    },

    ['Admin Models'] = {
      'pennywise',
      'ig_mrsphillips',
      'ig_michelle',
      'ig_ashley',
      'a_c_shepherd',
      'a_c_dalmatian',
      's_f_y_cop_01',
      's_f_y_ranger_01',
      's_f_y_sheriff_01',
      'serp_cop_01',
      'skeleton',
      'u_m_y_zombie_01',
      'jason',
      'mmyers',
      'leatherface',
      'dem',
      'nasrithdemon',
      'peter_venkman',
      'ray_stantz',
      'slimer',
      'Nc_Pepe',
      'S_M_M_Armoured_03',
      'Miss_Fortune',
      'Ghost',
      'chucky',
      'tiffany',
      'executioner',
      'Pig',
      'robot_dog',
      'S_M_M_MovAlien_01',
      'a_c_capybara',
    },

    ['Custom Models'] = {
      'ems_pedtaint_01',
      'a_m_y_methhead_01',
      'junkieaddonped',
      'ThePredator', -- halloween custome for 30390
      'chucky',
      'executioner',
      'cs_tracydisanto',
      'ig_tracydisanto',
      'a_c_chop',
      'pw_midget_carter',
      'pw_midget_andreas',
      'a_m_m_farmer_01',
    },

    ['Event Peds'] = {
      'pennywise',
      'skeleton',
      'u_m_y_zombie_01',
      'jason',
      'mmyers',
      'leatherface',
      'dem',
      'nasrithdemon',
      'peter_venkman',
      'ray_stantz',
      'slimer',
      'Ghost',
      'chucky',
      'tiffany',
      'executioner',
      'Pig',
      'ThePredator',
    },

    ['Jeery Peds'] = {
      'a_m_m_hasjew_01', --jerrys ped jewish
      'a_m_m_hillbilly_01', --jerrys ped
    }
  },

  -- Non-staff who have access to custom ped category
  custom_access = {
    [58794] = true,
    [59097] = true,
    [30390] = true,
    [77826] = true,
    [90238] = true,
    [138928] = true,
    [116946] = true,
  },

  jeery_ped = { -- Blacklisted peds for chair id
    [12454] = true,
  }
}
