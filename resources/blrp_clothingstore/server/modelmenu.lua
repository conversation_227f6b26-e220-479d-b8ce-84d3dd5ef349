RegisterNetEvent('blrp_clothingstore:server:openModelMenu', function(player)
  if not player then
    player = source
  end

  local character = exports.blrp_core:character(player)

  local menu = BMenu:new(true, {
    resource_title = 'Character Model',
    section_title = 'Main Menu',
    title_bg_image = '',
    position = { top_offset = 500, left_offset = 10 },
    max_items_shown_at_once = 12,
    prevent_sound = true,
    width = 280,
  })

  local function modelCallback(model_string)
    local success, message = tClothingStore.changePedModel(character.source, { model_string })

    if not success then
      return
    end

    character.notify(message)
  end

  local categories = {
    'Multiplayer Models',
    'Male Models',
    'Female Models',
  }

  if character.hasGroup('staff') then
    table.insert(categories, 'Animal Models')
    table.insert(categories, 'Admin Models')
  end

  if character.hasGroup('Event Team') then
    table.insert(categories, 'Event Peds')
  end

  if character.hasGroup('staff') or config_modelmenu.custom_access[tonumber(character.get('identifier'))] then
    table.insert(categories, 'Custom Models')
  end

  if character.hasGroup('staff') or config_modelmenu.jeery_ped[tonumber(character.get('id'))] then
    table.insert(categories, 'Jeery Peds')
  end

  for _, category in ipairs(categories) do
    local models = config_modelmenu.categories[category]

    menu:addSubMenu(false, category)

    for _, model_string in pairs(models) do
      menu:addSelection(category, model_string, function(player, value, callback)
        callback('')
        modelCallback(model_string)
      end)
    end
  end

  tClothingStore.startProximityMonitor(character.source)
  menu:show(character.source)
end)
