pClothingStore = {}
P.bindInstance('clothingstore', pClothingStore)

pClothingStore.wipeOutfits = function(allow_dress)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(character.get('identifier'))

  if
    not character.request('Are you sure you want to wipe all outfits? This action cannot be undone') or
    not character.request('Are you REALLY REALLY sure you want to wipe all outfits? This action CANNOT BE UNDONE')
  then
    return
  end

  if character.prompt('Type DELETE in to this box to confirm you want to wipe all outfits') ~= 'DELETE' then
    character.notify('Outfits have NOT been deleted, you really should read prompts before confirming!')
    return
  end

  wardrobe_cache[user_id] = {}
  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe_cache[user_id])
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true)
  character.notify('Wardrobe wiped')
  character.log('ACTION', 'Wiped wardrobe')
end

pClothingStore.deleteOutfit = function(outfit_name, allow_dress, force_user_id)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  if not character.request('Are you sure you want to delete the outfit "' .. outfit_name ..'"?') then
    return
  end

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  wardrobe_cache[user_id][outfit_name] = nil

  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe_cache[user_id])
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true, force_user_id)
  character.notify('Outfit deleted')
end

pClothingStore.upgradeOutfit = function(target_outfit_name, gender, allow_dress)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(character.get('identifier'))

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  local outfits = {}

  if target_outfit_name == -1 then
    if not character.request('Update all outfits to game version ' .. current_version .. ', gender: ' .. gender .. '?') then
      return
    end

    for k, _ in pairs(wardrobe) do
      table.insert(outfits, k)
    end
  else
    if not character.request('Update outfit "' .. target_outfit_name .. '" to game version ' .. current_version .. ', gender: ' .. gender .. '?') then
      return
    end

    outfits = { target_outfit_name }
  end

  for _, outfit_name in pairs(outfits) do
    local outfit = exports.blrp_core:deepcopy(wardrobe[outfit_name])

    if outfit then
      local final_offsets = {
        ['1'] = 0, -- Mask
        -- ['2'] = 0 -- Hair
        ['3'] = 0, -- Hands
        ['4'] = 0, -- Legs
        ['5'] = 0, -- Bags
        ['6'] = 0, -- Shoes
        ['7'] = 0, -- Neck Accessories
        ['8'] = 0, -- Shirt
        ['9'] = 0, -- Armor
        ['10'] = 0, -- Decals
        ['11'] = 0, -- Jacket
        ['p0'] = 0, -- Headgear
        ['p1'] = 0, -- Glasses
        ['p2'] = 0, -- Ears
        ['p6'] = 0, -- Watches
        ['p7'] = 0, -- Bracelet
      }

      local selected_offsets = nil
      local selected_counts = nil

      for version_number, version_data in pairs(config_versioning) do
        local outfit_version = outfit.version or 2372

        if version_data.build == outfit_version then
          selected_counts = version_data.counts_male

          if gender == 'female' then
            selected_counts = version_data.counts_female
          end
        end

        if version_data.build > outfit_version and version_data.build <= current_version then
          local selected_offsets = version_data.offsets_male

          if gender == 'female' then
            selected_offsets = version_data.offsets_female
          end

          for offset_key, offset_value in pairs(selected_offsets) do
            if final_offsets[offset_key] then
              -- print('Offsetting key for game version', offset_key, version_data.build, offset_value)

              final_offsets[offset_key] = final_offsets[offset_key] + offset_value
            end
          end
        end
      end

      for outfit_key, outfit_value in pairs(outfit) do
        outfit_key = tostring(outfit_key)

        local final_offset = final_offsets[outfit_key]

        if final_offset and type(outfit_value) == 'table' and outfit_value[1] and type(outfit_value[1]) == 'number' then
          if outfit_value[1] > (selected_counts[outfit_key] - 1) then
            -- print('Apply offset', outfit_key, outfit_value[1], final_offset)

            outfit_value[1] = outfit_value[1] + final_offset

            outfit[outfit_key] = outfit_value
          else
            -- print('Skip offset for below threshold value', outfit_value[1], selected_counts[outfit_key] - 1)
          end
        end
      end

      outfit.version = current_version

      wardrobe[outfit_name] = outfit
      wardrobe_cache[user_id] = wardrobe
    end
  end

  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe)
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true, force_user_id)

  if target_outfit_name == -1 then
    character.notify('All outfits updated to version ' .. current_version .. ' (' .. gender .. ')')
  else
    character.notify('Outfit updated to version ' .. current_version .. ' (' .. gender .. ')')
  end
end

pClothingStore.setCustomization = function(outfit_name, force_user_id, player_model)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  local outfit = wardrobe[outfit_name]

  -- Handle undress option
  if outfit_name == '> Undress' then
    if player_model == `mp_m_freemode_01` then
      outfit = config_wardrobes.undress_male
    else
      outfit = config_wardrobes.undress_female
    end
  end

  if not outfit then
    return
  end

  -- Check item bound clothing. If wearing any, keep it on through outfit change
  local current_custom = tClothingStore.getCustomization(character.source, { false, outfit.save_hair })

  local blacklist = getBlacklist(player_model)

  if blacklist then
    for component, component_data in pairs(current_custom) do
      if type(component_data) == 'table' then
        local component_original = component

        component = tostring(component)

        if blacklist[component] then
          local blacklist_value = blacklist[component][tonumber(component_data[1])]

          if type(blacklist_value) == 'table' then
            blacklist_value = blacklist_value[tonumber(component_data[2])] or nil
          end

          if blacklist_value and string.match(blacklist_value, 'item:') then
            if not outfit[component_original] then
              outfit[component_original] = {
                0, 0, 0
              }
            end

            outfit[component_original][1] = component_data[1]
            outfit[component_original][2] = component_data[2]
          end
        end
      end
    end
  end

  local save_hair = outfit.save_hair

  if
    save_hair and
    tonumber(force_user_id) ~= tonumber(character.get('identifier')) and
    not character.request('Also apply hairstyle? This will override your hair')
  then
    save_hair = false
  end

  if save_hair and outfit['2'] then
    MySQL.scalar('SELECT skin FROM player_skins WHERE character_id = ?', { character.get('id') }, function(player_skin)
      if not player_skin then
        return
      end

      player_skin = json.decode(player_skin)

      player_skin.hair_1 = outfit['2'][1]

      MySQL.update('UPDATE player_skins SET skin = ? WHERE character_id = ?', { json.encode(player_skin), character.get('id') })
    end)
  end

  tClothingStore.setCustomization(character.source, { outfit, false, false, false, save_hair })
end

pClothingStore.renameOutfit = function(outfit_name, new_outfit_name, allow_dress, force_user_id)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  if not wardrobe[outfit_name] then
    return
  end

  if not new_outfit_name or new_outfit_name == '' or string.len(new_outfit_name) > 25 then
    character.notify('Invalid name')
    return
  end

  if wardrobe[new_outfit_name] then
    character.notify('You already have an outfit with this name')
    return
  end

  wardrobe[new_outfit_name] = wardrobe[outfit_name]
  wardrobe[outfit_name] = nil
  wardrobe_cache[user_id] = wardrobe

  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe)
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true, force_user_id)
  character.notify('Outfit renamed')
end

pClothingStore.saveOutfit = function(outfit_name, allow_dress, force_user_id, override, save_hair)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
  local favorite = false

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  if not override then
    if not outfit_name or outfit_name == '' or string.len(outfit_name) > 25 then
      character.notify('Invalid name')
      return
    end

    if wardrobe[outfit_name] then
      character.notify('You already have an outfit with this name')
      return
    end
  else -- Overriding
    if not character.request('Are you sure you want to overide "' .. outfit_name ..'"?') then
      return
    end

    favorite = wardrobe[outfit_name].favorite
    save_hair = wardrobe[outfit_name].save_hair
  end

  local custom_to_save = tClothingStore.getCustomization(character.source)

  -- Item bound clothing check. If wearing any, don't allow save of outfit
  local player_model = GetEntityModel(GetPlayerPed(character.source))

  local blacklist = getBlacklist(player_model)

  local undress = config_wardrobes.undress_male

  if player_model == `mp_f_freemode_01` then
    undress = config_wardrobes.undress_female
  end

  local skipped_clothing_items = false

  if blacklist then
    for component, component_data in pairs(custom_to_save) do
      if type(component_data) == 'table' then
        component = tostring(component)

        if blacklist[component] then
          local blacklist_value = blacklist[component][tonumber(component_data[1])]

          if type(blacklist_value) == 'table' then
            blacklist_value = blacklist_value[tonumber(component_data[2])] or nil
          end

          if blacklist_value and string.match(blacklist_value, 'item:') and undress[component] then
            custom_to_save[component] = undress[component]
            skipped_clothing_items = true
          end
        end
      end
    end
  end

  custom_to_save.version = tonumber(current_version)
  custom_to_save.favorite = favorite or nil
  custom_to_save.save_hair = save_hair or nil

  wardrobe[outfit_name] = custom_to_save
  wardrobe_cache[user_id] = wardrobe

  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe)
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true, force_user_id)

  if skipped_clothing_items then
    character.notify('Outfit saved. Clothing bound to inventory items were not saved as part of the outfit')
  else
    character.notify('Outfit saved')
  end
end

pClothingStore.shareWithNearby = function(outfit_name, target_player)
  local character = exports.blrp_core:character(source)
  local targetCharacter = exports.blrp_core:character(target_player)
  local user_id = tonumber(character.get('identifier'))

  if not target_player or target_player == -1 then
    return
  end

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  if not wardrobe[outfit_name] then
    return
  end

  character.notify('Outfit shared (requested) to nearby person')

  local accepted = targetCharacter.request('Someone is offering for you to change into an outfit they have')

  if not accepted then
    return character.notify('They did not want to change into that outfit')
  end

  tClothingStore.setCustomization(targetCharacter.source, { wardrobe[outfit_name] })
end

pClothingStore.toggleFavorite = function(outfit_name, allow_dress, force_user_id)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  local wardrobe = wardrobe_cache[user_id]

  if not wardrobe[outfit_name] then
    return
  end

  if not wardrobe[outfit_name].favorite then
    wardrobe[outfit_name].favorite = true
    character.notify('Outfit added to favorites')
  else
    wardrobe[outfit_name].favorite = false
    character.notify('Outfit removed from favorites')
  end

  wardrobe_cache[user_id] = wardrobe

  MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
    user_id = user_id,
    dvalue = json.encode(wardrobe)
  })

  TriggerEvent('blrp_clothingstore:server:openWardrobe', allow_dress, character.source, true, force_user_id)
end

pClothingStore.getItemQuantities = function()
  local character = exports.blrp_core:character(source)

  local item_quantities = {}

  for item_id, item_data in pairs(exports.blrp_core:character(source).getAllItems()) do
    if string.match(item_id, ':meta:') then
      item_id = string.sub(item_id, 0, string.find(item_id, ':meta') - 1)
    end

    if not item_quantities[item_id] then
      item_quantities[item_id] = 0
    end

    item_quantities[item_id] = item_quantities[item_id] + item_data.amount
  end

  return item_quantities
end
