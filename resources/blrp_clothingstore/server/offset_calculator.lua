--[[
local old_version = 4 -- 2944
local new_version = 5 -- 3095

Citizen.CreateThread(function()
  Citizen.Wait(1000)

  for _, v in ipairs({
    { 'mp_m_freemode_01', 'male' },
    { 'mp_f_freemode_01', 'female' },
  }) do
    local model, gender = table.unpack(v)

    tClothingStore.setPlayerModel(1, { GetHash<PERSON><PERSON>(model) })

    print('[    VERSIONING]', model)

    local counts_old = config_versioning[old_version]['counts_' .. gender]
    local counts_new = config_versioning[new_version]['counts_' .. gender]
    local offsets_comp = config_versioning[new_version]['offsets_' .. gender]

    for key, new_value in pairs(counts_new) do
      Citizen.Wait(100)

      local count = tClothingStore.getDrawableCount(1, { key })

      if count ~= new_value then
        print('[ COUNTS - FAIL]', key, new_value, count)
      else
        print('[ COUNTS - PASS]', key)
      end

      if new_value ~= 0 then
        local offset = (new_value - counts_old[key])

        if offset ~= offsets_comp[key] then
          print('[OFFSETS - FAIL]', key, offsets_comp[key], offset)
        else
          print('[OFFSETS - PASS]', key)
        end
      end
    end

    print('----------------------------------------------------------------')
  end
end)
]]
