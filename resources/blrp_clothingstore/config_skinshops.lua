config_skinshops = {
  zones = {
    { coords = vector3(1917.984, 3737.489, 32.602), heading = 209.901}, -- <PERSON>
    { coords = vector3(617.58966064453, 2762.3706054688, 42.08812713623), heading = 3.2340037822723}, -- <PERSON>
    { coords = vector3(1191.5401611328, 2711.2045898438, 38.222633361816), heading = 80.487579345703}, -- Harmony East
    { coords = vector3(1694.1499023438, 4827.2700195312, 42.063083648682), heading = 7.7365736961365}, -- Grapeseed
    { coords = vector3(8.5252561569214, 6514.6162109375, 31.87783241272), heading = 310.25924682617}, -- <PERSON><PERSON><PERSON>
    { coords = vector3(-1105.2420654297, 2708.1596679688, 19.107866287231), heading = 132.05473327637}, -- Route 68
    { coords = vector3(-3173.0871582031, 1046.2518310547, 20.863212585449), heading = 153.74067687988}, -- Chumash
    { coords = vector3(-1454.9918212891, -239.53414916992, 49.806518554688), heading = 143.94891357422}, -- Morningwood
    { coords = vector3(-1192.1729736328, -771.294921875, 17.324415206909), heading = 305.32989501953}, -- Del Perro
    { coords = vector3(-826.35131835938, -1074.9688720703, 11.32811164856), heading = 114.10655212402}, -- Vespucci
    { coords = vector3(-709.16198730469, -158.71614074707, 37.415145874023), heading = 205.46565246582}, -- Rockford Hills
    { coords = vector3(-159.28427124023, -298.70562744141, 39.733276367188), heading = 339.8874206543}, -- Las Lagunas
    { coords = vector3(122.94999694824, -221.82157897949, 54.557830810547), heading = 157.37490844727}, -- Alta
    { coords = vector3(426.18768310547, -802.00592041016, 29.491147994995), heading = 354.69970703125}, -- Textile City
    { coords = vector3(74.652786254883, -1397.1192626953, 29.376127243042), heading = 179.21569824219}, -- Strawberry
    { coords = vector3(105.629, -1303.377, 28.783), heading = 303.41976928711, length = 6.0, width = 6.0}, -- Vanilla Unicorn
    { coords = vector3(-1402.2446289062, -1015.4266967773, 23.240039825439), heading = 122.11334991455, length = 4.0, width = 4.0}, -- Admin Shop
    { coords = vector3(605.645, 11.457, 87.802), heading = 2.9857678413391, length = 3.0, width = 3.0}, -- VWPD
    { coords = vector3(-1080.772, -822.905, 19.326), heading = 349.565, length = 3.0, width = 3.0 }, -- VPD Swat gtf room
    { coords = vector3(-1078.359, -823.830, 19.326), heading = 177.821, length = 3.0, width = 3.0 }, -- VPD man
    { coords = vector3(-1087.526, -819.259, 27.147), heading = 138.761, length = 3.0, width = 3.0 }, -- VPD womans
    { coords = vector3(156.87919616699, -741.02105712891, 242.15194702148), heading = 65.063835144043, length = 2.0, width = 2.0}, -- FIB HQ
    { coords = vector3(1838.409, 3677.512, 38.929), heading = 299.635, length = 2.0, width = 2.0}, -- Sandy Shores SO
    { coords = vector3(-444.383, 6018.064, 36.996), heading = 135.0, length = 3.0, width = 3.0}, -- Paleto Bay SO
    { coords = vector3(174.97946166992, 6374.8208007812, 31.538204193115), heading = 295.22692871094, length = 4.0, width = 4.0}, -- CRU Paleto Bay
    { coords = vector3(1834.1549072266, 2577.8752441406, 46.014312744141), heading = 359.57614135742, length = 2.0, width = 2.0}, -- Bolingbroke DOC
    { coords = vector3(1698.212, 2575.737, 45.911), heading = 359.57614135742, length = 2.0, width = 2.0}, -- Bolingbroke DOC bathroom
    { coords = vector3(1749.7198486328, 2480.5961914062, 45.740631103516), heading = 212.77062988281, length = 2.0, width = 2.0}, -- Bolingbroke Inmates
    { coords = vector3(1785.302, 3656.438, 34.853), heading = 124.29000091553, length = 2.0, width = 2.0}, -- Sandy Shores Hospital
    { coords = vector3(308.488,-601.789,43.714), heading = 246.29000091553, length = 2.3, width = 3.7}, -- Pillbox Hospital
    { coords = vector3(219.77114868164, -1654.4891357422, 29.800369262695), heading = 231.15634155273, length = 3.0, width = 3.0}, -- Davis Fire Station
   -- { coords = vector3(364.29876708984, -1592.9599609375, 25.451717376709), heading = 265.94329833984, length = 3.0, width = 3.0}, -- Davis PD
    --{ coords = vector3(5961.24, -5231.894, 84.60646), heading = 265.94329833984, length = 3.0, width = 3.0}, -- Yankton PD (Lawton Junction)
    --{ coords = vector3(6070.891, -5246.338, 85.779), heading = 177.046}, -- Lawton Junction
    --{ coords = vector3(-3200.463, -174.766, 7.836), heading = 151.419, length = 3.0, width = 5.0}, -- B1
  },

  categories = {
    ['1'] = 'Mask',
    -- ['2'] = 'Hair (Dev MP TEST)',
    ['3'] = 'Hands',
    ['4'] = 'Legs',
    ['5'] = 'Bags',
    ['6'] = 'Shoes',
    ['7'] = 'Neck Accessories',
    ['8'] = 'Shirt',
    ['9'] = 'Armor',
    ['10'] = 'Decals',
    ['11'] = 'Jacket',
    ['p0'] = 'Headgear',
    ['p1'] = 'Glasses',
    ['p2'] = 'Ears',
    ['p6'] = 'Watches',
    ['p7'] = 'Bracelet',
  },

  categories_plus_hair = {
    ['1'] = 'Mask',
    ['2'] = 'Hair (Dev MP TEST)',
    ['3'] = 'Hands',
    ['4'] = 'Legs',
    ['5'] = 'Bags',
    ['6'] = 'Shoes',
    ['7'] = 'Neck Accessories',
    ['8'] = 'Shirt',
    ['9'] = 'Armor',
    ['10'] = 'Decals',
    ['11'] = 'Jacket',
    ['p0'] = 'Headgear',
    ['p1'] = 'Glasses',
    ['p2'] = 'Ears',
    ['p6'] = 'Watches',
    ['p7'] = 'Bracelet',
  },

  non_mp_categories = {
    ['0'] = 'Head (Ped models ONLY)',
    ['1'] = 'Mask',
    ['2'] = 'Hair (Ped models ONLY)',
    ['3'] = 'Hands',
    ['4'] = 'Legs',
    ['5'] = 'Bags',
    ['6'] = 'Shoes',
    ['7'] = 'Neck Accessories',
    ['8'] = 'Shirt',
    ['9'] = 'Armor',
    ['10'] = 'Decals',
    ['11'] = 'Jacket',
    ['p0'] = 'Headgear',
    ['p1'] = 'Glasses',
    ['p2'] = 'Ears',
    ['p6'] = 'Watches',
    ['p7'] = 'Bracelet',
  },
}

if GlobalState.is_dev then
  config_skinshops.categories = {
    ['0'] = '[HEAD - 00] Head (Non-MP ONLY)',
    ['1'] = '[BERD - 01] Mask',
    ['3'] = '[UPPR - 03] Hands',
    ['2'] = '[HAIR - 02] Hair DEV',
    ['4'] = '[LOWR - 04] Legs',
    ['5'] = '[HAND - 05] Bags',
    ['6'] = '[FEET - 06] Shoes',
    ['7'] = '[TEEF - 07] Neck Accessories',
    ['8'] = '[ACCS - 08] Shirt',
    ['9'] = '[TASK - 09] Armor',
    ['10'] = '[DECL - 10] Decals',
    ['11'] = '[JBIB - 11] Jacket',
    ['p0'] = '[HEAD - p0] Headgear',
    ['p1'] = '[EYES - p1] Glasses',
    ['p2'] = '[EARS - p2] Ears',
    ['p6'] = '[LEFT_WRIST - p6] Watches',
    ['p7'] = '[RIGHT_WRIST - p7] Bracelet',
  }

  config_skinshops.non_mp_categories = {
    ['0'] = '[HEAD - 00] Head (Non-MP ONLY)',
    ['1'] = '[BERD - 01] Mask',
    ['3'] = '[UPPR - 03] Hands',
    ['2'] = '[HAIR - 02] Hair DEV',
    ['4'] = '[LOWR - 04] Legs',
    ['5'] = '[HAND - 05] Bags',
    ['6'] = '[FEET - 06] Shoes',
    ['7'] = '[TEEF - 07] Neck Accessories',
    ['8'] = '[ACCS - 08] Shirt',
    ['9'] = '[TASK - 09] Armor',
    ['10'] = '[DECL - 10] Decals',
    ['11'] = '[JBIB - 11] Jacket',
    ['p0'] = '[HEAD - p0] Headgear',
    ['p1'] = '[EYES - p1] Glasses',
    ['p2'] = '[EARS - p2] Ears',
    ['p6'] = '[LEFT_WRIST - p6] Watches',
    ['p7'] = '[RIGHT_WRIST - p7] Bracelet',
  }
end
