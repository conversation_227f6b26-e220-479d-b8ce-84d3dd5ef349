--[[

This blacklist is really more a whitelist, because the value for each whitelisted attribute is a group that is permitted to wear that item
Multiple groups may be specified, delimited by a | character.

Example:

blacklists[`a_m_y_methhead_01`]  = {
  ['1'] = {
    [1] = 'LEO|LSFD',
    [2] = {
      [1] = 'LEO',
      [2] = 'LSFD'
    }
  }
}

In the above example, mask DRAWABLE # 1 is restricted to anyone who has the LEO (Given to all LEOs, training or otherwise) OR LSFD group.

mask DRAWABLE # 2 is available to all, but TEXTURE # 1 is restricted to police only and TEXTURE #2 is restricted to EMS only.

]]

blacklists[`a_m_y_methhead_01`] = {
  sex = 'male',

  -- BERD - 01 - Mask
  ['1'] = {
    [1] = {
      [0] = 'item:clth_scorpions_mask_a',
      [1] = 'item:clth_scorpions_mask_b',
      [2] = 'item:clth_scorpions_mask_c',
    },
  },

  -- UPPR - 03 - Hands
  ['3'] = {

  },

  -- LOWR - 04 - Legs
  ['4'] = {

  },

  -- HAND - 05 -  Bags
  ['5'] = {

  },

  -- FEET - 06 - Shoes
  ['6'] = {

  },

  -- TEEF - 07 - Neck Accessories
  ['7'] = {
    [1] = 'item:clth_chain_scorp_a',
    [2] = 'item:clth_chain_scorp_b',
  },

  -- ACCS - 08 - Shirt
  ['8'] = {

  },

  -- TASK - 09 - Armor
  ['9'] = {

  },

  -- DECL - 10 - Decals
  ['10'] = {

  },

  -- JBIB - 11 - Jacket
  ['11'] = {

  },

  -- P_HEAD - p0 - Headgear
  ['p0'] = {

  },

  -- P_EYES - p1 - Glasses
  ['p1'] = {

  },

  -- P_EARS - p2 - Ears
  ['p2'] = {

  },

  -- P_LEFT_WRIST - p6 - Watches
  ['p6'] = {

  },

  -- P_RIGHT_WRIST - p7 - Bracelet
  ['p7'] = {

  },
}
