<html>
  <head>
    <title>BLRP Clothing Store NUI</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto+Mono">
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
    <style type="text/css">
      * {
        font-family: "Roboto Mono", sans-serif;
      }

      ::-webkit-scrollbar {
          width: 0.3vw;
      }

      ::-webkit-scrollbar-thumb {
          background: deepskyblue
      }

      #container {
        height: 100% !important;
        display: none;
        justify-content: flex-end;
        align-items: flex-start;
      }

      #wrapper {
        width: 450px;
        height: 90%;
        max-height: 90%;
        overflow: hidden;

        padding: 10px;

        margin-top: 25px;
        margin-right: 25px;

        background: rgba(36, 38, 60, 0.8);
        box-shadow: 0px 0px 10px deepskyblue;
        border: 1px solid deepskyblue;
        border-radius: 25px;

        color: white;
      }

      * {
        text-shadow: 0px 0px 6px rgb(9, 10, 26);
        }

      #categories {
        display: flex;
        flex-direction: column;

        max-height: 90%;
        overflow-y: scroll;
        overflow-x: hidden;

        padding-right: 10px;
      }

      .category {
        background: rgba(36, 38, 60, 0.2);
        margin-bottom: 20px;
        box-shadow: inset 0px 0px 3px rgb(0, 0, 0);
      }

      .category-header {
        background: rgb(20, 21, 34);
        padding: 5px;

      }

      .category-body {
        background: rgba(0, 0, 0, 0.5);
        padding: 10px 5px 5px 5px;

        display: flex;
        justify-content: space-evenly;
      }

      .selector-group {
        display: flex;
        flex-direction: column;

      }

      .selector-options {
        display: flex;
        align-items: center;

      }

      .selector-title {
        text-align: center;
        font-size: 0.9rem;
      }

      .number-input {
        background-color: rgba(0, 0, 0, 0);
        width: 75px;
        height: 80%;
        text-align: center;
        border: 0.02vw solid deepskyblue;
        border-top: none;
        border-left: none;
        border-right:none;
        color: white;
      }

      .btn {
        background: rgba(36, 38, 60, 0.8);
        border: none;
        color: white;
        padding: 5px 16px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 20px;
        margin: 4px 2px;
        cursor: pointer;
        border-radius: 10%;
      }

      .btn:hover {
        color: deepskyblue;
        outline: 1px solid deepskyblue;
        box-shadow: 0px 0px 4px deepskyblue;
      }

      .btn-forward {
        color: white;
      }

      *:not(input) {
        user-select: none;
      }

      #cameracontrols {
        margin-bottom: 5px;
        display: flex;
        justify-content: space-evenly;
      }

      .btn-camera {
        background: rgba(36, 38, 60, 0.8);
        color: white;
        padding: 8px 8px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 20px;
        margin: 4px 4px;
        cursor: pointer;
        border: 1px solid deepskyblue;
      }

      .btn-camera-active {
        border: 1px solid deepskyblue;
        margin: 2px 2px;
        background-color: rgba(36, 38, 60, 0.8);
        box-shadow: 0px 0px 10px deepskyblue;
      }

      .btn-remove-shirt {
        float: right;
        background: rgba(36, 38, 60, 0.8);
        box-shadow: 0px 0px 1px rgb(11, 14, 34);
        border: 0px;
        color: white;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
      }

			.popup {
			    display: none;
			    position: absolute;
			    font-size: 1.5vh;
			    top: 50%;
			    left: 50%;
			    transform: translate(-50%, -50%);
			    width: 25vw;
			    height: 17vh;
			}

			.popup .content {
			    display: flex;
			    flex-direction: column;
			    text-align: center;
			    color: white;
			}

			.popup > .content> .title {
			    background: rgba(0, 0, 0, 0.5);
			    margin-bottom: 0.5vh;
			    height: 2.5vh;
			    padding-top: 1%;
			    text-transform: uppercase;
			    border-top-left-radius: 20px;
			    border-top-right-radius: 20px;
			}

			.popup > .content> .message {
			    background: rgba(0, 0, 0, 0.5);
			    height: 8vh;
			    padding-top: 4vh;
			}

			.panelbottom {
			    background: rgba(0, 0, 0, 0.5);
			    border-bottom-left-radius: 20px;
			    border-bottom-right-radius: 20px;
			    height: 5vh;
			    margin-top: 0.5vh;
			    display: flex;
			    flex-direction: row;
			    justify-content: center;
			}

			.panelbottom button {
			    position: relative;
			    display: inline-block;
			    background: none;
			    height: 100%;
			    width: 10vw;
			    text-align: center;
			    text-transform: uppercase;
			    font-size: 1.5vh;
			    color: white;
			    outline: none;
			    border: none;
			    border-right: 0.02vw solid rgba(255, 255, 255, 0.2);
			}

			.panelbottom button:first-of-type {
			    border-left: 0.02vw solid rgba(255, 255, 255, 0.2);
			}

			.panelbottom button:hover:not(.active) {
			    background-color: #646464;
			}
    </style>
  </head>

  <body>
    <div id="container">
      <div id="wrapper">
        <div>
          <h2 style="text-align:center">Clothing Store</h2>
        </div>

        <div id="cameracontrols">
          <button class="btn-camera" data-view="head"><i class="fa-regular fa-fw fa-head-side"></i></button>
          <button class="btn-camera btn-camera-active" data-view="body"><i class="fa-regular fa-fw fa-person"></i></button>
          <button class="btn-camera" data-view="legs"><i class="fa-regular fa-fw fa-boot-heeled"></i></button>
        </div>

        <div id="categories"></div>
      </div>
			<div class="overlay">
			</div>
			<div class="popup">
					<div class="content">
							<div class="title">Confirmation</div>
							<div class="message">Are you sure you want to close the clothing shop?</div>
							<div class="panelbottom">
									<button data-option="yes">Yes</button>
									<button data-option="no">No</button>
							</div>
					</div>
			</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

    <script type="text/javascript">
      let category_options = {};
      let popup_promise = null;
      let require_confirmation = null;

      function renderTemplate(props) {
        return function(tok, i) {
          return (i % 2) ? props[tok] : tok;
        };
      }

      $(document).on('keypress', '.number-input', function(e) {
        return
          e.metaKey || // cmd/ctrl
          e.which <= 0 || // arrow keys
          e.which == 8 || // delete key
          /[0-9]/.test(String.fromCharCode(e.which)); // numbers
      }).on('change', '.number-input', function(e) {
        let value = $(this).val();

        if(!/[\-0-9]/.test(value)) {
          $(this).val($(this).data('value-cached'));
          return false;
        }

        value = parseInt(value);

        let category_selector = $($(this).closest('.category')[0]);

        let scope = $(this).data('scope');
        let category = category_selector.data('category');
        let drawable_key = category_selector.data('drawable-key');

        if(scope == 'drawable') {
          let item_key = Object.keys(category_options[category].drawables).find(k => category_options[category].drawables[k] === value);

          if(item_key === null || item_key === undefined) {
            $(this).val($(this).data('value-cached'));
            return false;
          }

          category_selector.data('drawable-key', item_key)
        } else if(scope == 'texture') {
          let texture_key = Object.keys(category_options[category].textures[category_options[category].drawables[drawable_key]]).find(k => category_options[category].textures[category_options[category].drawables[drawable_key]][k] === value);

          if(texture_key === null || texture_key === undefined) {
            $(this).val($(this).data('value-cached'));
            return false;
          }

          category_selector.data('texture-key', texture_key)
        }

        updateCategoryInputs(category_selector)
      }).on('focusin', '.number-input', function(){
        $(this).data('value-cached', $(this).val());
      }).on('click', 'button[data-direction]', function(e) {
        let direction = $(this).data('direction');
        let category_selector = $($(this).closest('.category')[0]);

        let scope = $(this).data('scope');
        let key = category_selector.data(scope + '-key');
        let category = category_selector.data('category');

        if(direction == '+') {
          key = parseInt(key) + 1
        } else {
          key = parseInt(key) - 1
        }

        if(scope == 'drawable') {
          let length = category_options[category].drawables.length

          if(key < 0) {
            key = (length - 1)
          } else if(key == length) {
            key = 0
          }

          category_selector.data('drawable-key', key)
          category_selector.data('texture-key', 0)
          updateCategoryInputs(category_selector)
        } else {
          let drawable_id = category_options[category].drawables[category_selector.data('drawable-key')];

          let length = category_options[category].textures[drawable_id].length

          if(key < 0) {
            key = (length - 1)
          } else if(key == length) {
            key = 0
          }

          category_selector.data('texture-key', key)
          updateCategoryInputs(category_selector)
        }
      });

      function updateCategoryInputs(selector) {
        let category_inputs = {
          category: 0,
          drawable: 0,
          texture: 0,
        }

        selector.find('input.number-input').each((key, input) => {
          input = $(input);

          let category = selector.data('category');
          let scope = input.data('scope');
          let drawable_key = selector.data('drawable-key');

          category_inputs.category = category

          if(scope == 'drawable') {
            category_inputs.drawable = category_options[category].drawables[drawable_key]

            input.val(category_options[category].drawables[drawable_key])
          } else {
            category_inputs.texture = category_options[category].textures[category_options[category].drawables[drawable_key]][selector.data('texture-key')]

            input.val(category_options[category].textures[category_options[category].drawables[drawable_key]][selector.data('texture-key')])
          }
        });

        $.post('https://blrp_clothingstore/setDual', JSON.stringify({
          category: category_inputs.category,
          inputs: category_inputs
        }));
      }

      var moving = false
      var lastOffsetX = 0
      var lastOffsetY = 0
      var lastScreenX = 0.5 * screen.width
      var lastScreenY = 0.5 * screen.height

      $('body').on('mousedown', function(e) {
        let element = $(e.target);

        if(element.prop('tagName') === 'DIV' && element.attr('id') === 'container') {
          moving = true;
        }
      }).on('mouseup', function(e) {
        if(moving) {
          moving = false;
        }
      }).on('mousemove', function(event) {
        if(moving) {
          let offsetX = event.screenX - lastScreenX;
          let offsetY = event.screenY - lastScreenY;
          if ((lastOffsetX > 0 && offsetX < 0) || (lastOffsetX < 0 && offsetX > 0)) {
            offsetX = 0
          }
          if ((lastOffsetY > 0 && offsetY < 0) || (lastOffsetY < 0 && offsetY > 0)) {
            offsetY = 0
          }
          lastScreenX = event.screenX;
          lastScreenY = event.screenY;
          lastOffsetX = offsetX;
          lastOffsetY = offsetY;
          $.post('https://blrp_clothingstore/updateCameraRotation', JSON.stringify({
            x: offsetX,
            y: offsetY,
          }));
        }
      }).on('wheel', function(e) {
        let element = $(e.target);

        if(element !== null && element.prop('tagName') === 'DIV' && element.attr('id') === 'container') {
          let zoom = e.originalEvent.deltaY / 2000;
          $.post('https://blrp_clothingstore/updateCameraZoom', JSON.stringify({
            zoom: zoom,
          }));
        }
      });

      $('button.btn-camera').click(function(e) {
        $('button.btn-camera').removeClass('btn-camera-active');
        $(this).addClass('btn-camera-active');

        $.post('https://blrp_clothingstore/updateCameraView', JSON.stringify({
          view: $(this).data('view'),
        }));
      });

      $(document).on('click', 'button.btn-remove-shirt', function(e) {
        let category_selector = $($(this).closest('.category')[0]);

        category_selector.data('drawable-key', 0);
        category_selector.data('texture-key', 0);
        category_selector.find('input.number-input').val(0);

        $.post('https://blrp_clothingstore/removeShirt', JSON.stringify({}));
      });

      $('button[data-option]').on('click', function(e) {
        e.preventDefault();

        if(popup_promise) {
          popup_promise($(this).data('option') == 'yes');
        }
      });

      let interfaceShowing = false;

      window.addEventListener('keydown', function(event) {
        if(!interfaceShowing) {
          return false;
        }

        if(event.key === 'Escape') {
					event.preventDefault();

          if (!require_confirmation) {
            $.post('https://blrp_clothingstore/escape', JSON.stringify({}));
            return;
          }

          new Promise((resolve, reject) => {
            popup_promise = resolve;
          }).then(close => {
            if(close) {
              $.post('https://blrp_clothingstore/escape', JSON.stringify({}));
            }
            popup_promise = null;

            console.log('popup resolution value is', close)
          }).finally(() => {
            $('.popup').fadeOut(100);
            $('.overlay').fadeOut(100);
            $('#main').css('pointer-events', 'auto');
            $('#cameracontrol').css('pointer-events', 'auto');
          });

          $('.popup').fadeIn(100);
          $('.overlay').fadeIn(100);
          $('#main').css('pointer-events', 'none');
          $('#cameracontrol').css('pointer-events', 'none');
        } else if(event.key === 'x') {
          $.post('https://blrp_clothingstore/passthrough', JSON.stringify({
            key: event.key
          }));
        }
      });

      window.addEventListener("message", (e) => {
        let data = e.data
        let action = data.action

        let container = document.getElementById('container');

        if(action === 'show') {
          require_confirmation = data.new_character;

          if(data.category_options) {
            category_options = data.category_options;
          }

          if(data.customization !== null && data.categories !== null) {
            $('#categories').html('');

            Object.keys(data.customization).sort((a, b) => {
              a = data.categories[a].toUpperCase();
              b = data.categories[b].toUpperCase();

              if(a < b) {
                return -1;
              } else if(a > b) {
                return 1;
              }

              return 0
            }).forEach(function(key) {
              let value = data.customization[key];

              let item = value[0]
              let texture = value[1]

              let template = $('script[data-template="category"]').text().split(/\$\{(.+?)\}/g);

              let item_key = 0
              let texture_key = 0

              try {
                item_key = Object.keys(category_options[key].drawables).find(k => category_options[key].drawables[k] === item);
                texture_key = Object.keys(category_options[key].textures[item]).find(k => category_options[key].textures[item][k] === texture);
              } catch(e) {
                // Exception happens when the person is wearing something they aren't whitelisted for
                // If that happens, set everything to 0
                item = 0
                item_key = 0
                texture = 0
                texture_key = 0
              }

              template_rendered = $(template.map(renderTemplate({
                name: data.categories[key],
                item: item,
                item_key: item_key,
                texture: texture,
                texture_key: texture_key,
                category: key,
              })).join(''));

              if(key != 8) {
                template_rendered.find('button.btn-remove-shirt').remove();
              }

              $('#categories').append(template_rendered);
            });
          }

          interfaceShowing = true;
          container.style.display = 'flex';
        } else if (action === 'hide') {
          interfaceShowing = false;
          container.style.display = 'none';
        }
      })
    </script>

    <script type="text/template" data-template="category">
      <div class="category" data-category="${category}" data-drawable-key="${item_key}" data-texture-key="${texture_key}">
        <div class="category-header">
          <span>${name}</span>
          <button class="btn-remove-shirt">Remove</button>
        </div>

        <div class="category-body">
          <div class="selector-group">
            <div class="selector-title">Item</div>

            <div class="selector-options">
              <button class="btn" data-direction="-" data-scope="drawable">⯇</button>
              <input class="number-input" type="text" value="${item}" data-scope="drawable" />
              <button class="btn" data-direction="+" data-scope="drawable">⯈</button>
            </div>
          </div>

          <div class="selector-group">
            <div class="selector-title">Texture</div>

            <div class="selector-options">
              <button class="btn" data-direction="-" data-scope="texture">⯇</button>
              <input class="number-input" type="text" value="${texture}" data-scope="texture" />
              <button class="btn" data-direction="+" data-scope="texture">⯈</button>
            </div>
          </div>
        </div>
      </div>
    </script>
  </body>
</html>
