function calculateOffsetBetweenBuilds(build_a, build_b)
  local counts = nil
  local old_version = nil
  local new_version = nil

  for version, version_info in pairs(config_versioning) do
    if version_info.build == build_a then
      counts = { male = version_info.counts_male, female = version_info.counts_female }
      old_version = version
    end

    if version_info.build == build_b then
      new_version = version
    end
  end

  local offsets = {
    male = {
      ['1'] = 0, -- Mask
      -- ['2'] = 0 -- Hair
      ['3'] = 0, -- Hands
      ['4'] = 0, -- Legs
      ['5'] = 0, -- Bags
      ['6'] = 0, -- Shoes
      ['7'] = 0, -- Neck Accessories
      ['8'] = 0, -- Shirt
      ['9'] = 0, -- Armor
      ['10'] = 0, -- Decals
      ['11'] = 0, -- Jacket
      ['p0'] = 0, -- Headgear
      ['p1'] = 0, -- Glasses
      ['p2'] = 0, -- Ears
      ['p6'] = 0, -- Watches
      ['p7'] = 0, -- Bracelet
    },

    female = {
      ['1'] = 0, -- Mask
      -- ['2'] = 0 -- Hair
      ['3'] = 0, -- Hands
      ['4'] = 0, -- Legs
      ['5'] = 0, -- Bags
      ['6'] = 0, -- Shoes
      ['7'] = 0, -- Neck Accessories
      ['8'] = 0, -- Shirt
      ['9'] = 0, -- Armor
      ['10'] = 0, -- Decals
      ['11'] = 0, -- Jacket
      ['p0'] = 0, -- Headgear
      ['p1'] = 0, -- Glasses
      ['p2'] = 0, -- Ears
      ['p6'] = 0, -- Watches
      ['p7'] = 0, -- Bracelet
    },
  }

  for version = old_version + 1, new_version do
    for gender, gender_offsets in pairs(offsets) do
      for component, _ in pairs(gender_offsets) do
        gender_offsets[component] = gender_offsets[component] + config_versioning[version]['offsets_' .. gender][component]
      end
    end
  end

  return offsets, counts
end

function backwardsCompatibleOutfit(original_build, outfit, gender)
  if not gender then
    error('No gender provided')
    return
  end

  local current_build = nil

  if IsDuplicityVersion() then
    current_build = GetConvarInt('sv_enforceGameBuild')
  else
    current_build = GetGameBuildNumber()
  end

  local offsets, counts = calculateOffsetBetweenBuilds(original_build, current_build)

  if gender == 'male' then
    offsets = offsets.male
    counts = counts.male
  elseif gender == 'female' then
    offsets = offsets.female
    counts = counts.male
  end

  for offset_category, offset in pairs(offsets) do
    for outfit_category, outfit_value in pairs(outfit) do
      if tostring(offset_category) == tostring(outfit_category) then
        if outfit_value[1] >= counts[tostring(offset_category)] then
          outfit_value[1] = outfit_value[1] + offset
        end
      end
    end
  end

  return outfit
end

exports('BackwardsCompatibleOutfit', backwardsCompatibleOutfit)
