local inside_zone = false

for zone_id, zone_data in ipairs(config_skinshops.zones) do
  BoxZone:Create(zone_data.coords, zone_data.length or 8.0, zone_data.width or 8.0, {
    name = 'ClothingStoreZone' .. zone_id,
    heading = zone_data.heading,
    minZ = zone_data.coords.z - 1.0,
    maxZ = zone_data.coords.z + 2.0,
    onPlayerInOut = function(inside)
      inside_zone = inside

      local action = 'START'

      if not inside then
        action = 'END'
      end

      exports['mythic_notify']:PersistentAlert(action, 'blrp_clothingstore-enter', 'inform', 'Press E to use clothing store')
    end,
  })
end

Citizen.CreateThread(function()
  while true do
    if not interface_visible and inside_zone and IsControlJustReleased(0, 38) then -- E
      tClothingStore.setVisible(true)

      Citizen.Wait(500)
    end

    Citizen.Wait(1)
  end
end)
