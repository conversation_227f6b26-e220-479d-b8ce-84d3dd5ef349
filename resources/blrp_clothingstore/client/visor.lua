-- First number is down, second number is up
local helmet_visor_numbers = {
    -- { build_where_you_got_the_numbers, jbib_visor_down, jbib_visor_up }
    [`mp_m_freemode_01`] = {
      { 3095, 18, 67 },
      { 3095, 50, 68 },
      { 3095, 51, 69 },
      { 3095, 52, 70 },
      { 3095, 53, 71 },
      { 3095, 62, 72 },
      { 3095, 73, 74 },
      { 3095, 78, 79 },
      { 3095, 80, 81 },
      { 3095, 82, 67 },
      { 3095, 91, 92 },
      { 3095, 116, 117 },
      { 3095, 118, 119 },
      { 3095, 123, 124 },
      { 3095, 125, 126 },
      { 3095, 128, 127 },
      { 3095, 147, 148 },
      { 3095, 195, 196 },
      { 3095, 199, 200 },
    },
  
    [`mp_f_freemode_01`] = {
      { 3095, 18, 66 },
      { 3095, 49, 67 },
      { 3095, 50, 68 },
      { 3095, 51, 69 },
      { 3095, 52, 70 },
      { 3095, 62, 71 },
      { 3095, 72, 73 },
      { 3095, 77, 78 },
      { 3095, 79, 80 },
      { 3095, 90, 91 },
      { 3095, 115, 116 },
      { 3095, 117, 118 },
      { 3095, 122, 123 },
      { 3095, 124, 125 },
      { 3095, 127, 126 },
      { 3095, 146, 147 },
      { 3095, 194, 195 },
      { 3095, 198, 199 },
    },
}
  
tClothingStore.toggleHelmetVisor = function()
    local ped = PlayerPedId()
    local model = GetEntityModel(ped)
    local gender_str = model == `mp_m_freemode_01` and 'male' or 'female'
    local visors = helmet_visor_numbers[model]
  
    if not visors then
        return
    end
  
    local current_visor = { GetPedPropIndex(ped, 0), GetPedPropTextureIndex(ped, 0) }
    local target_id = false
    local anim_dict, anim_name
  
    for _, visor in pairs(visors) do
        for i = 2, 3 do
            local visor_id = exports.blrp_clothingstore:BackwardsCompatibleOutfit(visor[1], { [0] = { visor[i], current_visor[2] } }, gender_str)[0][1]
  
            if current_visor[1] == visor_id then
                target_id = visor[3]
                anim_dict = "anim@mp_helmets@on_foot"
                anim_name = "visor_up"
  
                if i == 3 then
                    target_id = visor[2]
                    anim_name = "visor_down"
                end
  
                target_id = exports.blrp_clothingstore:BackwardsCompatibleOutfit(visor[1], { [0] = { target_id, current_visor[2] } }, gender_str)[0][1]
            end
        end
    end
  
    if not target_id then
        return
    end
  
    RequestAnimDict(anim_dict)
    while not HasAnimDictLoaded(anim_dict) do
        Citizen.Wait(10)
    end
  
    TaskPlayAnim(ped, anim_dict, anim_name, 8.0, -8.0, -1, 49, 0, false, false, false)
  
    Citizen.Wait(600)
  
    ClearPedTasks(ped)
  
    SetPedPropIndex(ped, 0, target_id, current_visor[2], true)
end