interface_visible = false
local should_compare = false
local last_customization = false
local last_idle_customization = false
local cached_gender_male = nil
local cached_model_freemode = false
local cached_model = nil
local group_cache = {}
local cache_retreived = 0

function runCache()
  local player_model = GetEntityModel(PlayerPedId())

  if player_model == `mp_m_freemode_01` then
    cached_model_freemode = true
    cached_gender_male = true
  elseif player_model == `mp_f_freemode_01` then
    cached_model_freemode = true
    cached_gender_male = false
  else
    cached_model_freemode = false
    cached_gender_male = false
  end

  cached_model = player_model
end

Citizen.CreateThread(function()
  while true do
    runCache()

    Citizen.Wait(5000)
  end
end)

function SetPedComponentVariationInternal(ped, componentId, drawableId, textureId, paletteId)
  -- Hair
  if componentId == 2 then
    local overlays = config_overlays.male

    if GetEntityModel(ped) == `mp_f_freemode_01` then
      overlays = config_overlays.female
    end

    local retains = {}

    for _, decoration in pairs(GetPedDecorations(PlayerPedId())) do
      if not isHairOverlay(decoration[1], decoration[2], cached_gender_male) then
        table.insert(retains, decoration)
      end
    end

    ClearPedDecorations(PlayerPedId())

    for _, decoration in pairs(retains) do
      AddPedDecorationFromHashes(PlayerPedId(), decoration[1], decoration[2])
    end

    local hair_overlay = overlays[drawableId]

    if hair_overlay then
      AddPedDecorationFromHashes(PlayerPedId(), hair_overlay[3], hair_overlay[4])
    end
  end

  SetPedComponentVariation(ped, componentId, drawableId, textureId, paletteId)
end

exports('SetPedComponentVariation', SetPedComponentVariationInternal)

tClothingStore = {}
T.bindInstance('clothingstore', tClothingStore)
pClothingStore = P.getInstance('blrp_clothingstore', 'clothingstore')

tClothingStore.parseCategory = function(category)
  if type(category) == 'string' and string.sub(category, 1, 1) == 'p' then
    return true, tonumber(string.sub(category, 2))
  else
    return false, tonumber(category)
  end
end

tClothingStore.getCustomization = function(include_model, include_hair)
  local ped = PlayerPedId()

  if include_model == nil then
    include_model = true
  end

  local custom = {}

  if include_model then
    custom.modelhash = GetEntityModel(ped)
  end

  local categories = config_skinshops.categories

  if include_hair then
    categories = config_skinshops.categories_plus_hair
  end

  if not ({
    [`mp_m_freemode_01`] = true,
    [`mp_f_freemode_01`] = true,
  })[GetEntityModel(PlayerPedId())] then
    categories = config_skinshops.non_mp_categories
  end

  for component_key, component_name in pairs(categories) do
    local is_prop, index = tClothingStore.parseCategory(component_key)

    if is_prop then
      custom[component_key] = {
        GetPedPropIndex(ped, index),
        math.max(GetPedPropTextureIndex(ped, index), 0)
      }
    else
      custom[component_key] = {
        GetPedDrawableVariation(ped, index),
        GetPedTextureVariation(ped, index),
        GetPedPaletteVariation(ped, index)
      }
    end
  end

  return custom
end

tClothingStore.setCustomization = function(customization, name, force_items_cache, force_ped_handle, allow_hair)
  runCache()

  local ped = PlayerPedId()

  if force_ped_handle and DoesEntityExist(force_ped_handle) then
    ped = force_ped_handle
  end

  -- Related to server side utility for converting cloakrooms between game versions
  -- Will print the new custom in client console also
  if name and type(name) == 'string' then
    print("['" .. name .."'] = {")

    local _custom = {}

    for k, v in pairs(customization) do
      _custom[tostring(k)] = v
    end

    local fields = {
      '1', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'p0', 'p1', 'p2', 'p6', 'p7',
    }

    if allow_hair then
      table.insert(fields, '2')
    end

    for _, field in ipairs(fields) do
      local custom_field = _custom[field]

      if custom_field then
        local prefix = ''

        if field == '1' then
          prefix = '-- '
        end

        local field_key = field

        if not tonumber(field_key) then
          field_key = "'" .. field_key .. "'"
        end

        print(prefix .. '[' .. field_key .. '] = {' .. tostring(custom_field[1]) .. ', ' .. tostring(custom_field[2]) .. '},')
      else
        if field == '1' then
          print('-- [1] = {0, 0},')
        else
          exports.blrp_core:me().notify('Field not found in custom: ' .. name .. ' / ' .. field)
        end
      end
    end

    print("},")
    print(' ')
  end
  --------------------------

  local is_ped_model = false

  if not ({
    [`mp_m_freemode_01`] = true,
    [`mp_f_freemode_01`] = true,
  })[GetEntityModel(force_ped_handle)] then
    is_ped_model = true
  end

  local function processCustomization(component_key, component_data)
    if
      string.match(component_key, 'version') or
      string.match(component_key, 'favorite') or
      string.match(component_key, 'save_hair') or
      string.match(component_key, 'model') or
      string.match(component_key, 'build')
    then
      return
    end

    if
      not is_ped_model and
      (
        component_key == 0 or component_key == "0" or -- Head
        component_key == 2 or component_key == "2" -- Hair
      )
    then
      return
    end

    if
      is_ped_model and
      not allow_hair and
      (
        component_key == 2 or
        component_key == "2"
      )
    then
      return
    end

    local is_prop, category = tClothingStore.parseCategory(component_key)

    local id, texture, palette = table.unpack(component_data)

    if is_prop then
      if not force_ped_handle and not tClothingStore.checkCanWear(component_key, id, texture, force_items_cache) then
        return
      end

      if id == -1 then
        ClearPedProp(ped, category)
      else
        SetPedPropIndex(ped, category, id, texture, 2)
      end
    else
      if not force_ped_handle and not tClothingStore.checkCanWear(component_key, id, texture, force_items_cache) then
        return
      end

      SetPedComponentVariationInternal(ped, category, id, texture, palette)
    end
  end

  for component_key, component_data in pairs(customization) do
    processCustomization(component_key, component_data)
  end

  should_compare = true
end

exports('SetCustomization', tClothingStore.setCustomization)

tClothingStore.getDrawableCount = function(category)
  local is_prop, index = tClothingStore.parseCategory(category)

  if is_prop then
    return GetNumberOfPedPropDrawableVariations(PlayerPedId(), index)
  else
    return GetNumberOfPedDrawableVariations(PlayerPedId(), index)
  end
end

tClothingStore.getTextureCount = function(category, drawable)
  local is_prop, index = tClothingStore.parseCategory(category)

  if is_prop then
    return GetNumberOfPedPropTextureVariations(PlayerPedId(), index, drawable)
  else
    return GetNumberOfPedTextureVariations(PlayerPedId(), index, drawable)
  end
end

local item_quantities_cache = {}
local item_quantities_cache_time = 0

function getItemQuantities(force_items_cache)
  if force_items_cache or item_quantities_cache_time < GetGameTimer() - 10000 then
    item_quantities_cache = pClothingStore.getItemQuantities()
    item_quantities_cache_time = GetGameTimer()
  end

  return item_quantities_cache
end

-- Check if we are able to wear this thing. Returns if allowed
tClothingStore.checkCanWear = function(category, drawable, texture, force_items_cache)
  local category_numeric = tonumber(category)

  -- Block Gen9 (GTA build 2699+) stub components
  if category_numeric and IsPedComponentVariationGen9Exclusive(PlayerPedId(), category_numeric, tonumber(drawable)) then
    return false
  end

  if GetGameTimer() - cache_retreived > 5000 then
    group_cache = exports.blrp_core:me().get('groups') or {}
    cache_retreived = GetGameTimer()
    print('[CLOTHING-STORE] Cache being grabbed')
  end

  local function checkPermissions(blacklist_value)
    for permission in string.gmatch(blacklist_value, '([^|]+)') do
      if string.match(permission, 'user') then
        -- Check format "user:58794"
        if tonumber(exports.blrp_core:me().get('identifier')) == tonumber(string.sub(permission, string.find(permission, ':') + 1, string.len(permission))) then
          return true
        end
      elseif string.match(permission, 'character') then
        -- Check format "character:9102"
        if tonumber(exports.blrp_core:me().get('id')) == tonumber(string.sub(permission, string.find(permission, ':') + 1, string.len(permission))) then
          return true
        end
      elseif string.match(permission, 'item') then
        -- Check format "item:clth_..."
        local item_quantities = getItemQuantities(force_items_cache)

        local item_id = string.sub(permission, string.find(permission, ':') + 1, string.len(permission))

        if item_quantities[item_id] and item_quantities[item_id] > 0 then
          return true
        end
      elseif group_cache[permission] then
        -- Check group literal
        return true
      elseif (permission == 'LEO' or permission == 'LSFD' or permission == 'DOC') and group_cache[permission .. '_OffDuty'] then
        -- Check *_OffDuty for LEO and LSFD and DOC
        return true
      end
    end

    return false
  end

  local blacklist = getBlacklist(cached_model)

  if not blacklist then
    return false
  end

  local blacklist_value = blacklist[category]

  if not blacklist_value then
    return true
  end

  blacklist_value = blacklist_value[drawable]

  if not blacklist_value then
    return true
  end

  if type(blacklist_value) == 'table' then
    if texture then
      blacklist_value = blacklist_value[texture]

      if not blacklist_value then
        return true
      end

      return checkPermissions(blacklist_value)
    end

    return true
  end

  return checkPermissions(blacklist_value)
end

exports('CheckCanWear', tClothingStore.checkCanWear)

tClothingStore.getCategoryOptions = function()
  local ped = PlayerPedId()

  local options = {}

  local categories = config_skinshops.categories

  if not ({
    [`mp_m_freemode_01`] = true,
    [`mp_f_freemode_01`] = true,
  })[GetEntityModel(PlayerPedId())] then
    categories = config_skinshops.non_mp_categories
  end

  for component_key, component_name in pairs(categories) do
    local drawable_count = tClothingStore.getDrawableCount(component_key)

    local drawables = {}
    local textures = {}

    if string.sub(component_key, 1, 1) == 'p' then
      table.insert(drawables, -1)
      textures[-1] = { 0 }
    end

    for drawable = 0, drawable_count - 1 do
      if tClothingStore.checkCanWear(component_key, drawable) then
        local _textures = {}

        for texture = 0, tClothingStore.getTextureCount(component_key, drawable) - 1 do
          if tClothingStore.checkCanWear(component_key, drawable, texture) then
            table.insert(_textures, texture)
          end
        end

        if #_textures > 0 then
          table.insert(drawables, drawable)
          textures[drawable] = _textures
        end
      end
    end

    options[component_key] = {
      drawable_count = drawable_count,
      drawables = drawables,
      textures = textures,
    }
  end

  return options
end

tClothingStore.setVisible = function(_visible, new_character)
  interface_visible = _visible

  local categories = config_skinshops.categories

  if not ({
    [`mp_m_freemode_01`] = true,
    [`mp_f_freemode_01`] = true,
  })[GetEntityModel(PlayerPedId())] then
    categories = config_skinshops.non_mp_categories
  end

  if interface_visible then
    TriggerEvent('badMenu:client:hideAll')
    SetNuiFocus(true, true)
    SendNUIMessage({
      action = 'show',
      categories = categories,
      customization = tClothingStore.getCustomization(false),
      category_options = tClothingStore.getCategoryOptions(),
      new_character = new_character or false,
    })

    Camera.Activate()
  else
    escapeNUI()
  end
end

exports('SetVisible', tClothingStore.setVisible)

AddEventHandler('blrp_clothingstore:client:setVisibleFromTarget', function()
  tClothingStore.setVisible(true)
end)

tClothingStore.isPedMale = function()
  if GetEntityModel(PlayerPedId()) ~= `mp_m_freemode_01` then
    return false
  end

  return true
end

tClothingStore.rollbackIdleCustom = function()
  Citizen.CreateThread(function()
    Citizen.Wait(1000)

    if last_idle_customization then
      local current_customization = tClothingStore.getCustomization()

      for category_code, category_data in pairs(tClothingStore.getCustomization()) do
        if not string.match(category_code, 'model') then
          local is_prop, category = tClothingStore.parseCategory(category_code)

          local id, texture, palette = table.unpack(category_data)

          if not tClothingStore.checkCanWear(category_code, id, texture) then
            local idle_id, idle_texture, idle_palette = table.unpack(last_idle_customization[category_code])

            local ped = PlayerPedId()

            if is_prop then
              if id == -1 then
                ClearPedProp(ped, category)
              else
                SetPedPropIndex(ped, category, idle_id, idle_texture, 2)
              end
            else
              SetPedComponentVariationInternal(ped, category, idle_id, idle_texture, idle_palette)
            end
          end
        end
      end
    else
      print('No idle customization to roll back to')
    end
  end)
end

tClothingStore.startProximityMonitor = function()
  Citizen.Wait(500)

  local initial_coords = GetEntityCoords(PlayerPedId())

  Citizen.CreateThread(function()
    while true do
      Citizen.Wait(100)

      if #(initial_coords - GetEntityCoords(PlayerPedId())) > 1.0 then
        TriggerEvent('badMenu:client:hideAll')
        return
      end
    end
  end)
end

tClothingStore.changePedModel = function(model_string)
  local model = GetHashKey(model_string)

  if not IsModelValid(model) or not IsModelAPed(model) then
    return false, 'Invalid model'
  end

  local original_health = GetEntityHealth(PlayerPedId())

  local start = GetGameTimer()

  while not HasModelLoaded(model) do
    Citizen.Wait(0)

    RequestModel(model)

    if GetGameTimer() - start > 5000 then
      break
    end
  end

  if not HasModelLoaded(model) then
    return false, 'Failed to load model'
  end

  SetPlayerModel(PlayerId(), model)
  SetEntityHealth(PlayerPedId(), math.floor(original_health))
  SetModelAsNoLongerNeeded(model)

  if model == `mp_m_freemode_01` then
    tClothingStore.setCustomization(config_wardrobes.undress_male)
  elseif model == `mp_f_freemode_01` then
    tClothingStore.setCustomization(config_wardrobes.undress_female)
  else
    SetPedRandomComponentVariation(PlayerPedId(), true)
  end

  exports.blrp_character:InitializeSkin() -- re-apply character traits after new model change

  return true, 'Model changed successfully'
end

tClothingStore.setPlayerModel = function(model)
  local start = GetGameTimer()

  while not HasModelLoaded(model) do
    Citizen.Wait(0)

    RequestModel(model)

    if GetGameTimer() - start > 5000 then
      break
    end
  end

  SetPlayerModel(PlayerId(), model)

  if model == `mp_m_freemode_01` then
    tClothingStore.setCustomization(config_wardrobes.undress_male)
  elseif model == `mp_f_freemode_01` then
    tClothingStore.setCustomization(config_wardrobes.undress_female)
  else
    SetPedRandomComponentVariation(PlayerPedId(), true)
  end
end

tClothingStore.openWardrobe = function(current_version, allow_dress, allow_undress, outfits, force_user_id)
  force_user_id = force_user_id or false

  table.sort(outfits, function(a, b)
    local a_fav = a.favorite or false
    local b_fav = b.favorite or false

    if a_fav ~= b_fav then
      return a_fav and (not b_fav)
    end

    return string.lower(a.name) < string.lower(b.name)
  end)

  local resource_title = 'Wardrobe'

  if force_user_id and tonumber(force_user_id) ~= tonumber(exports.blrp_core:me().get('identifier')) then
    resource_title = resource_title .. ' - ' .. force_user_id
  end

  local menu = BMenu:new(false, {
    resource_title = resource_title,
    section_title = 'Main Menu',
    title_bg_image = '',
    position = { top_offset = 500, left_offset = 10 },
    max_items_shown_at_once = 12,
    prevent_sound = true,
    width = 280,
  })

  menu:addTextInput(false, '> Save Current Outfit', '', function(value, callback)
    if not value or value == '' or string.len(value) > 25 then
      exports.blrp_core:me().notify('Invalid name')
      callback('')
      return
    end

    callback('close')

    local save_hair = exports.blrp_ui:TriggerAcceptPrompt('Save hair with this outfit? (Will always apply your current hairstyle)')

    pClothingStore.saveOutfit({ value, allow_dress, force_user_id, false, save_hair })
  end)

  if allow_undress then
    menu:addSelection(false, '> Undress', function(value, callback)
      callback('')

      pClothingStore.setCustomization({ '> Undress', false, GetEntityModel(PlayerPedId()) })
    end)
  end

  local has_outdated_outfits = false

  for _, outfit_data in pairs(outfits) do
    if outfit_data.version ~= current_version then
      has_outdated_outfits = true
    end
  end

  if has_outdated_outfits then
    local submenu_name = '> Update all outdated outfits'

    menu:addSubMenu(false, submenu_name)

    menu:addSelection(submenu_name, 'Update all outdated outfits to version ' .. current_version .. ' (male)', function(_, callback)
      callback('close')

      pClothingStore.upgradeOutfit({ -1, 'male', allow_dress })
    end)

    menu:addSelection(submenu_name, 'Update all outdated outfits to version ' .. current_version .. ' (female)', function(_, callback)
      callback('close')

      pClothingStore.upgradeOutfit({ -1, 'female', allow_dress })
    end)
  end

  for _, outfit_data in pairs(outfits) do
    local outfit_name = outfit_data.name
    local submenu_name = outfit_name

    if outfit_data.version ~= current_version then
      submenu_name = submenu_name .. ' <span style="color: #FFC300">' .. outfit_data.version .. '</span>'
    end

    if outfit_data.save_hair then
      submenu_name = '🦱 ' .. submenu_name
    end

    if outfit_data.favorite then
      submenu_name = '⭐ ' .. submenu_name
    end

    menu:addSubMenu(false, submenu_name)

    if outfit_data.version == current_version then
      menu:addSelection(submenu_name, '💢 Apply', function(value, callback)
        callback('')

        pClothingStore.setCustomization({ outfit_name, force_user_id, GetEntityModel(PlayerPedId()) })
      end)

      menu:addSelection(submenu_name, '⭐ Toggle Favorite', function(value, callback)
        callback('')

        pClothingStore.toggleFavorite({ outfit_name, allow_dress, force_user_id })
      end)

      menu:addTextInput(submenu_name, '🏷️ Rename', '', function(value, callback)
        if not value or value == '' or string.len(value) > 25 then
          exports.blrp_core:me().notify('Invalid name')
          callback('')
          return
        end

        callback('close')

        pClothingStore.renameOutfit({ outfit_name, value, allow_dress, force_user_id })
      end)

      menu:addSelection(submenu_name, '💾 Replace with current', function(value, callback)
        callback('close')

        pClothingStore.saveOutfit({ outfit_name, allow_dress, force_user_id, true })
      end)

      menu:addSelection(submenu_name, '📤 Share with nearby',function(value, callback)
        callback('close')

        TriggerEvent('vrp:client:selectTargetPlayer', 2, function(selected_ped)
          if selected_ped ~= -1 then
            local target_player = GetPlayerServerId(selected_ped)
            pClothingStore.shareWithNearby({ outfit_name, target_player })
          else
            TriggerEvent('vrp:client:notify', 'No nearby person')
          end
        end)
      end)
    else
      menu:addSelection(submenu_name, '<center style="width:100%">This outfit is out of date</center>')

      menu:addSelection(submenu_name, 'Update to version ' .. current_version .. ' (male)', function(_, callback)
        callback('close')

        pClothingStore.upgradeOutfit({ outfit_name, 'male', allow_dress })
      end)

      menu:addSelection(submenu_name, 'Update to version ' .. current_version .. ' (female)', function(_, callback)
        callback('close')

        pClothingStore.upgradeOutfit({ outfit_name, 'female', allow_dress })
      end)
    end

    menu:addSelection(submenu_name, '🗑️ Delete', function(_, callback)
      callback('close')

      pClothingStore.deleteOutfit({ outfit_name, allow_dress, force_user_id })
    end)
  end

  if #outfits > 0 and (not force_user_id or tonumber(force_user_id) == tonumber(exports.blrp_core:me().get('identifier'))) then
    menu:addSelection(false, '🗑️ Delete All Outfits', function(value, callback)
      callback('')

      pClothingStore.wipeOutfits({ allow_dress })
    end)
  end

  tClothingStore.startProximityMonitor()

  menu:show()
end

RegisterNUICallback('setDual', function(data)
  local ped = PlayerPedId()
  local is_prop, category = tClothingStore.parseCategory(data.category)

  if is_prop then
    local drawable = GetPedPropIndex(ped, category)

    if not tClothingStore.checkCanWear(data.category, data.inputs.drawable, data.inputs.texture) then
      return
    end

    if tonumber(data.inputs.drawable) == -1 then
      ClearPedProp(ped, category)
    else
      SetPedPropIndex(ped, category, data.inputs.drawable, data.inputs.texture, 2)
    end

    should_compare = true
  else
    local drawable = GetPedDrawableVariation(ped, category)

    if not tClothingStore.checkCanWear(data.category, data.inputs.drawable, data.inputs.texture) then
      return
    end

    SetPedComponentVariationInternal(ped, category, data.inputs.drawable, data.inputs.texture, GetPedPaletteVariation(ped, category))
    should_compare = true
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(10000)

    local first_spawn_complete = false

    pcall(function()
      first_spawn_complete = exports.blrp_characterselect:FirstSpawnComplete()
    end)

    if first_spawn_complete then
      local current_customization = tClothingStore.getCustomization()

      local is_emergency_service = exports.blrp_core:me().hasGroup('DOC')

      if not is_emergency_service then
        last_idle_customization = current_customization
      end

      if should_compare then
        should_compare = false

        local should_update = false

        if not last_customization then
          should_update = true
        else
          for component_key, component_data in pairs(current_customization) do
            if
              not string.match(component_key, 'version') and
              not string.match(component_key, 'model') and
              not string.match(component_key, 'save_hair') and
              not string.match(component_key, 'favorite') and
              not string.match(component_key, 'build')
            then
              if
                (last_customization[component_key] and component_data[1] ~= last_customization[component_key][1]) or
                (last_customization[component_key] and component_data[2] ~= last_customization[component_key][2])
              then
                should_update = true
              end
            end
          end
        end

        if should_update and not is_emergency_service then
          TriggerServerEvent('vrp:server:player-state:updateCustomization', current_customization)
        end

        last_customization = current_customization
      end
    end
  end
end)

function escapeNUI()
  interface_visible = false

  Camera.Deactivate()

  SetNuiFocus(false, false)
  SendNUIMessage({
    action = 'hide'
  })
end

RegisterNUICallback('escape', escapeNUI)
AddEventHandler('menu:forceCloseMenu', escapeNUI)

RegisterNUICallback('passthrough', function(data)
  if data.key == 'x' then
    TriggerEvent('vrp:client:police:forceToggleHandsUp')
  end
end)

RegisterNUICallback('removeShirt', function()
  -- Rufus
  if GetEntityModel(PlayerPedId()) == `a_m_y_methhead_01` then
    SetPedComponentVariationInternal(PlayerPedId(), 8, 1, 0, GetPedPaletteVariation(PlayerPedId(), 8))
    return
  end

  SetPedComponentVariationInternal(PlayerPedId(), 8, 15, 0, GetPedPaletteVariation(PlayerPedId(), 8))
end)
