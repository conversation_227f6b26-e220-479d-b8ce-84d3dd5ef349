-- First number is down, second number is up
local hoodie_numbers = {
  -- { build_where_you_got_the_numbers, jbib_hood_down, jbib_hood_up }
  [`mp_m_freemode_01`] = {
    { 3095, 69, 68 },
    { 3095, 205, 202 },
    { 3095, 200, 203 },
    { 3095, 206, 207 },
    { 3095, 210, 211 },
    { 3095, 217, 218 },
    { 3095, 251, 253 },
    { 3095, 262, 263 },
    { 3095, 279, 280 },
    { 3095, 296, 297 },
    { 3095, 301, 302 },
    { 3095, 305, 306 },
    { 3095, 330, 331 },
    { 3095, 352, 353 },
    { 3095, 374, 373 },
    { 3095, 384, 385 },
    { 3095, 400, 401 },
    { 3095, 426, 427 },
    { 3095, 450, 451 },
    { 3095, 480, 481 },
    { 3095, 670, 664 },
    { 3095, 730, 690 },
    { 3095, 737, 736 },
  },

  [`mp_f_freemode_01`] = {
    { 3095, 63, 62 },
    { 3095, 202, 205 },
    { 3095, 207, 204 },
    { 3095, 210, 211 },
    { 3095, 214, 215 },
    { 3095, 227, 228 },
    { 3095, 259, 261 },
    { 3095, 271, 272 },
    { 3095, 292, 293 },
    { 3095, 307, 308 },
    { 3095, 312, 313 },
    { 3095, 316, 317 },
    { 3095, 345, 346 },
    { 3095, 370, 371 },
    { 3095, 392, 393 },
    { 3095, 407, 408 },
    { 3095, 439, 436 },
    { 3095, 456, 457 },
    { 3095, 483, 484 },
    { 3095, 514, 515 },
    { 3095, 778, 752 },
  },
}

tClothingStore.toggleHoodie = function()
  local ped = PlayerPedId()
  local model = GetEntityModel(ped)
  local gender_str = model == `mp_m_freemode_01` and 'male' or 'female'
  local hoodies = hoodie_numbers[model]

  if not hoodies then
    return
  end

  local current_hoodie = { GetPedDrawableVariation(ped, 11), GetPedTextureVariation(ped, 11), GetPedPaletteVariation(ped, 11) }
  local target_id = false

  for _, hoody in pairs(hoodies) do
    for i = 2, 3 do
      local hoody_id = exports.blrp_clothingstore:BackwardsCompatibleOutfit(hoody[1], { [11] = { hoody[i], current_hoodie[2] } }, gender_str)[11][1]

      if current_hoodie[1] == hoody_id then
        target_id = hoody[3]

        if i == 3 then
          target_id = hoody[2]
        end

        target_id = exports.blrp_clothingstore:BackwardsCompatibleOutfit(hoody[1], { [11] = { target_id, current_hoodie[2] } }, gender_str)[11][1]
      end
    end
  end

  if not target_id then
    return
  end

  exports.vrp:PlayAnimation(true, { { "mp_masks@standard_car@ds@", "put_on_mask", 1 } }, false)

  Citizen.Wait(300)

  SetPedComponentVariation(ped, 11, target_id, current_hoodie[2], current_hoodie[3])
end
