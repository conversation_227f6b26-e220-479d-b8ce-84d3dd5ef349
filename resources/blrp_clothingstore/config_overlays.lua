config_overlays = {
  male = {
    [0] = { 'mpbeach_overlays', 'FM_Hair_Fuzz' },
    [1] = { 'multiplayer_overlays', 'NG_M_Hair_001' },
    [2] = { 'multiplayer_overlays', 'NG_M_Hair_002' },
    [3] = { 'multiplayer_overlays', 'NG_M_Hair_003' },
    [4] = { 'multiplayer_overlays', 'NG_M_Hair_004' },
    [5] = { 'multiplayer_overlays', 'NG_M_Hair_005' },
    [6] = { 'multiplayer_overlays', 'NG_M_Hair_006' },
    [7] = { 'multiplayer_overlays', 'NG_M_Hair_007' },
    [8] = { 'multiplayer_overlays', 'NG_M_Hair_008' },
    [9] = { 'multiplayer_overlays', 'NG_M_Hair_009' },
    [10] = { 'multiplayer_overlays', 'NG_M_Hair_013' },
    [11] = { 'multiplayer_overlays', 'NG_M_Hair_002' },
    [12] = { 'multiplayer_overlays', 'NG_M_Hair_011' },
    [13] = { 'multiplayer_overlays', 'NG_M_Hair_012' },
    [14] = { 'multiplayer_overlays', 'NG_M_Hair_014' },
    [15] = { 'multiplayer_overlays', 'NG_M_Hair_015' },
    [16] = { 'multiplayer_overlays', 'NGBea_M_Hair_000' },
    [17] = { 'multiplayer_overlays', 'NGBea_M_Hair_001' },
    [18] = { 'multiplayer_overlays', 'NGBus_M_Hair_000' },
    [19] = { 'multiplayer_overlays', 'NGBus_M_Hair_001' },
    [20] = { 'multiplayer_overlays', 'NGHip_M_Hair_000' },
    [21] = { 'multiplayer_overlays', 'NGHip_M_Hair_001' },
    [22] = { 'multiplayer_overlays', 'NGInd_M_Hair_000' },
    [24] = { 'mplowrider_overlays', 'LR_M_Hair_000' },
    [25] = { 'mplowrider_overlays', 'LR_M_Hair_001' },
    [26] = { 'mplowrider_overlays', 'LR_M_Hair_002' },
    [27] = { 'mplowrider_overlays', 'LR_M_Hair_003' },
    [28] = { 'mplowrider2_overlays', 'LR_M_Hair_004' },
    [29] = { 'mplowrider2_overlays', 'LR_M_Hair_005' },
    [30] = { 'mplowrider2_overlays', 'LR_M_Hair_006' },
    [31] = { 'mpbiker_overlays', 'MP_Biker_Hair_000_M' },
    [32] = { 'mpbiker_overlays', 'MP_Biker_Hair_001_M' },
    [33] = { 'mpbiker_overlays', 'MP_Biker_Hair_002_M' },
    [34] = { 'mpbiker_overlays', 'MP_Biker_Hair_003_M' },
    [35] = { 'mpbiker_overlays', 'MP_Biker_Hair_004_M' },
    [36] = { 'mpbiker_overlays', 'MP_Biker_Hair_005_M' },
    [37] = { 'multiplayer_overlays', 'NG_M_Hair_001' },
    [38] = { 'multiplayer_overlays', 'NG_M_Hair_002' },
    [39] = { 'multiplayer_overlays', 'NG_M_Hair_003' },
    [40] = { 'multiplayer_overlays', 'NG_M_Hair_004' },
    [41] = { 'multiplayer_overlays', 'NG_M_Hair_005' },
    [42] = { 'multiplayer_overlays', 'NG_M_Hair_006' },
    [43] = { 'multiplayer_overlays', 'NG_M_Hair_007' },
    [44] = { 'multiplayer_overlays', 'NG_M_Hair_008' },
    [45] = { 'multiplayer_overlays', 'NG_M_Hair_009' },
    [46] = { 'multiplayer_overlays', 'NG_M_Hair_013' },
    [47] = { 'multiplayer_overlays', 'NG_M_Hair_002' },
    [48] = { 'multiplayer_overlays', 'NG_M_Hair_011' },
    [49] = { 'multiplayer_overlays', 'NG_M_Hair_012' },
    [50] = { 'multiplayer_overlays', 'NG_M_Hair_014' },
    [51] = { 'multiplayer_overlays', 'NG_M_Hair_015' },
    [52] = { 'multiplayer_overlays', 'NGBea_M_Hair_000' },
    [53] = { 'multiplayer_overlays', 'NGBea_M_Hair_001' },
    [54] = { 'multiplayer_overlays', 'NGBus_M_Hair_000' },
    [55] = { 'multiplayer_overlays', 'NGBus_M_Hair_001' },
    [56] = { 'multiplayer_overlays', 'NGHip_M_Hair_000' },
    [57] = { 'multiplayer_overlays', 'NGHip_M_Hair_001' },
    [58] = { 'multiplayer_overlays', 'NGInd_M_Hair_000' },
    [59] = { 'mplowrider_overlays', 'LR_M_Hair_000' },
    [60] = { 'mplowrider_overlays', 'LR_M_Hair_001' },
    [61] = { 'mplowrider_overlays', 'LR_M_Hair_002' },
    [62] = { 'mplowrider_overlays', 'LR_M_Hair_003' },
    [63] = { 'mplowrider2_overlays', 'LR_M_Hair_004' },
    [64] = { 'mplowrider2_overlays', 'LR_M_Hair_005' },
    [65] = { 'mplowrider2_overlays', 'LR_M_Hair_006' },
    [66] = { 'mpbiker_overlays', 'MP_Biker_Hair_000_M' },
    [67] = { 'mpbiker_overlays', 'MP_Biker_Hair_001_M' },
    [68] = { 'mpbiker_overlays', 'MP_Biker_Hair_002_M' },
    [69] = { 'mpbiker_overlays', 'MP_Biker_Hair_003_M' },
    [70] = { 'mpbiker_overlays', 'MP_Biker_Hair_004_M' },
    [71] = { 'mpbiker_overlays', 'MP_Biker_Hair_005_M' },
    [72] = { 'mpgunrunning_overlays', 'MP_Gunrunning_Hair_M_000_M' },
    [73] = { 'mpgunrunning_overlays', 'MP_Gunrunning_Hair_M_001_M' },
    [74] = { 'mpvinewood_overlays', 'MP_Vinewood_Hair_M_000_M' },
    [75] = { 'mptuner_overlays', 'MP_Tuner_Hair_001_M' },
    [76] = { 'mpsecurity_overlays', 'MP_Security_Hair_001_M' },
    [77] = { 'mpsum2_overlays', 'MP_Sum2_Hair_000_M' },
    [78] = { 'mpsum2_overlays', 'MP_Sum2_Hair_002_M' },
    [79] = { 'mp2023_01_overlays', 'MP_2023_01_Hair_000_M' },
    [80] = { 'mp2023_01_overlays', 'MP_2023_01_Hair_002_M' },
    [81] = { 'mp2023_02_overlays', 'MP_2023_02_Hair_000_M' },
  },

  female = {
    [0] = { 'mpbeach_overlays', 'FM_Hair_Fuzz' },
    [1] = { 'multiplayer_overlays', 'NG_F_Hair_001' },
    [2] = { 'multiplayer_overlays', 'NG_F_Hair_002' },
    [3] = { 'multiplayer_overlays', 'NG_F_Hair_003' },
    [4] = { 'multiplayer_overlays', 'NG_F_Hair_004' },
    [5] = { 'multiplayer_overlays', 'NG_F_Hair_005' },
    [6] = { 'multiplayer_overlays', 'NG_F_Hair_006' },
    [7] = { 'multiplayer_overlays', 'NG_F_Hair_007' },
    [8] = { 'multiplayer_overlays', 'NG_F_Hair_008' },
    [9] = { 'multiplayer_overlays', 'NG_F_Hair_009' },
    [10] = { 'multiplayer_overlays', 'NG_F_Hair_010' },
    [11] = { 'multiplayer_overlays', 'NG_F_Hair_011' },
    [12] = { 'multiplayer_overlays', 'NG_F_Hair_012' },
    [13] = { 'multiplayer_overlays', 'NG_F_Hair_013' },
    [14] = { 'multiplayer_overlays', 'NG_M_Hair_014' },
    [15] = { 'multiplayer_overlays', 'NG_M_Hair_015' },
    [16] = { 'multiplayer_overlays', 'NGBea_F_Hair_000' },
    [17] = { 'multiplayer_overlays', 'NGBea_F_Hair_001' },
    [18] = { 'multiplayer_overlays', 'NG_F_Hair_007' },
    [19] = { 'multiplayer_overlays', 'NGBus_F_Hair_000' },
    [20] = { 'multiplayer_overlays', 'NGBus_F_Hair_001' },
    [21] = { 'multiplayer_overlays', 'NGBea_F_Hair_001' },
    [22] = { 'multiplayer_overlays', 'NGHip_F_Hair_000' },
    [23] = { 'multiplayer_overlays', 'NGInd_F_Hair_000' },
    [25] = { 'mplowrider_overlays', 'LR_F_Hair_000' },
    [26] = { 'mplowrider_overlays', 'LR_F_Hair_001' },
    [27] = { 'mplowrider_overlays', 'LR_F_Hair_002' },
    [28] = { 'mplowrider2_overlays', 'LR_F_Hair_003' },
    [29] = { 'mplowrider2_overlays', 'LR_F_Hair_003' },
    [30] = { 'mplowrider2_overlays', 'LR_F_Hair_004' },
    [31] = { 'mplowrider2_overlays', 'LR_F_Hair_006' },
    [32] = { 'mpbiker_overlays', 'MP_Biker_Hair_000_F' },
    [33] = { 'mpbiker_overlays', 'MP_Biker_Hair_001_F' },
    [34] = { 'mpbiker_overlays', 'MP_Biker_Hair_002_F' },
    [35] = { 'mpbiker_overlays', 'MP_Biker_Hair_003_F' },
    [36] = { 'multiplayer_overlays', 'NG_F_Hair_003' },
    [37] = { 'mpbiker_overlays', 'MP_Biker_Hair_006_F' },
    [38] = { 'mpbiker_overlays', 'MP_Biker_Hair_004_F' },
    [39] = { 'multiplayer_overlays', 'NG_F_Hair_001' },
    [40] = { 'multiplayer_overlays', 'NG_F_Hair_002' },
    [41] = { 'multiplayer_overlays', 'NG_F_Hair_003' },
    [42] = { 'multiplayer_overlays', 'NG_F_Hair_004' },
    [43] = { 'multiplayer_overlays', 'NG_F_Hair_005' },
    [44] = { 'multiplayer_overlays', 'NG_F_Hair_006' },
    [45] = { 'multiplayer_overlays', 'NG_F_Hair_007' },
    [46] = { 'multiplayer_overlays', 'NG_F_Hair_008' },
    [47] = { 'multiplayer_overlays', 'NG_F_Hair_009' },
    [48] = { 'multiplayer_overlays', 'NG_F_Hair_010' },
    [49] = { 'multiplayer_overlays', 'NG_F_Hair_011' },
    [50] = { 'multiplayer_overlays', 'NG_F_Hair_012' },
    [51] = { 'multiplayer_overlays', 'NG_F_Hair_013' },
    [52] = { 'multiplayer_overlays', 'NG_M_Hair_014' },
    [53] = { 'multiplayer_overlays', 'NG_M_Hair_015' },
    [54] = { 'multiplayer_overlays', 'NGBea_F_Hair_000' },
    [55] = { 'multiplayer_overlays', 'NGBea_F_Hair_001' },
    [56] = { 'multiplayer_overlays', 'NG_F_Hair_007' },
    [57] = { 'multiplayer_overlays', 'NGBus_F_Hair_000' },
    [58] = { 'multiplayer_overlays', 'NGBus_F_Hair_001' },
    [59] = { 'multiplayer_overlays', 'NGBea_F_Hair_001' },
    [60] = { 'multiplayer_overlays', 'NGHip_F_Hair_000' },
    [61] = { 'multiplayer_overlays', 'NGInd_F_Hair_000' },
    [62] = { 'mplowrider_overlays', 'LR_F_Hair_000' },
    [63] = { 'mplowrider_overlays', 'LR_F_Hair_001' },
    [64] = { 'mplowrider_overlays', 'LR_F_Hair_002' },
    [65] = { 'mplowrider2_overlays', 'LR_F_Hair_003' },
    [66] = { 'mplowrider2_overlays', 'LR_F_Hair_003' },
    [67] = { 'mplowrider2_overlays', 'LR_F_Hair_004' },
    [68] = { 'mplowrider2_overlays', 'LR_F_Hair_006' },
    [69] = { 'mpbiker_overlays', 'MP_Biker_Hair_000_F' },
    [70] = { 'mpbiker_overlays', 'MP_Biker_Hair_001_F' },
    [71] = { 'mpbiker_overlays', 'MP_Biker_Hair_002_F' },
    [72] = { 'mpbiker_overlays', 'MP_Biker_Hair_003_F' },
    [73] = { 'multiplayer_overlays', 'NG_F_Hair_003' },
    [74] = { 'mpbiker_overlays', 'MP_Biker_Hair_006_F' },
    [75] = { 'mpbiker_overlays', 'MP_Biker_Hair_004_F' },
    [76] = { 'mpgunrunning_overlays', 'MP_Gunrunning_Hair_F_000_F' },
    [77] = { 'mpgunrunning_overlays', 'MP_Gunrunning_Hair_F_001_F' },
    [78] = { 'mpvinewood_overlays', 'MP_Vinewood_Hair_F_000_F' },
    [79] = { 'mptuner_overlays', 'MP_Tuner_Hair_000_F' },
    [80] = { 'mpsecurity_overlays', 'MP_Security_Hair_000_F' },
    [81] = { 'mpsum2_overlays', 'MP_Sum2_Hair_001_F' },
    [82] = { 'mpsum2_overlays', 'MP_Sum2_Hair_003_F' },
    [83] = { 'mpchristmas3_overlays', 'MP_Christmas3_Hair_000_F' },
    [84] = { 'mp2023_01_overlays', 'MP_2023_01_Hair_001_F' },
    [85] = { 'mp2023_02_overlays', 'MP_2023_02_Hair_001_F' },
    [234] = { 'mpbeach_overlays', 'FM_Hair_Fuzz' },
  },
}

Citizen.CreateThread(function()
  for gender, overlays in pairs(config_overlays) do
    for id, overlay in pairs(overlays) do
      local collection_hash = GetHashKey(overlay[1])
      local overlay_hash = GetHashKey(overlay[2])

      if collection_hash < 0 then
        collection_hash = exports.blrp_clothingstore:SignedToUnsigned(collection_hash)
      end

      if overlay_hash < 0 then
        overlay_hash = exports.blrp_clothingstore:SignedToUnsigned(overlay_hash)
      end

      config_overlays[gender][id][3] = tonumber(collection_hash)
      config_overlays[gender][id][4] = tonumber(overlay_hash)
    end
  end
end)

function isHairOverlay(collectionHash, overlayHash, male)
  local overlays = config_overlays.male

  if not male then
    overlays = config_overlays.female
  end

  collectionHash = tonumber(collectionHash)
  overlayHash = tonumber(overlayHash)

  for id, overlay in pairs(overlays) do
    if overlay[3] == collectionHash and overlay[4] == overlayHash then
      return true
    end
  end

  return false
end

exports('IsHairOverlay', isHairOverlay)
