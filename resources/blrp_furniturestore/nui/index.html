<html>
  <head>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
  </head>

  <body>
    <div id="container">
      <div id="wrapper" v-if="visible">
        <div>
          <h2 v-if="mode == 'place'">Place Furniture</h2>
          <h2 style="margin-bottom: 20px;" v-else-if="active_category == null">Furniture Store</h2>
          <h2 style="margin-bottom: 20px;" v-else>{{ active_category.name }}</h2>
          <h5 v-if="mode == 'place'">You can also press F to open this menu!</h5>
        </div>

        <div id="header">
          <div class="button" @click="backClicked()"><i class="fa-solid fa-arrow-left fa-fw"></i></div>

          <div class="button" @click="switchModeClicked()" v-if="mode != 'store'"><i class="fa-solid fa-cart-shopping fa-fw"></i></div> <!-- store mode -->
          <div class="button" @click="removeModeClicked()" v-if="mode == 'place'"><i class="fa-solid fa-trash fa-fw"></i></div> <!-- remove mode -->
          <div class="button" @click="switchModeClicked()" v-if="mode != 'place'"><i class="fa-solid fa-couch fa-fw"></i></div> <!-- place mode -->

          <input type="text" placeholder="Search for items..." v-model="search_term" />
        </div>

        <div
          class="options"
          v-if="active_category == null"
        >
          <div
            class="button"
            @click="clickedCategory(category.name)"
            v-for="category in visibleCategories"
          >
            <div>{{ category.name }}</div>
            <div>►</div>
          </div>
        </div>

        <div class="options" v-else>
          <div
            class="button"
            v-for="item in visibleItems"
            @click="clickedItem(item)"
            @mouseover="hoverItem(item)"
            @mouseleave="hoverClear()"
          >
            <div>{{ item[0] }}</div>
            <div style="display: flex;">
              <div
                style="margin-right: 10px;"
                v-if="recently_purchased_model == (item[1] + item[4] ?? '') && Date.now() - recently_purchased_time < 800"
              >
                <i class="fa-solid fa-dollar-sign fa-fw" style="color: greenyellow" v-if="recently_purchased_success"></i>
                <i class="fa-solid fa-dollar-sign fa-fw" style="color: red" v-if="!recently_purchased_success"></i>
              </div>
              <div>{{ mode == 'store' ? '$' : mode == 'place' ? 'x' : '#' }}{{ item[2] }}</div>
            </div>
          </div>
        </div>
      </div>

			<div class="popup" v-if="expensive_item">
        <div class="popup-title">Expensive Item Confirmation</div>
        <div>Are you sure you want to purchase this item for ${{ expensive_item ? Number(expensive_item[2]).toLocaleString() : '' }}?</div>
        <div class="popup-buttons">
          <span class="button" @click="popupAccept()">Yes</span>
          <span class="button" @click="popupDecline()">No</span>
        </div>
			</div>
    </div>

    <script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
    <script src="js/app.js"></script>
  </body>
</html>
