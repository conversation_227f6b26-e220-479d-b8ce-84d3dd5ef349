# you probably don't want to change these!
# only change them if you're using a server with multiple network interfaces
endpoint_add_tcp "0.0.0.0:30130"
endpoint_add_udp "0.0.0.0:30130"

# disable announcing? clear out the master by uncommenting this
sv_master1 ""

# Private Information
exec license.cfg

# Miscelleanous
exec extra.cfg

# DEV hostname
sv_hostname "BadlandsRP | Dev Server"
set blrp_watermark "badlandsrp.com"
# PROD Server 1
#sv_hostname "^0BadlandsRP ^4| ^0Persistent ^4| ^0LSFD/LSPD/DOC/DOJ ^4| ^0Casino ^4| ^0Housing ^4| ^0Legal/Illegal Jobs ^4| ^0Businesses ^4| ^0Trucking ^4| ^0Custom Jobs"
#set blrp_watermark "us1.blrp.life"

sets sv_projectName "BadlandsRP"
sets sv_projectDesc "LSFD LSPD DOC DOJ Casino Housing Furniture Legal Illegal Businesses Trucking Custom Jobs Gangs"

sets locale "en-US"
sets tags "roleplay, rp, police, ems, cars, jobs, persistent, economy, housing, roleplay, drugs, custom, economy, eup, gang, gangs"

<NAME_EMAIL>
sets banner_detail "https://i.gyazo.com/a335ffffa563ed978f7cbb1ed913d472.png"

sets Website "https://badlandsrp.com"
sets Discord "https://discord.gg/badlandsrp"
sets Store "https://store.badlandsrp.com"
sets TikTok "https://www.tiktok.com/@badlandsrp"
sets Linktree "https://linktr.ee/badlandsrp"

# loading a server icon (96x96 PNG file)
load_server_icon hello.png

# server slots limit
sv_maxclients 300

sv_enableWhitelist "off"

set sv_enforceGameBuild 3095
set onesync on
set onesync_population true
sets sv_pureLevel 1
set dev 1

# Don't kick players whose connection to CNL times out
set sv_kick_players_cnl 0

setr voice_debugMode 0
setr voice_useNativeAudio true
setr voice_useSendingRangeOnly true
setr voice_enableRadioAnim 1

setr cl_customCrosshair false
setr profile_skfx 0
setr profile_fpsHeadbob 1

# Ox_lib stuff
setr ox:primaryColor violet
setr ox:primaryShade 8
setr ox:userLocales 1 # Allow users to select their locales using /ox_lib

#Enable below for production only - causes issues in dev environments
#setr voice_externalAddress "*************"

#Prod
#setr voice_externalPort 30120
#Dev
setr voice_externalPort 30130

# Dev environment specific
exec dev.cfg

# Server Resources
exec resources.cfg
