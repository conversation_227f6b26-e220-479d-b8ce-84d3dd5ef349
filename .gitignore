*/node_modules
*/*/node_modules
*.log

cache
/*.log
server/*
server-5104/*
server-5562/*
logs
playerlogs
rpclogs
resources/eup-stream/
resources/eup-stream-bak/
resources/clothing_batch2/
resources/clothing_batch3/
resources/ws_server/node_modules/
resources/blrp_api/ws/node_modules/
resources/blrp_api/node_modules/
resources/gizmo/web/node_modules/

StreetRaces_saveData.txt
imgui.ini

sceneLogging.txt

resources/clothing_batch4/

resources/eup_leo/

resources/eup_snr/

resources/clothing_pack_1/

resources/clothing_pack_2/

resources/clothing_pack_3/

resources/eup_extended/

resources/eup_leo/

resources/clothing_pack1/
resources/clothing_addon/

resources/clothing_addon_*/
/dev.cmd

.idea/
.vscode/

dev.cfg
resources/\[clothing\]
polyzone_created_zones.txt
resources/clothing_addon_1
resources/clothing_addon_2
resources/clothing_addon_3
resources/clothing_addon_1a
resources/clothing_addon_2a
resources/clothing_addon_3a
resources/screenshot-basic/dist/server.js
resources/screenshot-basic/dist/ui.html
resources/screenshot-basic/dist/ui.js
.replxx_history
polyzone_created_zones.txt
/.vs/BadlandsRP/v16
/txData
/.vs
/resources/clothing_restream/
resources/clothing_restream
resources/blrp_api/yarn.lock
/hypnonema.db
hypnonema.db
/resources/vehicleDebug/.gitattributes
/resources/vehicleDebug/cl_config.lua
/resources/vehicleDebug/cl_debugger.lua
/resources/vehicleDebug/fxmanifest.lua
/resources/vehicleDebug/html/index.css
/resources/vehicleDebug/html/index.html
/resources/vehicleDebug/html/index.js
/resources/vehicleDebug/LICENSE
/resources/vehicleDebug/README.md
run2.cmd
/resources/mx-surround/api/build/audios/
/resources/mx-surround/api/build/cache.json
/db/default/LOCK
/db
/db/*
/db/default
db/default/CURRENT
db/default/IDENTITY
db/default/LOG
db/default/MANIFEST-000005
/db
/db/default
db/default/LOG
/resources/\[cars\]/onx-tuning
/resources/\[cars\]/kq_dragy
