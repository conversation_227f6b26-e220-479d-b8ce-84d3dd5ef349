# Rcon Password
rcon_password kiF8keozAojmma77PP5ffQYoPFNymsX

# Steam Check Key
set steam_webApiKey "310C2377815B5BD4238B4DCF07F7DA80"

# hide player endpoints in external log output
sv_endpointprivacy true

# mysql async connection
# DEV mysql CONNECTION
set mysql_connection_string "mysql://wLkH3jg5suMyN6Jq:yruaBCR9xBhjK3ht!@***************:3306/gta5_gamemode_essential_dev?charset=utf8mb4_unicode_ci&decimalNumbers=true"
# PROD mysql CONNECTION
#set mysql_connection_string "host=127.0.0.1;database=gta5_gamemode_essential_prod;userid=z4RDXc7JLGFgMZBD;password=****************"

# Discord Log Webhooks

# in-game-chat-1
set discord_url_string "https://discord.com/api/webhooks/1010223952016187392/Yrdb7qfpQkQ4gcFpvA5ZjTx3_NXxtiNA32WQTlmcs7OSBrRqM6OYSoJ77j0enGd7MEGq"

# character-creation
set discord_url_character_string "https://discord.com/api/webhooks/912108514481164319/aKzjGoWnAO3jOVEjJEuYlY4w4JUYadjd6WuUZVxZtaZVBex8y12AZpqRjH0FS40op6Tu"

# in-game-logs
set discord_logs_string "https://discordapp.com/api/webhooks/686638238441537548/zrqC__cNPr4mVBuAnT3G6ki2cHY9YDvJmTrUalrMUy0S5QeCQI92GWoCFxYyF1mfWjPy"

# staff-support
set discord_url_support_string "https://discord.com/api/webhooks/961972261508767805/LMp4Bk9xo9ZM_8qzuA3dLv734VtwMFd4eFS-ergXs9Qv8QCuHMAmUrIdJjgaXj1D8xo3"

# development
set discord_url_development_string "https://discord.com/api/webhooks/970796942441447424/GmnDfY-3BgKluO3QiyOCdLbXOEn95KV77BLmf0ZM3frYcFd5oTQi6ZVsLU8hWwZqfCtY"

# swat-general
set discord_url_swatcallout_string "https://discord.com/api/webhooks/970796657585320086/SrV4mHsE90sD0aYaGoiwR3nHE-LrZz7iDeiSv--CuQTbuhXHepojRsExGvUFTTPm2jPx"

# housing-log
set discord_url_housinglog_string "https://discord.com/api/webhooks/1006698649822183618/lxrUgLOHfqnwhhP9jHobywwM3gCJF26uFob-KMyGgL28lNJ0o1CHFtCqzvY6_A3pon5b"

# warehouses
set discord_url_warehouselog_string "https://discord.com/api/webhooks/1271659806658134106/weeXkfqaEOT80Z10ccvkx3EX9aY_qNk098YBlcIw6zDNCJwhZMnK1lqqvvI0lY5vFRcX"

# transaction-audit
set discord_url_transactions_string "https://discord.com/api/webhooks/1087141637320359946/SIWeBsffP6FsDD1idhMLCCjmnprviYrU2Xnbmb_BV44D3S8Ceo4niLOqfest7cpZg4F_"

# player-reports
set discord_url_reports_string "https://discord.com/api/webhooks/1119027540460306492/izdCSO0k-wiOtaFt_Qh8yAqAfqpG0mh4KX-axxi9BjB_8e4oYXNODXn7Za_Lkz6-SEVX"

# Medal.tv public key
set medal_public_key "pub_FA7gZxAlIeP7gGnvzskD2iPMsC0cgQG7"

# Discord bot key
set discord_presence_check false
set discord_age_check true
set discord_bot_key "gb7rawBfMqILDJxZVucX"
set discord_bot_address "localhost"

# Server Keys uncomment for correct key
# Local Host/Dev 127.0.0.1
#sv_licenseKey t6sr16jzi92ycv7djyfrpik1qznow9oo
# Prod 1 Key
#sv_licenseKey 42a23o48odpnwfi8u6jbdn5v67hsrmmh
# Prod 2 Key
#sv_licenseKey 3m8un6e48zk57487viz6evpzuspxk0od
# Dev NEW key
sv_licenseKey 3ovmqinupn2goca2z9rzbujtbttu3uky

# Pusher websocket credentials - Development
set pusher_id "*******"
set pusher_key "00752bfae52ec9c01c84"
set pusher_secret "613f8af8afaacc90b05e"
set pusher_cluster "mt1"
